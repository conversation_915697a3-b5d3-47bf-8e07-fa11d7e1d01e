// v2 04.08.2025 20:30 - Modern TypeScript base configuration
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    // Language and Environment
    "lib": ["ES2022"],
    "target": "ES2022", // Node.js 18+ supports ES2022

    // Module System
    "module": "ESNext", // Modern Node.js supports ES modules
    "moduleResolution": "node",

    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,

    // Type Checking - Strict Mode (Progressive)
    "strict": true,
    "exactOptionalPropertyTypes": false, // Can be enabled later for stricter typing
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false, // Can be enabled later, very strict
    "noImplicitOverride": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,

    // Emit - Global no-emit for tsx compatibility
    "noEmit": true,
    "declaration": true,
    "importHelpers": true,
    "newLine": "lf",

    // JavaScript Support
    "allowJs": true,
    "checkJs": false,

    // Editor Support
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo",

    // Completeness
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    // Types - Common across all projects (vitest everywhere except mobile)
    "types": ["node", "vitest/globals"],

    // ESLint Integration - Let ESLint handle these
    "noUnusedLocals": false,
    "noUnusedParameters": false
  },
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
    "**/__tests__/**",
    "**/__mocks__/**"
  ]
}
