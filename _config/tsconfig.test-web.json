{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "target": "ES2022",

    "module": "ESNext",
    "moduleResolution": "node",

    "types": ["node", "vitest/globals", "jest"],
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,

    "noEmit": true,
    "sourceMap": true,

    "strict": false,
    "noImplicitAny": false,
    "useUnknownInCatchVariables": false,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,

    "isolatedModules": true,
    "skipLibCheck": true,

    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noImplicitReturns": false
  },
  "include": [
    "../services/**/__tests__/**/*.ts",
    "../services/**/__tests__/**/*.tsx",
    "../shared/**/__tests__/**/*.ts", 
    "../shared/**/__tests__/**/*.tsx",
    "../**/__mocks__/**/*.ts",
    "../**/__mocks__/**/*.tsx",
    "../**/*.test.ts",
    "../**/*.test.tsx",
    "../**/*.spec.ts",
    "../**/*.spec.tsx"
  ],
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/coverage/**",
    "**/.vite/**",
    "**/.next/**",
    "**/public/**",
    // Exclude mobile-specific files
    "**/*.(test|spec).(native|rn|mobile).{ts,tsx}",
    "**/react-native/**",
    "**/mobile/**",
    "**/android/**",
    "**/ios/**"
  ]
}