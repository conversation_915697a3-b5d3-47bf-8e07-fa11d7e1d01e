// v2 04.08.2025 20:30 - Modern browser-specific TypeScript configuration
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    // Browser Environment
    "target": "ES2020", // Modern browsers support ES2020+
    "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"],

    // Module System - Browser specific
    "module": "ESNext",
    "moduleResolution": "NodeNext",

    // React/JSX Support
    "jsx": "react-jsx",
    "jsxImportSource": "react",

    // Browser-specific optimizations
    "noEmit": true, // Browsers don't emit, bundlers handle this
    "verbatimModuleSyntax": false, // Allow flexible import/export for bundlers

    // Modern browser features
    "useDefineForClassFields": true,
    "allowImportingTsExtensions": false,

    // Types for browser environment
    "types": ["node", "react", "react-dom", "vitest/globals"],

    // Performance optimizations for large browser apps
    "assumeChangesOnlyAffectDirectDependencies": true
  },
  "include": ["src/**/*", "public/**/*", "**/*.tsx", "**/*.ts"],
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/*.test.*",
    "**/*.spec.*",
    "**/coverage/**",
    "**/*.config.js",
    "**/*.config.ts"
  ]
}
