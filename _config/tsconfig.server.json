// v2 04.08.2025 20:30 - Modern server-side TypeScript configuration
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "lib": ["ES2022"], // Server doesn't need DOM

    // Server-specific settings
    "allowJs": true,
    "checkJs": false,

    // Output settings for server builds
    "outDir": "./dist",
    "rootDir": "./src",
    "removeComments": false,
    "sourceMap": true,

    // Node.js optimizations
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "verbatimModuleSyntax": false,

    // Server doesn't need JSX typically, but preserve for flexibility
    "jsx": "preserve",

    // Types for Node.js environment - default empty, projects should override
    "types": ["node"],

    // Performance for server builds
    "incremental": true,
    "composite": false, // Will be set per workspace if needed

    // Node.js specific features
    "allowImportingTsExtensions": false,
    "resolvePackageJsonExports": false,
    "resolvePackageJsonImports": false
  },
  "include": ["src/**/*", "scripts/**/*", "**/*.ts", "**/*.js"],
  "exclude": [
    "**/node_modules/**",
    "**/dist/**",
    "**/build/**",
    "**/*.test.*",
    "**/*.spec.*",
    "**/coverage/**",
    "**/*.config.js",
    "**/*.config.ts",
    "**/public/**"
  ]
}
