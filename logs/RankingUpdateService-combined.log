{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.618Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.624Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:44:21.635Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.640Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.642Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.644Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T21:44:21.645Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.389Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.394Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:54:17.402Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.406Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.407Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.409Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T21:54:17.410Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.808Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.817Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:54:54.834Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.846Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.847Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.864Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T21:54:54.867Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.798Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.804Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:56:19.817Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.826Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.827Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.828Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T21:56:19.829Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.476Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.483Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:08:24.506Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.511Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.514Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.516Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T22:08:24.517Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.278Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.291Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:15:07.302Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.306Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.307Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.308Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T22:15:07.309Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.894Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.922Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:17:09.928Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.932Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.933Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.951Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T22:17:09.955Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.808Z"}
{"level":"warn","message":"Invalid DomainDescription for x: DomainDescription validation failed","service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.821Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:13:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:28:14.889Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.892Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":42,"service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.892Z"}
{"level":"info","message":"Processing single ranking update for domain: x","service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.894Z"}
{"level":"info","message":"Single ranking update completed for domain: x","overallScore":1,"service":"RankingUpdateService","timestamp":"2025-08-04T22:28:14.894Z"}
