{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:44:21.635Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:54:17.402Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:54:54.834Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:12:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T21:56:19.817Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:08:24.506Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:15:07.302Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:14:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:17:09.928Z"}
{"level":"error","message":"Single ranking update failed for domain: x: DomainDescription validation failed","service":"RankingUpdateService","stack":"Error: DomainDescription validation failed\n    at Object.assert (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/__tests__/validation.test.ts:13:61)\n    at RankingUpdateService.handleSingleRankingUpdate (/Users/<USER>/vprojects/domainr/services/ranking-engine/src/services/RankingUpdateService.ts:273:16)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-04T22:28:14.889Z"}
