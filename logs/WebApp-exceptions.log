{"date":"Tue Aug 05 2025 01:08:25 GMT+0300 (GMT+03:00)","error":{},"exception":true,"level":"error","message":"uncaughtException: Class constructor WebAppService cannot be invoked without 'new'\nTypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","os":{"loadavg":[19.87060546875,33.52587890625,30.025390625],"uptime":166383},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/vprojects/domainr/node_modules/.pnpm/tinypool@1.1.1/node_modules/tinypool/dist/entry/process.js"],"cwd":"/Users/<USER>/vprojects/domainr","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":200940,"external":3922122,"heapTotal":103399424,"heapUsed":72693176,"rss":184483840},"pid":53590,"uid":501,"version":"v22.17.0"},"service":"WebApp","stack":"TypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","timestamp":"2025-08-04T22:08:25.276Z","trace":[{"column":28,"file":"node:events","function":"Server.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:_http_server","function":"parserOnIncoming","line":1155,"method":null,"native":false},{"column":17,"file":"node:_http_common","function":"HTTPParser.parserOnHeadersComplete","line":117,"method":"parserOnHeadersComplete","native":false}]}
{"date":"Tue Aug 05 2025 01:16:58 GMT+0300 (GMT+03:00)","error":{},"exception":true,"level":"error","message":"uncaughtException: Class constructor WebAppService cannot be invoked without 'new'\nTypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","os":{"loadavg":[9.39111328125,14.66357421875,20.90673828125],"uptime":166896},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/vprojects/domainr/node_modules/.pnpm/tinypool@1.1.1/node_modules/tinypool/dist/entry/process.js"],"cwd":"/Users/<USER>/vprojects/domainr","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":268956,"external":3990138,"heapTotal":103661568,"heapUsed":73281872,"rss":188727296},"pid":62255,"uid":501,"version":"v22.17.0"},"service":"WebApp","stack":"TypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","timestamp":"2025-08-04T22:16:58.490Z","trace":[{"column":28,"file":"node:events","function":"Server.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:_http_server","function":"parserOnIncoming","line":1155,"method":null,"native":false},{"column":17,"file":"node:_http_common","function":"HTTPParser.parserOnHeadersComplete","line":117,"method":"parserOnHeadersComplete","native":false}]}
{"date":"Tue Aug 05 2025 01:17:10 GMT+0300 (GMT+03:00)","error":{},"exception":true,"level":"error","message":"uncaughtException: Class constructor WebAppService cannot be invoked without 'new'\nTypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","os":{"loadavg":[27.017578125,18.23876953125,22.06884765625],"uptime":166908},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/vprojects/domainr/node_modules/.pnpm/tinypool@1.1.1/node_modules/tinypool/dist/entry/process.js"],"cwd":"/Users/<USER>/vprojects/domainr","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":215258,"external":3936440,"heapTotal":102875136,"heapUsed":72884600,"rss":188104704},"pid":62525,"uid":501,"version":"v22.17.0"},"service":"WebApp","stack":"TypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","timestamp":"2025-08-04T22:17:10.719Z","trace":[{"column":28,"file":"node:events","function":"Server.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:_http_server","function":"parserOnIncoming","line":1155,"method":null,"native":false},{"column":17,"file":"node:_http_common","function":"HTTPParser.parserOnHeadersComplete","line":117,"method":"parserOnHeadersComplete","native":false}]}
{"date":"Tue Aug 05 2025 01:18:28 GMT+0300 (GMT+03:00)","error":{},"exception":true,"level":"error","message":"uncaughtException: Class constructor WebAppService cannot be invoked without 'new'\nTypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","os":{"loadavg":[11.69921875,15.5791015625,20.74365234375],"uptime":166986},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","/Users/<USER>/vprojects/domainr/node_modules/.pnpm/tinypool@1.1.1/node_modules/tinypool/dist/entry/process.js"],"cwd":"/Users/<USER>/vprojects/domainr","execPath":"/Users/<USER>/.nvm/versions/node/v22.17.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":148503,"external":3869480,"heapTotal":103137280,"heapUsed":72876072,"rss":186548224},"pid":63470,"uid":501,"version":"v22.17.0"},"service":"WebApp","stack":"TypeError: Class constructor WebAppService cannot be invoked without 'new'\n    at Server.emit (node:events:518:28)\n    at parserOnIncoming (node:_http_server:1155:12)\n    at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17)","timestamp":"2025-08-04T22:18:28.025Z","trace":[{"column":28,"file":"node:events","function":"Server.emit","line":518,"method":"emit","native":false},{"column":12,"file":"node:_http_server","function":"parserOnIncoming","line":1155,"method":null,"native":false},{"column":17,"file":"node:_http_common","function":"HTTPParser.parserOnHeadersComplete","line":117,"method":"parserOnHeadersComplete","native":false}]}
