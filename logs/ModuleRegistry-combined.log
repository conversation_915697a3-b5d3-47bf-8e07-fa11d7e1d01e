{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.102Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.108Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.115Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.119Z","totalTime":3}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.126Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.126Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.128Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.129Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.130Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.131Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.132Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.132Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.133Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.133Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.137Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.137Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.139Z"}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.139Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.140Z","totalTime":1}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.140Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: demo.org","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.142Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: demo.org","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.142Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.145Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.145Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.147Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.147Z","totalTime":0}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.148Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.148Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.150Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.150Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.152Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.153Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.154Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.154Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.155Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.155Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.158Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:01.159Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.161Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.161Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.164Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.164Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.168Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.169Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.169Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.170Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.170Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.170Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.171Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.171Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.172Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:01.172Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.098Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.103Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.108Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:15:02.120Z","totalTime":13}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.184Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.184Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.186Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:02.186Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.188Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.188Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.188Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:15:02.189Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.189Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.189Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.190Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:02.190Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.191Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.191Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.191Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:02.192Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.211Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.212Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.213Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:15:02.214Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.219Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.219Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.226Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:15:02.226Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.229Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.229Z"}
{"level":"info","message":"Executing 2 modules for domain: nonexistent.invalid","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.230Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: nonexistent.invalid","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:02.230Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.231Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.231Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.231Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:15:02.232Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.262Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.263Z"}
{"level":"info","message":"Registered module: failing","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.264Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:15:02.264Z"}
{"completeness":50,"failures":1,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:15:02.768Z","totalTime":504}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.368Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.375Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.381Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.384Z","totalTime":3}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.390Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.391Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.391Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.392Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.392Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.392Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.393Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.395Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.396Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.396Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.401Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.401Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.404Z"}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.404Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.405Z","totalTime":1}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.405Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: demo.org","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.406Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: demo.org","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.406Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.408Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.410Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.413Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.414Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.416Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.416Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.418Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.418Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.420Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.421Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.422Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.422Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.423Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.423Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.424Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.424Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.425Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.426Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.428Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.428Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.431Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.431Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.432Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.432Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.433Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.433Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.434Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.434Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.435Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.435Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.848Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.852Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.856Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:17:05.857Z","totalTime":2}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.860Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.861Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.862Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.862Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.863Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.863Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.864Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:17:05.864Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.865Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.865Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.865Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.865Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.866Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.866Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.870Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.883Z","totalTime":13}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.888Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.889Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.892Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:17:05.896Z","totalTime":4}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.899Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.900Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.902Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:17:05.904Z","totalTime":3}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.906Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.906Z"}
{"level":"info","message":"Executing 2 modules for domain: nonexistent.invalid","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.907Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: nonexistent.invalid","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.907Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.908Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.908Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.908Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:17:05.909Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.910Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.910Z"}
{"level":"info","message":"Registered module: failing","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.919Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:17:05.920Z"}
{"completeness":50,"failures":1,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:17:06.422Z","totalTime":502}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.600Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.605Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.611Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.614Z","totalTime":3}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.650Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.650Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.653Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.654Z","totalTime":3}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.655Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.655Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.656Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.656Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.657Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.657Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.659Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.660Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.661Z"}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.662Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.662Z","totalTime":1}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.662Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: demo.org","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.663Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: demo.org","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.664Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.665Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.665Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.666Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.678Z","totalTime":7}
{"level":"info","message":"Executing 2 modules for domain: test.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.703Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: test.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.703Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.728Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.730Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.749Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.750Z","totalTime":1}
{"level":"info","message":"Executing 2 modules for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.751Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: invalid.domain.that.should.fail","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.751Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.755Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.755Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.756Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:09.756Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.757Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.757Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.758Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.758Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.766Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.766Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.767Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.767Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.768Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.768Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.768Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.768Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.769Z"}
{"level":"info","message":"Registered module: robots","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:09.769Z"}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.505Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.509Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.511Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:28:10.513Z","totalTime":2}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.516Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.517Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.518Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:10.518Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.520Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.520Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.521Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:28:10.521Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.521Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.521Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.522Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:10.522Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.523Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.523Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.524Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:10.524Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.525Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.525Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.525Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:28:10.525Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.526Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.526Z"}
{"level":"info","message":"Executing 1 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.527Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:28:10.527Z","totalTime":1}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.528Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.528Z"}
{"level":"info","message":"Executing 2 modules for domain: nonexistent.invalid","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.528Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: nonexistent.invalid","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:10.528Z","totalTime":0}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.529Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.529Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.530Z"}
{"completeness":100,"failures":0,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":2,"timestamp":"2025-08-04T22:28:10.553Z","totalTime":23}
{"level":"info","message":"Registered module: dns","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.555Z"}
{"level":"info","message":"Registered module: ssl","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.555Z"}
{"level":"info","message":"Registered module: failing","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.555Z"}
{"level":"info","message":"Executing 2 modules for domain: example.com","service":"ModuleRegistry","timestamp":"2025-08-04T22:28:10.556Z"}
{"completeness":50,"failures":1,"level":"info","message":"Module execution completed for domain: example.com","service":"ModuleRegistry","success":1,"timestamp":"2025-08-04T22:28:11.058Z","totalTime":502}
