---
inclusion: always
---

# Tech Rules v1

## Technologies

- Node.js v20+ with Yarn - Primary runtime environment
- PHP v8.4+ - Secondary runtime environment
- MariaDB v11.2+ - Primary database
- Redis v7.2+
- ScyllaDB v6.2+
- Manticore Search DB v6.2+ - Full-text search and faceted filtering
- weserv image proxy 5.x+ - Image optimization, WebP conversion and image proxying
- Justfile - Command runner for development workflows (`.justfile`)
- docker
- Browserless - Headless browser for screenshots and performance audits

## Libraries

- PhalconPHP v5.6+
- uWebsockets.js v20.52+
- React.js
- ESLint #[[file:.eslint.rules.js]]
- Vitest for test runner
- @testing-library for text executor
