# Design Document

## Overview

The Domain Ranking System is a comprehensive web analysis platform similar to Alexa Rankings, Cloudflare Radar, and URLScan.io that evaluates and ranks domains based on technical performance, security, content quality, and web presence metrics. The system consists of four main services: a web application for domain analysis and rankings, a crawler service for comprehensive website data collection, a ranking engine for multi-dimensional scoring, and a unified data layer spanning multiple database technologies. The architecture emphasizes resource efficiency, modularity, and open-source solutions while maintaining high performance and scalability to eventually analyze "all websites" on the internet.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
    end
    
    subgraph "Application Layer"
        APP[Web Application<br/>ultimate-express + React SSR]
        CRAWLER[Crawler Service<br/>Node.js + Browserless]
        RANKING[Ranking Engine<br/>Node.js]
        SCHEDULER[Job Scheduler<br/>Redis-SMQ]
    end
    
    subgraph "Data Layer"
        SCYLLA[(ScyllaDB<br/>Main Data)]
        MARIA[(MariaDB<br/>Relational Data)]
        MANTICORE[(Manticore Search<br/>Full-text Search)]
        REDIS[(Redis<br/>Cache + Queue)]
    end
    
    subgraph "External Services"
        BROWSERLESS[Browserless Instance<br/>VDS]
        IMAGEPROXY[images.weserv.nl<br/>WebP Conversion]
    end
    
    WEB --> APP
    APP --> SCYLLA
    APP --> MARIA
    APP --> MANTICORE
    APP --> REDIS
    
    SCHEDULER --> CRAWLER
    SCHEDULER --> RANKING
    
    CRAWLER --> SCYLLA
    CRAWLER --> MARIA
    CRAWLER --> BROWSERLESS
    CRAWLER --> IMAGEPROXY
    
    RANKING --> SCYLLA
    RANKING --> MARIA
    RANKING --> REDIS
```

### Service Communication

- **Synchronous**: HTTP/REST APIs between web application and data services
- **Asynchronous**: Redis-SMQ for job scheduling and inter-service communication
- **Data Consistency**: Eventual consistency model with Redis caching for performance

## Components and Interfaces

### 1. Web Application Service

**Technology Stack:**
- **Framework**: ultimate-express (Express.js compatible)
- **Frontend**: React with Server-Side Rendering
- **Template Engine**: EJS for server-side templates
- **Styling**: CSS Modules or Styled Components (no Tailwind)

**Key Interfaces:**
```javascript
// Domain Search API
GET /api/domains/search?query={domain}&category={category}&sort={rank|speed|security}
Response: {
  domains: [DomainRanking],
  pagination: PaginationInfo,
  filters: FilterOptions,
  totalResults: number
}

// Domain Analysis API
GET /api/domains/{domain}/analysis
Response: {
  domain: string,
  globalRank: number,
  categoryRank: number,
  metrics: {
    performance: PerformanceMetrics,
    security: SecurityMetrics,
    seo: SEOMetrics,
    technical: TechnicalMetrics,
    traffic: TrafficEstimate
  },
  lastUpdated: timestamp
}

// Domain Comparison API
GET /api/domains/compare?domains={domain1,domain2,domain3}
Response: {
  comparison: DomainComparison[],
  metrics: MetricComparison,
  recommendations: string[]
}

// Top Domains API
GET /api/domains/top?category={category}&country={country}&limit={limit}
Response: {
  topDomains: TopDomainList[],
  category: string,
  timeframe: string
}
```

### 2. Crawler Service

**Responsibilities (Prioritized by Resource Usage):**

**Phase 1 - Low Resource, High Priority:**
- robots.txt analysis and compliance checking
- DNS records collection (A, AAAA, MX, CNAME, TXT, NS)
- Basic domain information (age, registrar via WHOIS API)
- SSL certificate basic info (issuer, expiration, grade)
- Server response headers and basic info
- Homepage meta tags (title, description) extraction
- Simple technology detection from headers/HTML

**Phase 2 - Moderate Resource:**
- Basic performance metrics (response time, page size)
- Security headers analysis (HSTS, CSP, X-Frame-Options)
- Language detection and basic content analysis
- Social media links extraction
- Sitemap.xml detection and basic analysis

**Phase 3 - High Resource (Future Implementation):**
- Full Core Web Vitals analysis
- Multi-page content crawling
- Screenshot capture and visual analysis
- Comprehensive accessibility scoring
- Advanced backlink discovery

**Key Interfaces:**
```javascript
// Domain Crawl Job Interface
interface DomainCrawlJob {
  domain: string;
  crawlType: 'full' | 'quick' | 'security' | 'performance';
  priority: 'high' | 'medium' | 'low';
  retryCount: number;
  scheduledAt: Date;
  userAgent: string;
}

// Domain Analysis Result
interface DomainAnalysis {
  domain: string;
  performance: {
    loadTime: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    cumulativeLayoutShift: number;
    firstInputDelay: number;
    speedIndex: number;
  };
  security: {
    sslGrade: string;
    securityHeaders: SecurityHeaders;
    vulnerabilities: Vulnerability[];
    certificateInfo: CertificateInfo;
  };
  seo: {
    metaTags: MetaTags;
    structuredData: StructuredData[];
    sitemap: SitemapInfo;
    robotsTxt: RobotsInfo;
  };
  technical: {
    technologies: Technology[];
    serverInfo: ServerInfo;
    httpHeaders: HttpHeaders;
    pageSize: number;
    resourceCount: ResourceCount;
  };
  screenshots: ScreenshotData[];
  subdomains: string[];
}
```

### 3. Ranking Engine Service

**Responsibilities:**
- Domain composite score calculation
- Multi-dimensional ranking algorithms
- Global and category-specific rankings
- Traffic estimation and trend analysis
- Backlink authority scoring

**Core Algorithm Components:**
```javascript
// Domain Ranking Algorithm Interface
interface DomainRankingAlgorithm {
  calculateGlobalRank(domain: DomainData): number;
  calculateCategoryRank(domain: DomainData, category: string): number;
  calculatePerformanceScore(metrics: PerformanceMetrics): number;
  calculateSecurityScore(security: SecurityMetrics): number;
  calculateSEOScore(seo: SEOMetrics): number;
  calculateTechnicalScore(technical: TechnicalMetrics): number;
  estimateTraffic(domain: DomainData): TrafficEstimate;
}

// Domain Scoring Weights (configurable)
const DOMAIN_RANKING_WEIGHTS = {
  performance: 0.25,    // 25% - Speed, Core Web Vitals
  security: 0.20,       // 20% - SSL, headers, vulnerabilities
  seo: 0.20,           // 20% - Meta tags, structure, content
  technical: 0.15,      // 15% - Technologies, server quality
  backlinks: 0.20      // 20% - Link authority and quality
};
```

### 4. Job Scheduler Service

**Technology**: Redis with redis-smq
**Responsibilities:**
- Crawl job scheduling and management
- Ranking recalculation triggers
- System maintenance tasks
- Failed job retry logic

## Data Models

### ScyllaDB Schema (Main Data Storage)

```cql
-- Domain Rankings (Global and category-based rankings)
CREATE TABLE domain_rankings (
    ranking_type text,            -- 'global', 'category:tech', 'country:us', etc.
    rank int,
    domain text,
    overall_score decimal,
    performance_score decimal,
    security_score decimal,
    seo_score decimal,
    technical_score decimal,
    backlink_score decimal,
    traffic_estimate bigint,
    last_updated timestamp,
    PRIMARY KEY (ranking_type, rank, domain)
) WITH CLUSTERING ORDER BY (rank ASC);

-- Comprehensive Domain Analysis Data
CREATE TABLE domain_analysis (
    domain text PRIMARY KEY,
    global_rank int,
    category text,
    category_rank int,
    
    -- Performance Metrics
    performance_metrics map<text, decimal>,  -- load_time, fcp, lcp, cls, fid, speed_index
    
    -- Security Metrics
    security_metrics map<text, text>,        -- ssl_grade, security_headers, vulnerabilities
    ssl_certificate map<text, text>,         -- issuer, expiration, chain_info
    
    -- SEO Metrics
    seo_metrics map<text, text>,            -- meta_tags, structured_data, sitemap_status
    
    -- Technical Metrics
    technical_metrics map<text, text>,       -- page_size, resource_count, compression
    technologies set<text>,                  -- cms, frameworks, analytics, cdn
    server_info map<text, text>,            -- ip, location, hosting_provider, cdn_provider
    
    -- Domain Information
    domain_age_days int,
    registration_date date,
    expiration_date date,
    registrar text,
    
    -- DNS Records
    dns_records map<text, list<text>>,      -- a, aaaa, mx, cname, txt, ns records
    
    -- Content Analysis
    content_metrics map<text, decimal>,      -- readability, content_length, media_count
    language_detected text,
    mobile_friendly_score decimal,
    accessibility_score decimal,
    
    -- Social Presence
    social_links map<text, text>,           -- facebook, twitter, linkedin, instagram
    
    -- Visual Data
    screenshot_urls list<text>,
    subdomains set<text>,
    
    -- Crawl Metadata
    last_crawled timestamp,
    crawl_status text,
    error_count int,
    crawl_duration_ms int
);

-- Domain Content Storage (for full-text search indexing)
CREATE TABLE domain_content (
    domain text,
    page_type text,                         -- 'homepage', 'about', 'contact', 'privacy'
    content_hash text,
    title text,
    description text,
    content_text text,                      -- Full page text content
    meta_keywords set<text>,
    headings list<text>,                    -- h1, h2, h3 content
    last_updated timestamp,
    PRIMARY KEY (domain, page_type)
);

-- Domain Reviews and Ratings
CREATE TABLE domain_reviews (
    domain text,
    review_id uuid,
    source text,                            -- 'trustpilot', 'sitejabber', 'google', 'manual'
    rating decimal,
    review_text text,
    review_date date,
    sentiment_score decimal,
    verified boolean,
    PRIMARY KEY (domain, review_date, review_id)
) WITH CLUSTERING ORDER BY (review_date DESC);

-- Domain Crawl Jobs
CREATE TABLE domain_crawl_jobs (
    job_id uuid PRIMARY KEY,
    domain text,
    crawl_type text,                        -- 'full', 'quick', 'security', 'performance', 'content'
    priority text,
    status text,
    retry_count int,
    scheduled_at timestamp,
    started_at timestamp,
    completed_at timestamp,
    error_message text,
    user_agent text,
    pages_to_crawl list<text>               -- specific pages to analyze
);

-- Domain Traffic History
CREATE TABLE domain_traffic_history (
    domain text,
    date date,
    estimated_visits bigint,
    bounce_rate decimal,
    avg_session_duration int,
    page_views_per_session decimal,
    traffic_sources map<text, decimal>,     -- organic, direct, referral, social percentages
    top_keywords list<text>,
    PRIMARY KEY (domain, date)
) WITH CLUSTERING ORDER BY (date DESC);

-- Domain Ranking History (for trend analysis)
CREATE TABLE domain_ranking_history (
    domain text,
    date date,
    ranking_type text,
    rank int,
    score decimal,
    PRIMARY KEY (domain, ranking_type, date)
) WITH CLUSTERING ORDER BY (date DESC);
```

### MariaDB Schema (Relational Data)

```sql
-- Domain Categories and Classifications
CREATE TABLE domain_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_category_id) REFERENCES domain_categories(id),
    INDEX idx_parent (parent_category_id)
);

-- Domain to Category Mapping
CREATE TABLE domain_category_mapping (
    domain VARCHAR(255),
    category_id INT,
    confidence_score DECIMAL(3,2),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (domain, category_id),
    FOREIGN KEY (category_id) REFERENCES domain_categories(id),
    INDEX idx_domain (domain),
    INDEX idx_category (category_id)
);

-- Backlink Analysis
CREATE TABLE backlinks (
    id CHAR(36) PRIMARY KEY,
    source_domain VARCHAR(255),
    target_domain VARCHAR(255),
    link_quality_score DECIMAL(3,2),
    anchor_text VARCHAR(500),
    link_type ENUM('follow', 'nofollow', 'sponsored', 'ugc'),
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_target_domain (target_domain),
    INDEX idx_source_domain (source_domain),
    INDEX idx_quality_score (link_quality_score),
    INDEX idx_discovered (discovered_at)
);

-- Domain Whois Information
CREATE TABLE domain_whois (
    domain VARCHAR(255) PRIMARY KEY,
    registrar VARCHAR(255),
    registration_date DATE,
    expiration_date DATE,
    name_servers TEXT,
    registrant_country VARCHAR(2),
    registrant_organization VARCHAR(255),
    privacy_protected BOOLEAN DEFAULT FALSE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_registrar (registrar),
    INDEX idx_registration_date (registration_date),
    INDEX idx_country (registrant_country)
);
```

### Manticore Search Schema (Full-text Search & Faceted Search)

```sql
-- Main domain search index
CREATE TABLE domains_index (
    id bigint,
    domain text indexed,
    title text indexed,
    description text indexed,
    content text indexed,
    category string attribute,
    country string attribute,
    language string attribute,
    technologies multi attribute,
    registrar string attribute,
    domain_age_days int attribute,
    global_rank int attribute,
    overall_score float attribute,
    performance_score float attribute,
    security_score float attribute,
    seo_score float attribute,
    technical_score float attribute,
    backlink_score float attribute,
    traffic_estimate bigint attribute,
    ssl_grade string attribute,
    mobile_friendly_score float attribute,
    accessibility_score float attribute,
    last_updated timestamp attribute
) engine='columnar';

-- Domain content search index (for full-text search across all pages)
CREATE TABLE domain_content_index (
    id bigint,
    domain text indexed,
    page_type string attribute,
    title text indexed,
    content text indexed,
    headings text indexed,
    meta_keywords multi attribute,
    language string attribute,
    last_updated timestamp attribute
) engine='columnar';

-- Domain reviews search index
CREATE TABLE domain_reviews_index (
    id bigint,
    domain text indexed,
    review_text text indexed,
    source string attribute,
    rating float attribute,
    sentiment_score float attribute,
    review_date timestamp attribute,
    verified int attribute
) engine='columnar';
```

### Manticore Search Integration

```javascript
// Manticore Search Service
class ManticoreSearchService {
  constructor() {
    this.client = new ManticoreClient({
      host: process.env.MANTICORE_HOST || 'manticore',
      port: process.env.MANTICORE_PORT || 9308
    });
  }

  // Domain search with faceted filtering
  async searchDomains(query, filters = {}) {
    const searchQuery = {
      index: 'domains_index',
      query: {
        match: { 
          domain: query,
          title: query,
          description: query,
          content: query
        }
      },
      filter: this.buildFilters(filters),
      sort: this.buildSort(filters.sort),
      limit: filters.limit || 20,
      offset: filters.offset || 0,
      facet: {
        category: {},
        country: {},
        technologies: {},
        ssl_grade: {}
      }
    };
    
    return await this.client.search(searchQuery);
  }

  // Get top domains by category with search capability
  async getTopDomains(category, filters = {}) {
    const searchQuery = {
      index: 'domains_index',
      query: { match_all: {} },
      filter: {
        category: category,
        ...this.buildFilters(filters)
      },
      sort: [{ global_rank: { order: 'asc' } }],
      limit: filters.limit || 100
    };
    
    return await this.client.search(searchQuery);
  }

  // Advanced domain comparison search
  async compareDomains(domains) {
    const searchQuery = {
      index: 'domains_index',
      query: {
        terms: { domain: domains }
      },
      sort: [{ overall_score: { order: 'desc' } }]
    };
    
    return await this.client.search(searchQuery);
  }

  buildFilters(filters) {
    const manticoreFilters = {};
    
    if (filters.category) manticoreFilters.category = filters.category;
    if (filters.country) manticoreFilters.country = filters.country;
    if (filters.minRank) manticoreFilters.global_rank = { gte: filters.minRank };
    if (filters.maxRank) manticoreFilters.global_rank = { ...manticoreFilters.global_rank, lte: filters.maxRank };
    if (filters.minScore) manticoreFilters.overall_score = { gte: filters.minScore };
    if (filters.technologies) manticoreFilters.technologies = { in: filters.technologies };
    if (filters.sslGrade) manticoreFilters.ssl_grade = filters.sslGrade;
    
    return manticoreFilters;
  }
}
```

### Data Synchronization Strategy

```javascript
// Sync service to keep Manticore in sync with ScyllaDB
class DataSyncService {
  async syncDomainToManticore(domain, domainData) {
    const manticoreDoc = {
      id: this.generateId(domain),
      domain: domain,
      title: domainData.seo_metrics.title || '',
      description: domainData.seo_metrics.description || '',
      content: await this.getDomainContent(domain),
      category: domainData.category,
      country: domainData.server_info.country,
      language: domainData.language_detected,
      technologies: Array.from(domainData.technologies),
      registrar: domainData.registrar,
      domain_age_days: domainData.domain_age_days,
      global_rank: domainData.global_rank,
      overall_score: this.calculateOverallScore(domainData),
      performance_score: this.extractScore(domainData.performance_metrics),
      security_score: this.extractScore(domainData.security_metrics),
      seo_score: this.extractScore(domainData.seo_metrics),
      technical_score: this.extractScore(domainData.technical_metrics),
      backlink_score: domainData.backlink_score || 0,
      traffic_estimate: domainData.traffic_estimate || 0,
      ssl_grade: domainData.ssl_certificate.grade,
      mobile_friendly_score: domainData.mobile_friendly_score,
      accessibility_score: domainData.accessibility_score,
      last_updated: new Date()
    };

    await this.manticoreClient.insert('domains_index', manticoreDoc);
  }

  async syncDomainContent(domain, contentData) {
    for (const [pageType, content] of Object.entries(contentData)) {
      const contentDoc = {
        id: this.generateId(`${domain}_${pageType}`),
        domain: domain,
        page_type: pageType,
        title: content.title,
        content: content.content_text,
        headings: content.headings.join(' '),
        meta_keywords: Array.from(content.meta_keywords),
        language: content.language || 'en',
        last_updated: new Date()
      };

      await this.manticoreClient.insert('domain_content_index', contentDoc);
    }
  }
}
```

### Redis Data Structures

```javascript
// Caching Keys
const CACHE_KEYS = {
  domainRanking: 'ranking:domain:{domain}',
  globalRankings: 'rankings:global:{page}',
  categoryRankings: 'rankings:category:{category}:{page}',
  domainAnalysis: 'analysis:{domain}',
  topDomains: 'top:domains:{category}:{timeframe}',
  searchResults: 'search:results:{query_hash}',
  userSession: 'session:{sessionId}'
};

// Job Queue Structure
const JOB_QUEUES = {
  domainCrawl: 'queue:domain:crawl',
  rankingUpdate: 'queue:ranking:update',
  trafficAnalysis: 'queue:traffic:analysis',
  backlinkAnalysis: 'queue:backlink:analysis',
  manticoreSync: 'queue:manticore:sync',
  maintenance: 'queue:maintenance'
};

// Real-time Data Structures
const REALTIME_KEYS = {
  crawlStats: 'stats:crawl:realtime',
  rankingUpdates: 'updates:rankings:stream',
  searchStats: 'stats:search:realtime',
  systemHealth: 'health:system:status'
};
```

## Error Handling

### Crawler Service Error Handling

```javascript
class CrawlerErrorHandler {
  async handleCrawlFailure(job, error) {
    if (job.retryCount < MAX_RETRIES) {
      await this.scheduleRetry(job, this.calculateBackoff(job.retryCount));
    } else {
      await this.markJobFailed(job, error);
      await this.notifyAdministrator(job, error);
    }
  }
  
  calculateBackoff(retryCount) {
    return Math.min(300000, 1000 * Math.pow(2, retryCount)); // Exponential backoff, max 5 minutes
  }
}
```

### Database Connection Resilience

```javascript
class DatabaseManager {
  constructor() {
    this.scyllaClient = new ScyllaClient({ 
      contactPoints: ['scylla:9042'],
      localDataCenter: 'datacenter1',
      pooling: { maxRequestsPerConnection: 32768 }
    });
    
    this.mariaPool = mysql.createPool({
      host: 'mariadb',
      user: process.env.MARIA_USER,
      password: process.env.MARIA_PASSWORD,
      database: process.env.MARIA_DATABASE,
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000
    });
  }
  
  async executeWithRetry(operation, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) throw error;
        await this.delay(1000 * attempt);
      }
    }
  }
}
```

## Testing Strategy

### Unit Testing
- **Framework**: Jest for all Node.js services
- **Coverage Target**: 80% code coverage minimum
- **Mock Strategy**: Mock external services (databases, APIs) for isolated testing

### Integration Testing
- **Database Testing**: Use Docker containers with test databases
- **API Testing**: Supertest for HTTP endpoint testing
- **Service Communication**: Test Redis-SMQ job processing

### End-to-End Testing
- **Framework**: Playwright for browser automation
- **Scenarios**: Complete user journeys from domain search to detailed analysis
- **Performance Testing**: Load testing with Artillery.js for high-volume domain analysis

### Domain Analysis Algorithm Testing

```javascript
describe('Domain Analysis Algorithm', () => {
  test('should calculate performance score correctly', () => {
    const performanceData = {
      loadTime: 1200,
      firstContentfulPaint: 800,
      largestContentfulPaint: 1500,
      cumulativeLayoutShift: 0.05,
      firstInputDelay: 50
    };
    
    const score = calculatePerformanceScore(performanceData);
    expect(score).toBeGreaterThan(0.8);
  });
  
  test('should evaluate security metrics properly', () => {
    const securityData = {
      sslGrade: 'A+',
      securityHeaders: { hsts: true, csp: true, xframe: true },
      vulnerabilities: []
    };
    
    const score = calculateSecurityScore(securityData);
    expect(score).toBe(1.0);
  });
});
```

### Domain Ranking Algorithm Validation

```javascript
describe('Domain Ranking Algorithm', () => {
  test('should rank domains correctly by composite score', () => {
    const domains = [
      { 
        domain: 'example1.com', 
        performanceScore: 0.9, 
        securityScore: 0.8, 
        seoScore: 0.7,
        technicalScore: 0.8,
        backlinkScore: 0.6
      },
      { 
        domain: 'example2.com', 
        performanceScore: 0.7, 
        securityScore: 0.9, 
        seoScore: 0.8,
        technicalScore: 0.9,
        backlinkScore: 0.8
      }
    ];
    
    const ranked = rankDomains(domains);
    expect(ranked[0].overallScore).toBeGreaterThan(ranked[1].overallScore);
  });
  
  test('should handle category-specific rankings', () => {
    const domains = [
      { domain: 'tech-site.com', category: 'technology' },
      { domain: 'news-site.com', category: 'news' }
    ];
    
    const techRanking = calculateCategoryRank(domains[0], 'technology');
    const newsRanking = calculateCategoryRank(domains[1], 'news');
    
    expect(techRanking).toBeDefined();
    expect(newsRanking).toBeDefined();
  });
});
```

## Page Quality Scoring Algorithm

### Content Quality Metrics

```javascript
class PageQualityAnalyzer {
  calculateContentQuality(pageData) {
    const metrics = {
      contentLength: this.scoreContentLength(pageData.textContent.length),
      readability: this.scoreReadability(pageData.fleschKincaidScore),
      mediaRichness: this.scoreMediaContent(pageData.images, pageData.videos),
      structuralQuality: this.scoreHeadingStructure(pageData.headings),
      engagement: this.scoreEngagementSignals(pageData.timeOnPage, pageData.bounceRate)
    };
    
    return this.weightedAverage(metrics, {
      contentLength: 0.25,
      readability: 0.20,
      mediaRichness: 0.20,
      structuralQuality: 0.15,
      engagement: 0.20
    });
  }
  
  scoreContentLength(length) {
    // Optimal content length: 1500-3000 words
    if (length < 300) return 0.2;
    if (length < 800) return 0.5;
    if (length < 1500) return 0.7;
    if (length <= 3000) return 1.0;
    return Math.max(0.6, 1.0 - (length - 3000) / 10000);
  }
  
  scoreReadability(fleschScore) {
    // Flesch-Kincaid Reading Ease: 60-70 is optimal
    if (fleschScore >= 60 && fleschScore <= 70) return 1.0;
    return Math.max(0.3, 1.0 - Math.abs(fleschScore - 65) / 100);
  }
}
```

## Open-Source Backlink Analysis Strategy

### Internal Link Analysis Approach

```javascript
class BacklinkAnalyzer {
  async analyzeBacklinks(domain) {
    // Strategy 1: Common Crawl data analysis
    const commonCrawlLinks = await this.searchCommonCrawl(domain);
    
    // Strategy 2: Social media mentions
    const socialMentions = await this.analyzeSocialMentions(domain);
    
    // Strategy 3: Directory and citation analysis
    const directoryLinks = await this.findDirectoryListings(domain);
    
    // Strategy 4: Competitor backlink comparison
    const competitorAnalysis = await this.analyzeCompetitorBacklinks(domain);
    
    return this.calculateLinkAuthorityScore({
      commonCrawlLinks,
      socialMentions,
      directoryLinks,
      competitorAnalysis
    });
  }
  
  async searchCommonCrawl(domain) {
    // Use Common Crawl Index API (free)
    const response = await fetch(
      `https://index.commoncrawl.org/CC-MAIN-2024-10-index?url=${domain}&output=json`
    );
    return this.processCommonCrawlResults(await response.json());
  }
}
```

## Deployment Architecture

### Docker Container Strategy

```yaml
# docker-compose.yml
version: '3.8'
services:
  web-app:
    build: ./services/web-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SCYLLA_HOSTS=scylla:9042
      - MARIA_HOST=mariadb:3306
      - REDIS_URL=redis://redis:6379
    depends_on:
      - scylla
      - mariadb
      - redis
      - manticore
  
  crawler:
    build: ./services/crawler
    environment:
      - BROWSERLESS_URL=http://browserless:3000
      - IMAGE_PROXY_URL=http://image-proxy:8080
    depends_on:
      - scylla
      - mariadb
      - redis
      - browserless
  
  ranking-engine:
    build: ./services/ranking-engine
    depends_on:
      - scylla
      - mariadb
      - redis
  
  scylla:
    image: scylladb/scylla:5.2
    ports:
      - "9042:9042"
    volumes:
      - scylla_data:/var/lib/scylla
  
  mariadb:
    image: mariadb:10.11
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=domain_ranking
    volumes:
      - maria_data:/var/lib/mysql
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
  
  manticore:
    image: manticoresearch/manticore:6.0.4
    volumes:
      - manticore_data:/var/lib/manticore
  
  browserless:
    image: browserless/chrome:latest
    environment:
      - MAX_CONCURRENT_SESSIONS=5
      - CONNECTION_TIMEOUT=60000
  
  image-proxy:
    image: weserv/images:5.x
    ports:
      - "8080:80"

volumes:
  scylla_data:
  maria_data:
  redis_data:
  manticore_data:
```

### Resource Optimization

- **Memory Limits**: Set appropriate container memory limits based on service requirements
- **CPU Allocation**: Use CPU limits to prevent resource contention
- **Database Tuning**: Optimize ScyllaDB and MariaDB configurations for VDS deployment
- **Connection Pooling**: Implement efficient connection pooling for all database connections
