# Design Document

## Overview

The domain-seeder service is a specialized domain discovery system that continuously identifies NEW domains absent from our database by implementing intelligent discovery strategies beyond simple top-list fetching. The service operates as a standalone Node.js application that employs multiple discovery approaches: incremental zone file processing for newly registered domains, temporal analysis of ranking changes to catch rising domains, long-tail exploration through Common Crawl and FDNS data, and differential analysis of ranking lists to identify domains that weren't previously in top positions. The architecture emphasizes discovering domains we don't have yet rather than re-processing the same popular domains, using streaming data processing, strict backpressure controls, and comprehensive monitoring to ensure efficient domain discovery without overwhelming infrastructure.

**Key Innovation**: Instead of always fetching the same top domains, the service implements discovery strategies that focus on change detection, new registrations, rising domains, and long-tail exploration to continuously find domains absent from our existing database as referenced in `database/sample-data/domains.json` and the domain ranking algorithms described in `docs/top-domain-ranking-algorithm.md`.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "External Sources"
        TRANCO[Tranco Top Sites]
        RADAR[Cloudflare Radar]
        UMBRELLA[Cisco Umbrella 1M]
        CZDS[CZDS Zone Files]
        PIR[PIR .org Registry]
        CC[Common Crawl]
        SONAR[Rapid7 FDNS/Sonar]
    end

    subgraph "Discovery Strategies"
        DIFF[Differential Analysis<br/>New entries in rankings]
        ZONE[Zone File Processing<br/>New registrations]
        LONGTAIL[Long-tail Exploration<br/>Beyond top lists]
        TEMPORAL[Temporal Analysis<br/>Rising domains]
    end

    subgraph "Domain Seeder Service"
        SCHEDULER[Intelligent Scheduler]
        FETCHERS[Source Connectors]
        DISCOVERER[Discovery Engine]
        NORMALIZER[Domain Normalizer]
        DEDUPER[Deduplicator]
        CHECKER[DB Existence Checker]
        ENQUEUER[Domain Enqueuer]
        METRICS[Metrics Collector]
    end

    subgraph "State Management"
        SNAPSHOTS[Historical Snapshots]
        PSL[Public Suffix List]
        BLOOM[Bloom Filter Cache]
        RATELIMITER[Rate Limiter]
    end

    subgraph "Storage Layer"
        REDIS[(Redis<br/>Queue + Cache)]
        SCYLLA[(ScyllaDB<br/>Domain Check)]
        MARIA[(MariaDB<br/>Domain Check)]
        MANTICORE[(Manticore<br/>Domain Check)]
    end

    subgraph "Output"
        NEWQUEUE[new:domains Queue]
        HIGHQUEUE[new:domains:high Queue]
        PROVENANCE[Domain Discovery Table]
    end

    TRANCO --> FETCHERS
    RADAR --> FETCHERS
    UMBRELLA --> FETCHERS
    CZDS --> FETCHERS
    PIR --> FETCHERS
    CC --> FETCHERS
    SONAR --> FETCHERS

    FETCHERS --> DIFF
    FETCHERS --> ZONE
    FETCHERS --> LONGTAIL
    FETCHERS --> TEMPORAL

    DIFF --> DISCOVERER
    ZONE --> DISCOVERER
    LONGTAIL --> DISCOVERER
    TEMPORAL --> DISCOVERER

    SCHEDULER --> FETCHERS
    DISCOVERER --> NORMALIZER
    NORMALIZER --> PSL
    NORMALIZER --> DEDUPER
    DEDUPER --> BLOOM
    DEDUPER --> CHECKER
    CHECKER --> SCYLLA
    CHECKER --> MARIA
    CHECKER --> MANTICORE
    CHECKER --> ENQUEUER
    ENQUEUER --> RATELIMITER
    ENQUEUER --> REDIS
    ENQUEUER --> NEWQUEUE
    ENQUEUER --> HIGHQUEUE
    ENQUEUER --> PROVENANCE

    SNAPSHOTS --> DIFF
    SNAPSHOTS --> TEMPORAL
    METRICS --> REDIS
```

### Discovery Strategies

The service implements four core discovery strategies to avoid the problem of repeatedly processing the same top domains:

#### 0. Zone DIFF Registration Strategy

- Purpose: Discover brand‑new domains from authoritative registry sources
- Method: Ingest CZDS/registry zone DIFFs and emit only creations since last cursor per TLD
- Implementation: Maintain per‑TLD cursor {zone_date, file_offset, last_hash}; parse DIFF records; ignore deletions; validate via PSL/IDN; enqueue new domains
- Example: .com creations on 2025‑08‑05 not present in seen_all_time

#### 1. Differential Analysis Strategy

- **Purpose**: Identify domains that newly appear in ranking lists
- **Method**: Compare current ranking snapshots with historical snapshots stored in Redis/ScyllaDB
- **Implementation**: Store daily snapshots of top-1M from each source, compute set differences to find new entries
- **Example**: If yesterday's Tranco had domains 1-1000000, and today has a new domain at position 50000, that's a discovery candidate

#### 2. Zone File Processing Strategy

- **Purpose**: Catch newly registered domains from authoritative sources
- **Method**: Process CZDS zone file diffs to identify domains registered in the last 24-48 hours
- **Implementation**: Compare zone file timestamps and domain creation dates, focus on recently registered domains
- **Example**: New .com registrations from yesterday's zone file that don't exist in our database

#### 3. Long-tail Exploration Strategy

- **Purpose**: Discover domains beyond typical top-1M lists
- **Method**: Process Common Crawl host frequency data and FDNS data starting from position 1M+ in rankings
- **Implementation**: Skip domains 1-1M (likely already known), process domains ranked 1M-10M+
- **Example**: A domain ranked 2.5M in Common Crawl that we've never seen before

#### 4. Temporal Analysis Strategy

- **Purpose**: Identify rapidly rising domains that may not be in our database yet
- **Method**: Track ranking velocity and identify domains with significant upward movement
- **Implementation**: Calculate ranking deltas over time, prioritize domains with >50% rank improvement week-over-week
- **Example**: A domain that moved from position 800k to 200k in Cloudflare Radar over 7 days

### Service Communication Flow

1. **Intelligent Scheduling**: Scheduler triggers different strategies based on data freshness and discovery goals
2. **Multi-Strategy Fetching**: Source connectors employ different strategies rather than just fetching top-N
3. **Discovery Engine**: Processes fetched data through appropriate discovery strategy filters
4. **Historical Comparison**: Compare against stored snapshots to identify truly new domains
5. **Normalization**: Domains are normalized to eTLD+1 using PSL, converted to lowercase/punycode
6. **Deduplication**: In-memory bloom filter eliminates duplicates within processing batches
7. **Existence Check**: Batch queries against Scylla/Maria/Manticore to identify missing domains
8. **Rate-Limited Enqueue**: Missing domains are enqueued with strict backpressure controls
9. **Provenance Tracking**: Lightweight metadata stored for discovery audit trail with strategy used

## Content Generation (Phase 1 in Seeder)

- Purpose: immediately attach SEO-friendly baseline content to new domains without live fetching
- Inputs: domain, TLD, IDN data, optional language/country hints; source provenance
- Outputs: overview.summary (80–160 words), metadata.category {primary, secondary}, metadata.tags[≥5], seo.seoSummary; generation {preGenerated:true, method:self, inputs:["heuristics"], version}
- Heuristics: TLD/category priors, name tokenization, brand detection for top lists, language defaults by TLD
- Validation: enforce ISO codes, schema validation against updated domain-description schema; quarantine on failure
- Enqueue payload: include content snippet for UI preview; full content stored in domain_discovery
- Handoff: scheduler regenerates (Phase 2 live) per docs/domain-content-generation-plan.md

## Reliability Enhancements

- Idempotency: Redis Streams with consumer groups and ACKs; SETNX discovered:{domain} TTL 30d; per-shard Bloom filters
- Persistence/Resume: per-source cursors (offset/date/hash) stored in Scylla/Redis; append-only discovery log; batch commit checkpoints
- Validation: strict PSL/IDN validators; ISO3166-1 and ISO639-1 enforcement; quarantine bucket for malformed inputs with retry policies
- Self-healing: circuit breakers per source/DB; exponential backoff with jitter; adaptive batch sizes based on error rates; autoscaling on queue depth and source staleness
- Exactly-once semantics: dedupe on enqueue via discovered keys; stream ACKs; reconciliation job to de-duplicate downstream

## Components and Interfaces

### 1. Source Connectors

**Technology Stack:**

- **HTTP Client**: axios with retry logic and timeout handling
- **Streaming**: Node.js streams for large dataset processing
- **Authentication**: Environment-based credential management
- **Rate Limiting**: Per-source rate limiting to respect ToS

**Source Connector Interface:**

```typescript
interface SourceConnector {
  name: string;
  priority: number;
  cadence: "daily" | "weekly" | "monthly";

  fetchDomains(options: FetchOptions): AsyncIterable<DomainCandidate>;
  getLastUpdate(): Promise<Date | null>;
  healthCheck(): Promise<boolean>;
}

interface DomainCandidate {
  domain: string;
  rank?: number;
  source: string;
  metadata?: Record<string, any>;
}

interface FetchOptions {
  limit?: number;
  offset?: number;
  since?: Date;
  tiers?: ("top-10k" | "100k" | "1M" | "long-tail")[];
}
```

**Individual Source Implementations:**

```typescript
// Tranco Connector
class TrancoConnector implements SourceConnector {
  name = "tranco";
  priority = 1;
  cadence = "daily" as const;

  async *fetchDomains(options: FetchOptions): AsyncIterable<DomainCandidate> {
    const response = await axios.get("https://tranco-list.eu/top-1m.csv.zip", {
      responseType: "stream",
      timeout: 30000,
    });

    // Stream and parse CSV data
    for await (const chunk of this.parseCSVStream(response.data)) {
      yield {
        domain: chunk.domain,
        rank: chunk.rank,
        source: "tranco",
        metadata: { listDate: chunk.date },
      };
    }
  }
}

// Cloudflare Radar Connector
class RadarConnector implements SourceConnector {
  name = "radar";
  priority = 2;
  cadence = "daily" as const;

  async *fetchDomains(options: FetchOptions): AsyncIterable<DomainCandidate> {
    const apiKey = process.env.CLOUDFLARE_API_KEY;
    const response = await axios.get(
      "https://api.cloudflare.com/client/v4/radar/ranking/top",
      {
        headers: { Authorization: `Bearer ${apiKey}` },
        params: { limit: options.limit || 1000000 },
      }
    );

    for (const item of response.data.result) {
      yield {
        domain: item.domain,
        rank: item.rank,
        source: "radar",
        metadata: { category: item.category },
      };
    }
  }
}

// CZDS Zone File Connector
class CZDSConnector implements SourceConnector {
  name = "czds";
  priority = 4;
  cadence = "daily" as const;

  async *fetchDomains(options: FetchOptions): AsyncIterable<DomainCandidate> {
    const username = process.env.CZDS_USERNAME;
    const password = process.env.CZDS_PASSWORD;

    // Authenticate and get zone file URLs
    const zones = await this.getAvailableZones(username, password);

    for (const zone of zones) {
      const zoneData = await this.downloadZoneFile(
        zone.url,
        username,
        password
      );

      for await (const domain of this.parseZoneFile(zoneData)) {
        yield {
          domain,
          source: "czds",
          metadata: { tld: zone.tld, zoneDate: zone.date },
        };
      }
    }
  }
}
```

### 2. Discovery Engine

**Responsibilities:**

- Coordinate multiple discovery strategies
- Manage historical snapshots for differential analysis
- Implement strategy-specific filtering and processing
- Prioritize discoveries based on strategy and confidence

**Discovery Engine Interface:**

```typescript
interface DiscoveryEngine {
  processWithStrategy(
    strategy: DiscoveryStrategy,
    candidates: DomainCandidate[]
  ): Promise<DiscoveredDomain[]>;

  getHistoricalSnapshot(
    source: string,
    date: string
  ): Promise<DomainSnapshot | null>;
  storeSnapshot(source: string, domains: DomainCandidate[]): Promise<void>;
}

interface DiscoveredDomain extends DomainCandidate {
  discoveryStrategy: DiscoveryStrategy;
  confidence: number;
  discoveryReason: string;
}

type DiscoveryStrategy = "differential" | "zone-new" | "long-tail" | "temporal";

class IntelligentDiscoveryEngine implements DiscoveryEngine {
  private snapshotStore: SnapshotStore;
  private strategies: Map<DiscoveryStrategy, StrategyProcessor>;

  constructor(snapshotStore: SnapshotStore) {
    this.snapshotStore = snapshotStore;
    this.strategies = new Map([
      ["differential", new DifferentialAnalysisProcessor()],
      ["zone-new", new ZoneFileProcessor()],
      ["long-tail", new LongTailProcessor()],
      ["temporal", new TemporalAnalysisProcessor()],
    ]);
  }

  async processWithStrategy(
    strategy: DiscoveryStrategy,
    candidates: DomainCandidate[]
  ): Promise<DiscoveredDomain[]> {
    const processor = this.strategies.get(strategy);
    if (!processor) {
      throw new Error(`Unknown strategy: ${strategy}`);
    }

    return processor.process(candidates, this.snapshotStore);
  }
}

// Strategy Processors
class DifferentialAnalysisProcessor implements StrategyProcessor {
  async process(
    candidates: DomainCandidate[],
    snapshotStore: SnapshotStore
  ): Promise<DiscoveredDomain[]> {
    const discovered: DiscoveredDomain[] = [];
    const source = candidates[0]?.source;

    if (!source) return discovered;

    // Get yesterday's snapshot
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const historicalSnapshot = await snapshotStore.getSnapshot(
      source,
      yesterday.toISOString().split("T")[0]
    );

    if (!historicalSnapshot) {
      // First run - store current snapshot and return empty (no diff possible)
      await snapshotStore.storeSnapshot(source, candidates);
      return discovered;
    }

    // Find domains in current list that weren't in historical snapshot
    const historicalDomains = new Set(
      historicalSnapshot.domains.map((d) => d.domain)
    );

    for (const candidate of candidates) {
      if (!historicalDomains.has(candidate.domain)) {
        discovered.push({
          ...candidate,
          discoveryStrategy: "differential",
          confidence: 0.9, // High confidence - definitely new in rankings
          discoveryReason: `New entry in ${source} rankings (not in previous snapshot)`,
        });
      }
    }

    // Store current snapshot for next comparison
    await snapshotStore.storeSnapshot(source, candidates);

    return discovered;
  }
}

class ZoneFileProcessor implements StrategyProcessor {
  async process(
    candidates: DomainCandidate[],
    snapshotStore: SnapshotStore
  ): Promise<DiscoveredDomain[]> {
    const discovered: DiscoveredDomain[] = [];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 2); // Last 48 hours

    for (const candidate of candidates) {
      // Check if domain was registered recently (from CZDS metadata)
      const registrationDate = candidate.metadata?.registrationDate;
      if (registrationDate && new Date(registrationDate) > cutoffDate) {
        discovered.push({
          ...candidate,
          discoveryStrategy: "zone-new",
          confidence: 0.95, // Very high confidence - newly registered
          discoveryReason: `Newly registered domain (registered ${registrationDate})`,
        });
      }
    }

    return discovered;
  }
}

class LongTailProcessor implements StrategyProcessor {
  async process(
    candidates: DomainCandidate[],
    snapshotStore: SnapshotStore
  ): Promise<DiscoveredDomain[]> {
    const discovered: DiscoveredDomain[] = [];
    const LONG_TAIL_THRESHOLD = 1000000; // Beyond top 1M

    for (const candidate of candidates) {
      // Only process domains ranked beyond typical top lists
      if (candidate.rank && candidate.rank > LONG_TAIL_THRESHOLD) {
        discovered.push({
          ...candidate,
          discoveryStrategy: "long-tail",
          confidence: 0.7, // Medium confidence - might be noise
          discoveryReason: `Long-tail discovery (rank ${candidate.rank} in ${candidate.source})`,
        });
      }
    }

    return discovered;
  }
}

class TemporalAnalysisProcessor implements StrategyProcessor {
  async process(
    candidates: DomainCandidate[],
    snapshotStore: SnapshotStore
  ): Promise<DiscoveredDomain[]> {
    const discovered: DiscoveredDomain[] = [];
    const source = candidates[0]?.source;

    if (!source) return discovered;

    // Get snapshots from last 7 days
    const snapshots = await snapshotStore.getRecentSnapshots(source, 7);

    if (snapshots.length < 2) {
      return discovered; // Need at least 2 snapshots for temporal analysis
    }

    // Build ranking history for each domain
    const rankingHistory = new Map<string, number[]>();

    for (const snapshot of snapshots) {
      for (const domain of snapshot.domains) {
        if (!rankingHistory.has(domain.domain)) {
          rankingHistory.set(domain.domain, []);
        }
        rankingHistory.get(domain.domain)!.push(domain.rank || Infinity);
      }
    }

    // Identify rapidly rising domains
    for (const candidate of candidates) {
      const history = rankingHistory.get(candidate.domain);
      if (!history || history.length < 2) continue;

      const currentRank = candidate.rank || Infinity;
      const previousRank = history[history.length - 2];

      // Check for significant improvement (50% or better)
      if (previousRank !== Infinity && currentRank < previousRank * 0.5) {
        const improvement = Math.round(
          ((previousRank - currentRank) / previousRank) * 100
        );

        discovered.push({
          ...candidate,
          discoveryStrategy: "temporal",
          confidence: 0.8, // High confidence - clear upward trend
          discoveryReason: `Rising domain (${improvement}% rank improvement from ${previousRank} to ${currentRank})`,
        });
      }
    }

    return discovered;
  }
}
```

### 3. Domain Normalization Engine

**Responsibilities:**

- eTLD+1 extraction using Public Suffix List
- Lowercase and punycode conversion
- IP address and invalid TLD filtering
- Domain validation and sanitization

**Normalization Interface:**

```typescript
interface DomainNormalizer {
  normalize(domain: string): NormalizedDomain | null;
  isValidDomain(domain: string): boolean;
  getETLD1(domain: string): string | null;
}

interface NormalizedDomain {
  original: string;
  normalized: string;
  etld1: string;
  tld: string;
  isValid: boolean;
  errors?: string[];
}

class PSLDomainNormalizer implements DomainNormalizer {
  private psl: PublicSuffixList;
  private updateInterval: number = 7 * 24 * 60 * 60 * 1000; // 7 days

  constructor() {
    this.psl = new PublicSuffixList();
    this.schedulePSLUpdates();
  }

  normalize(domain: string): NormalizedDomain | null {
    try {
      // Remove protocol, www, trailing slashes
      let cleaned = domain
        .toLowerCase()
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .replace(/\/.*$/, "")
        .trim();

      // Check if it's an IP address
      if (this.isIPAddress(cleaned)) {
        return null;
      }

      // Convert to punycode if needed
      const punycode = this.toPunycode(cleaned);

      // Extract eTLD+1
      const etld1 = this.psl.getDomain(punycode);
      if (!etld1) {
        return null;
      }

      // Get TLD
      const tld = this.psl.getPublicSuffix(punycode);

      return {
        original: domain,
        normalized: punycode,
        etld1,
        tld: tld || "",
        isValid: true,
      };
    } catch (error) {
      return {
        original: domain,
        normalized: "",
        etld1: "",
        tld: "",
        isValid: false,
        errors: [error.message],
      };
    }
  }

  private async updatePSL(): Promise<void> {
    try {
      const response = await axios.get(
        "https://publicsuffix.org/list/public_suffix_list.dat"
      );
      this.psl.parse(response.data);
      this.logger.info("PSL updated successfully");
    } catch (error) {
      this.logger.error("Failed to update PSL:", error);
    }
  }
}
```

### 3. Database Existence Checker

**Responsibilities:**

- Batch domain existence queries across multiple databases
- Fallback bloom filter caching for performance
- Redis caching of recent positive results
- Connection pooling and error handling

**Existence Checker Interface:**

```typescript
interface DomainRepository {
  hasDomain(domain: string): Promise<boolean>;
  batchHasDomains(domains: string[]): Promise<Map<string, boolean>>;
  healthCheck(): Promise<boolean>;
}

class CompositeDomainRepository implements DomainRepository {
  private scyllaRepo: ScyllaDomainRepository;
  private mariaRepo: MariaDomainRepository;
  private manticoreRepo: ManticoreDomainRepository;
  private bloomCache: BloomFilter;
  private redisCache: RedisClientWrapper;

  constructor(dbManager: DatabaseManager) {
    this.scyllaRepo = new ScyllaDomainRepository(dbManager.getScyllaClient());
    this.mariaRepo = new MariaDomainRepository(dbManager.getMariaClient());
    this.manticoreRepo = new ManticoreDomainRepository(
      dbManager.getManticoreClient()
    );
    this.bloomCache = new BloomFilter(10000000, 0.01); // 10M items, 1% false positive
    this.redisCache = dbManager.getRedisClient();
  }

  async batchHasDomains(domains: string[]): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const uncachedDomains: string[] = [];

    // Check Redis cache first
    for (const domain of domains) {
      const cached = await this.redisCache.get(`domain:exists:${domain}`);
      if (cached !== null) {
        results.set(domain, cached === "true");
      } else {
        uncachedDomains.push(domain);
      }
    }

    if (uncachedDomains.length === 0) {
      return results;
    }

    // Check bloom filter for quick negative results
    const bloomFiltered = uncachedDomains.filter((domain) => {
      if (!this.bloomCache.test(domain)) {
        results.set(domain, false);
        return false;
      }
      return true;
    });

    if (bloomFiltered.length === 0) {
      return results;
    }

    // Query databases in parallel
    const [scyllaResults, mariaResults, manticoreResults] =
      await Promise.allSettled([
        this.scyllaRepo.batchHasDomains(bloomFiltered),
        this.mariaRepo.batchHasDomains(bloomFiltered),
        this.manticoreRepo.batchHasDomains(bloomFiltered),
      ]);

    // Merge results (domain exists if found in any database)
    for (const domain of bloomFiltered) {
      let exists = false;

      if (
        scyllaResults.status === "fulfilled" &&
        scyllaResults.value.get(domain)
      ) {
        exists = true;
      } else if (
        mariaResults.status === "fulfilled" &&
        mariaResults.value.get(domain)
      ) {
        exists = true;
      } else if (
        manticoreResults.status === "fulfilled" &&
        manticoreResults.value.get(domain)
      ) {
        exists = true;
      }

      results.set(domain, exists);

      // Cache result in Redis (24h TTL)
      await this.redisCache.setex(
        `domain:exists:${domain}`,
        86400,
        exists.toString()
      );

      // Update bloom filter for positive results
      if (exists) {
        this.bloomCache.add(domain);
      }
    }

    return results;
  }
}

class ScyllaDomainRepository implements DomainRepository {
  constructor(private client: ScyllaClient) {}

  async batchHasDomains(domains: string[]): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const batchSize = 5000;

    for (let i = 0; i < domains.length; i += batchSize) {
      const batch = domains.slice(i, i + batchSize);
      const placeholders = batch.map(() => "?").join(",");

      const query = `
        SELECT domain FROM domain_analysis 
        WHERE domain IN (${placeholders})
      `;

      try {
        const result = await this.client.execute(query, batch);
        const foundDomains = new Set(result.rows.map((row) => row.domain));

        for (const domain of batch) {
          results.set(domain, foundDomains.has(domain));
        }
      } catch (error) {
        this.logger.error("Scylla batch query failed:", error);
        // Mark all as unknown on error
        for (const domain of batch) {
          results.set(domain, false);
        }
      }
    }

    return results;
  }
}
```

### 4. Seen‑All‑Time and Cursors

- seen_all_time: Scylla table domain_seen(domain PRIMARY KEY, first_seen_at, last_source, tld)
- per‑source cursor store: Scylla table source_cursor(source, shard, cursor json, PRIMARY KEY(source, shard))
- Redis mirror: discovered:{domain} with TTL for fast dup checks

### 5. Reliability & Resilience System

**Responsibilities:**

- Idempotency guarantees across restarts and failures
- Resume checkpoints for crash recovery
- Strict validation at every pipeline stage
- Redis Streams with ACK-based processing
- Comprehensive backpressure controls
- Crash-safe logging and state management

**Reliability Architecture:**

```typescript
interface ReliabilityManager {
  createCheckpoint(stage: string, data: any): Promise<string>;
  resumeFromCheckpoint(checkpointId: string): Promise<any>;
  validatePipelineStage(stage: string, data: any): Promise<ValidationResult>;
  ensureIdempotency(operation: string, key: string): Promise<boolean>;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedData?: any;
}

class CrashSafeReliabilityManager implements ReliabilityManager {
  private redis: RedisClientWrapper;
  private logger: CrashSafeLogger;
  private checkpointStore: CheckpointStore;

  constructor(redis: RedisClientWrapper) {
    this.redis = redis;
    this.logger = new CrashSafeLogger("domain-seeder");
    this.checkpointStore = new RedisCheckpointStore(redis);
  }

  async createCheckpoint(stage: string, data: any): Promise<string> {
    const checkpointId = `checkpoint:${stage}:${Date.now()}:${crypto.randomUUID()}`;

    const checkpoint = {
      id: checkpointId,
      stage,
      timestamp: new Date().toISOString(),
      data: JSON.stringify(data),
      hash: this.calculateHash(data),
    };

    // Store checkpoint with TTL
    await this.redis.setex(checkpointId, 86400, JSON.stringify(checkpoint)); // 24h TTL

    // Also store in persistent checkpoint log
    await this.checkpointStore.store(checkpoint);

    this.logger.info("Checkpoint created", { checkpointId, stage });
    return checkpointId;
  }

  async resumeFromCheckpoint(checkpointId: string): Promise<any> {
    const checkpointData = await this.redis.get(checkpointId);

    if (!checkpointData) {
      // Try persistent store
      const persistentCheckpoint = await this.checkpointStore.retrieve(
        checkpointId
      );
      if (!persistentCheckpoint) {
        throw new Error(`Checkpoint not found: ${checkpointId}`);
      }
      return JSON.parse(persistentCheckpoint.data);
    }

    const checkpoint = JSON.parse(checkpointData);

    // Verify checkpoint integrity
    const data = JSON.parse(checkpoint.data);
    const expectedHash = this.calculateHash(data);

    if (checkpoint.hash !== expectedHash) {
      throw new Error(`Checkpoint corruption detected: ${checkpointId}`);
    }

    this.logger.info("Resuming from checkpoint", {
      checkpointId,
      stage: checkpoint.stage,
    });
    return data;
  }

  async validatePipelineStage(
    stage: string,
    data: any
  ): Promise<ValidationResult> {
    const validators = {
      "source-fetch": this.validateSourceData,
      normalization: this.validateNormalizedDomains,
      "existence-check": this.validateExistenceResults,
      enqueue: this.validateEnqueueData,
    };

    const validator = validators[stage];
    if (!validator) {
      return { isValid: false, errors: [`Unknown validation stage: ${stage}`] };
    }

    return validator.call(this, data);
  }

  async ensureIdempotency(operation: string, key: string): Promise<boolean> {
    const idempotencyKey = `idempotent:${operation}:${key}`;
    const result = await this.redis.setnx(
      idempotencyKey,
      Date.now().toString()
    );

    if (result === 1) {
      // Set TTL to prevent key accumulation
      await this.redis.expire(idempotencyKey, 86400); // 24 hours
      return true; // Operation can proceed
    }

    return false; // Operation already in progress or completed
  }

  private validateSourceData(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data.candidates)) {
      errors.push("Source data must contain candidates array");
    }

    if (data.candidates.length === 0) {
      warnings.push("No candidates found in source data");
    }

    for (const candidate of data.candidates) {
      if (!candidate.domain) {
        errors.push("Candidate missing domain field");
      }
      if (!candidate.source) {
        errors.push("Candidate missing source field");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitizedData: errors.length === 0 ? data : null,
    };
  }

  private validateNormalizedDomains(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const domain of data.normalizedDomains) {
      if (!domain.etld1) {
        errors.push(`Domain missing eTLD+1: ${domain.original}`);
      }

      if (!this.isValidDomain(domain.etld1)) {
        errors.push(`Invalid eTLD+1 format: ${domain.etld1}`);
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private calculateHash(data: any): string {
    return crypto
      .createHash("sha256")
      .update(JSON.stringify(data))
      .digest("hex");
  }
}
```

### 5. Redis Streams with ACK-based Processing

**Implementation:**

```typescript
interface StreamProcessor {
  processStream(streamName: string, consumerGroup: string): Promise<void>;
  acknowledgeMessage(streamName: string, messageId: string): Promise<void>;
  handleFailedMessage(
    streamName: string,
    messageId: string,
    error: Error
  ): Promise<void>;
}

class ReliableStreamProcessor implements StreamProcessor {
  private redis: RedisClientWrapper;
  private maxRetries = 3;
  private processingTimeout = 300000; // 5 minutes

  constructor(redis: RedisClientWrapper) {
    this.redis = redis;
  }

  async processStream(
    streamName: string,
    consumerGroup: string
  ): Promise<void> {
    const consumerName = `${process.env.HOSTNAME || "unknown"}-${process.pid}`;

    // Create consumer group if it doesn't exist
    try {
      await this.redis.xgroup(
        "CREATE",
        streamName,
        consumerGroup,
        "$",
        "MKSTREAM"
      );
    } catch (error) {
      // Group already exists, continue
    }

    while (true) {
      try {
        // Read from stream
        const messages = await this.redis.xreadgroup(
          "GROUP",
          consumerGroup,
          consumerName,
          "COUNT",
          "10",
          "BLOCK",
          "1000",
          "STREAMS",
          streamName,
          ">"
        );

        if (!messages || messages.length === 0) {
          continue;
        }

        for (const [stream, streamMessages] of messages) {
          for (const [messageId, fields] of streamMessages) {
            await this.processMessage(
              streamName,
              messageId,
              fields,
              consumerGroup
            );
          }
        }

        // Also process pending messages (failed/unacknowledged)
        await this.processPendingMessages(
          streamName,
          consumerGroup,
          consumerName
        );
      } catch (error) {
        this.logger.error("Stream processing error:", error);
        await this.delay(5000); // Wait before retrying
      }
    }
  }

  private async processMessage(
    streamName: string,
    messageId: string,
    fields: any[],
    consumerGroup: string
  ): Promise<void> {
    const startTime = Date.now();

    try {
      // Convert Redis fields array to object
      const messageData = this.fieldsToObject(fields);

      // Validate message structure
      const validation = await this.validateMessage(messageData);
      if (!validation.isValid) {
        throw new Error(`Invalid message: ${validation.errors.join(", ")}`);
      }

      // Process the message based on type
      await this.handleMessage(messageData);

      // Acknowledge successful processing
      await this.acknowledgeMessage(streamName, messageId);

      this.logger.info("Message processed successfully", {
        streamName,
        messageId,
        processingTime: Date.now() - startTime,
      });
    } catch (error) {
      await this.handleFailedMessage(streamName, messageId, error as Error);
    }
  }

  async acknowledgeMessage(
    streamName: string,
    messageId: string
  ): Promise<void> {
    await this.redis.xack(streamName, "domain-seeder-group", messageId);
  }

  async handleFailedMessage(
    streamName: string,
    messageId: string,
    error: Error
  ): Promise<void> {
    this.logger.error("Message processing failed", {
      streamName,
      messageId,
      error: error.message,
    });

    // Get message retry count
    const retryKey = `retry:${streamName}:${messageId}`;
    const retryCount = parseInt((await this.redis.get(retryKey)) || "0");

    if (retryCount < this.maxRetries) {
      // Increment retry count and requeue
      await this.redis.setex(retryKey, 3600, (retryCount + 1).toString());

      // Add to retry stream with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, retryCount), 300000); // Max 5 minutes

      setTimeout(async () => {
        await this.redis.xadd(
          `${streamName}:retry`,
          "*",
          "original_id",
          messageId,
          "retry_count",
          retryCount + 1,
          "error",
          error.message
        );
      }, delay);
    } else {
      // Max retries reached, send to dead letter queue
      await this.redis.xadd(
        `${streamName}:dlq`,
        "*",
        "original_id",
        messageId,
        "final_error",
        error.message,
        "retry_count",
        retryCount
      );
    }

    // Still acknowledge to remove from pending
    await this.acknowledgeMessage(streamName, messageId);
  }

  private async processPendingMessages(
    streamName: string,
    consumerGroup: string,
    consumerName: string
  ): Promise<void> {
    // Get pending messages older than processing timeout
    const pendingMessages = await this.redis.xpending(
      streamName,
      consumerGroup,
      "-",
      "+",
      "10"
    );

    if (!pendingMessages || pendingMessages.length === 0) {
      return;
    }

    const now = Date.now();

    for (const [messageId, consumer, idleTime] of pendingMessages) {
      if (idleTime > this.processingTimeout) {
        // Claim and reprocess stuck message
        const claimedMessages = await this.redis.xclaim(
          streamName,
          consumerGroup,
          consumerName,
          this.processingTimeout,
          messageId
        );

        if (claimedMessages && claimedMessages.length > 0) {
          const [claimedId, fields] = claimedMessages[0];
          await this.processMessage(
            streamName,
            claimedId,
            fields,
            consumerGroup
          );
        }
      }
    }
  }
}
```

### 6. Rate-Limited Domain Enqueuer with Enhanced Reliability

**Responsibilities:**

- Redis Streams queue management with ACK-based processing
- Token bucket rate limiting with jitter
- Idempotency checking to prevent re-enqueuing
- Tiered queue management (high priority vs normal)
- Crash-safe state management
- Comprehensive backpressure controls

**Enqueuer Interface:**

```typescript
interface DomainEnqueuer {
  enqueueDomains(domains: EnqueueDomain[]): Promise<EnqueueResult>;
  getQueueDepth(): Promise<number>;
  checkRateLimit(): Promise<RateLimitStatus>;
}

interface EnqueueDomain {
  domain: string;
  source: string;
  tier: "top-10k" | "100k" | "1M" | "long-tail";
  metadata?: Record<string, any>;
}

interface EnqueueResult {
  enqueued: number;
  skipped: number;
  rateLimited: number;
  errors: string[];
}

interface RateLimitStatus {
  tokensAvailable: number;
  resetTime: Date;
  isLimited: boolean;
}

class RateLimitedDomainEnqueuer implements DomainEnqueuer {
  private redis: RedisClientWrapper;
  private tokenBucket: TokenBucket;
  private config: SeederConfig;

  constructor(redis: RedisClientWrapper, config: SeederConfig) {
    this.redis = redis;
    this.config = config;
    this.tokenBucket = new TokenBucket({
      capacity: config.ENQUEUE_BATCH,
      refillRate: config.ENQUEUE_BATCH / (config.ENQUEUE_INTERVAL_MS / 1000),
      refillPeriod: config.ENQUEUE_INTERVAL_MS,
    });
  }

  async enqueueDomains(domains: EnqueueDomain[]): Promise<EnqueueResult> {
    const result: EnqueueResult = {
      enqueued: 0,
      skipped: 0,
      rateLimited: 0,
      errors: [],
    };

    // Check queue depth backpressure
    const queueDepth = await this.getQueueDepth();
    if (queueDepth >= this.config.NEW_QUEUE_MAX_DEPTH) {
      result.errors.push(
        `Queue depth ${queueDepth} exceeds maximum ${this.config.NEW_QUEUE_MAX_DEPTH}`
      );
      return result;
    }

    // Check daily limit
    const dailyCount = await this.getDailyEnqueueCount();
    if (dailyCount >= this.config.MAX_NEW_PER_DAY) {
      result.errors.push(`Daily limit ${this.config.MAX_NEW_PER_DAY} reached`);
      return result;
    }

    // Process domains in batches
    for (let i = 0; i < domains.length; i += this.config.ENQUEUE_BATCH) {
      const batch = domains.slice(i, i + this.config.ENQUEUE_BATCH);

      // Check rate limit
      if (!(await this.tokenBucket.consume(batch.length))) {
        result.rateLimited += batch.length;
        continue;
      }

      // Check idempotency
      const newDomains = await this.filterAlreadyDiscovered(batch);
      result.skipped += batch.length - newDomains.length;

      if (newDomains.length === 0) {
        continue;
      }

      // Enqueue to appropriate queues
      const highPriorityDomains = newDomains.filter(
        (d) => d.tier === "top-10k"
      );
      const normalDomains = newDomains.filter((d) => d.tier !== "top-10k");

      try {
        // Enqueue high priority domains
        if (highPriorityDomains.length > 0) {
          await this.enqueueToQueue("new:domains:high", highPriorityDomains);
        }

        // Enqueue normal domains
        if (normalDomains.length > 0) {
          await this.enqueueToQueue("new:domains", normalDomains);
        }

        // Mark as discovered for idempotency
        await this.markAsDiscovered(newDomains);

        // Store provenance data
        await this.storeProvenance(newDomains);

        result.enqueued += newDomains.length;

        // Update daily counter
        await this.incrementDailyCount(newDomains.length);
      } catch (error) {
        result.errors.push(`Enqueue batch failed: ${error.message}`);
      }

      // Add jitter to avoid burst patterns
      if (i + this.config.ENQUEUE_BATCH < domains.length) {
        const jitter = Math.random() * this.config.ENQUEUE_INTERVAL_MS * 0.1;
        await this.delay(this.config.ENQUEUE_INTERVAL_MS + jitter);
      }
    }

    return result;
  }

  private async filterAlreadyDiscovered(
    domains: EnqueueDomain[]
  ): Promise<EnqueueDomain[]> {
    const pipeline = this.redis.pipeline();

    for (const domain of domains) {
      pipeline.exists(`discovered:${domain.domain}`);
    }

    const results = await pipeline.exec();
    const newDomains: EnqueueDomain[] = [];

    for (let i = 0; i < domains.length; i++) {
      if (results[i][1] === 0) {
        // Domain not discovered yet
        newDomains.push(domains[i]);
      }
    }

    return newDomains;
  }

  private async markAsDiscovered(domains: EnqueueDomain[]): Promise<void> {
    const pipeline = this.redis.pipeline();
    const ttl = 30 * 24 * 60 * 60; // 30 days

    for (const domain of domains) {
      pipeline.setex(`discovered:${domain.domain}`, ttl, Date.now().toString());
    }

    await pipeline.exec();
  }
}
```

### 5. Metrics and Monitoring

**Prometheus Metrics:**

```typescript
interface SeederMetrics {
  // Source metrics
  candidates_fetched_total: Counter;
  candidates_after_normalize: Counter;
  known_in_db_total: Counter;
  new_discovered_total: Counter;

  // Enqueue metrics
  enqueue_attempts_total: Counter;
  enqueue_success_total: Counter;
  rate_limited_total: Counter;

  // System metrics
  queue_depth: Gauge;
  db_check_latency_ms: Histogram;
  source_staleness_seconds: Gauge;

  // Error metrics
  source_errors_total: Counter;
  db_errors_total: Counter;
  enqueue_errors_total: Counter;
}

class MetricsCollector {
  private metrics: SeederMetrics;
  private register: Registry;

  constructor() {
    this.register = new Registry();
    this.initializeMetrics();
  }

  private initializeMetrics(): void {
    this.metrics = {
      candidates_fetched_total: new Counter({
        name: "seeder_candidates_fetched_total",
        help: "Total number of candidate domains fetched from sources",
        labelNames: ["source"],
        registers: [this.register],
      }),

      candidates_after_normalize: new Counter({
        name: "seeder_candidates_after_normalize",
        help: "Number of candidates after normalization and deduplication",
        registers: [this.register],
      }),

      known_in_db_total: new Counter({
        name: "seeder_known_in_db_total",
        help: "Number of domains found to already exist in database",
        registers: [this.register],
      }),

      new_discovered_total: new Counter({
        name: "seeder_new_discovered_total",
        help: "Number of new domains discovered and enqueued",
        labelNames: ["source", "tier"],
        registers: [this.register],
      }),

      enqueue_attempts_total: new Counter({
        name: "seeder_enqueue_attempts_total",
        help: "Total number of enqueue attempts",
        registers: [this.register],
      }),

      enqueue_success_total: new Counter({
        name: "seeder_enqueue_success_total",
        help: "Total number of successful enqueues",
        registers: [this.register],
      }),

      rate_limited_total: new Counter({
        name: "seeder_rate_limited_total",
        help: "Total number of rate limited operations",
        registers: [this.register],
      }),

      queue_depth: new Gauge({
        name: "seeder_queue_depth",
        help: "Current depth of the new domains queue",
        labelNames: ["queue"],
        registers: [this.register],
      }),

      db_check_latency_ms: new Histogram({
        name: "seeder_db_check_latency_ms",
        help: "Database existence check latency in milliseconds",
        buckets: [10, 50, 100, 500, 1000, 5000, 10000],
        registers: [this.register],
      }),

      source_staleness_seconds: new Gauge({
        name: "seeder_source_staleness_seconds",
        help: "Time since last successful fetch from source",
        labelNames: ["source"],
        registers: [this.register],
      }),
    };
  }

  recordCandidatesFetched(source: string, count: number): void {
    this.metrics.candidates_fetched_total.inc({ source }, count);
  }

  recordNewDiscovered(source: string, tier: string, count: number): void {
    this.metrics.new_discovered_total.inc({ source, tier }, count);
  }

  recordDBCheckLatency(latencyMs: number): void {
    this.metrics.db_check_latency_ms.observe(latencyMs);
  }

  updateQueueDepth(queue: string, depth: number): void {
    this.metrics.queue_depth.set({ queue }, depth);
  }

  getMetrics(): string {
    return this.register.metrics();
  }
}
```

## Data Models

### Domain Discovery Schema

```sql
-- ScyllaDB: Domain discovery provenance with strategy tracking
CREATE TABLE domain_discovery (
    domain text PRIMARY KEY,
    first_seen_at timestamp,
    sources set<text>,
    discovery_strategy text,
    discovery_reason text,
    confidence decimal,
    discovery_metadata map<text, text>,
    last_updated timestamp
);

-- ScyllaDB: Historical snapshots for differential analysis
CREATE TABLE source_snapshots (
    source text,
    snapshot_date date,
    domain text,
    rank int,
    metadata map<text, text>,
    PRIMARY KEY (source, snapshot_date, domain)
) WITH CLUSTERING ORDER BY (snapshot_date DESC, domain ASC);

-- ScyllaDB: Discovery statistics by strategy
CREATE TABLE discovery_stats (
    date date,
    source text,
    strategy text,
    tier text,
    discovered_count counter,
    confidence_avg decimal,
    PRIMARY KEY (date, source, strategy, tier)
);

-- ScyllaDB: Temporal analysis tracking for rising domains
CREATE TABLE domain_ranking_history (
    domain text,
    source text,
    date date,
    rank int,
    PRIMARY KEY (domain, source, date)
) WITH CLUSTERING ORDER BY (date DESC);
```

### Redis Data Structures with Streams and Reliability

```typescript
// Stream structures for reliable processing
const STREAM_KEYS = {
  newDomains: "stream:new:domains", // Main discovery stream
  newDomainsHigh: "stream:new:domains:high", // High priority stream
  retryDomains: "stream:new:domains:retry", // Retry stream
  dlqDomains: "stream:new:domains:dlq", // Dead letter queue stream
  checkpoints: "stream:checkpoints", // Checkpoint events stream
};

// Consumer groups for stream processing
const CONSUMER_GROUPS = {
  domainSeeder: "domain-seeder-group",
  scheduler: "scheduler-group",
  monitoring: "monitoring-group",
};

// Cache structures with reliability features
const CACHE_KEYS = {
  discovered: "discovered:{domain}", // Idempotency check with TTL
  exists: "domain:exists:{domain}", // DB existence cache
  dailyCount: "daily:count:{date}", // Daily enqueue counter
  sourceLastUpdate: "source:last:{source}", // Source freshness
  processingLock: "lock:processing:{key}", // Processing locks
  checkpoint: "checkpoint:{stage}:{id}", // Recovery checkpoints
  validation: "validation:{stage}:{hash}", // Validation cache
};

// Rate limiting with backpressure
const RATE_LIMIT_KEYS = {
  tokenBucket: "rate:tokens",
  dailyLimit: "rate:daily:{date}",
  backpressure: "backpressure:active",
  queueDepth: "queue:depth:{stream}",
};

// Reliability and monitoring keys
const RELIABILITY_KEYS = {
  heartbeat: "heartbeat:{service}:{instance}",
  crashRecovery: "crash:recovery:{instance}",
  idempotency: "idempotent:{operation}:{key}",
  retryCount: "retry:{stream}:{messageId}",
  processingTimeout: "timeout:{stream}:{messageId}",
};
```

### Crash-Safe Logging System

```typescript
interface CrashSafeLogger {
  info(message: string, metadata?: any): void;
  warn(message: string, metadata?: any): void;
  error(message: string, metadata?: any): void;
  checkpoint(stage: string, data: any): Promise<void>;
  recovery(instanceId: string): Promise<LogEntry[]>;
}

class ReliableLogger implements CrashSafeLogger {
  private redis: RedisClientWrapper;
  private instanceId: string;
  private logBuffer: LogEntry[] = [];
  private flushInterval: NodeJS.Timeout;

  constructor(serviceName: string, redis: RedisClientWrapper) {
    this.redis = redis;
    this.instanceId = `${serviceName}-${process.env.HOSTNAME || "unknown"}-${
      process.pid
    }`;

    // Flush logs every 5 seconds or on process exit
    this.flushInterval = setInterval(() => this.flushLogs(), 5000);
    process.on("SIGTERM", () => this.gracefulShutdown());
    process.on("SIGINT", () => this.gracefulShutdown());
    process.on("uncaughtException", (error) => {
      this.error("Uncaught exception", {
        error: error.message,
        stack: error.stack,
      });
      this.flushLogs().then(() => process.exit(1));
    });
  }

  info(message: string, metadata?: any): void {
    this.addLogEntry("info", message, metadata);
  }

  warn(message: string, metadata?: any): void {
    this.addLogEntry("warn", message, metadata);
  }

  error(message: string, metadata?: any): void {
    this.addLogEntry("error", message, metadata);
  }

  async checkpoint(stage: string, data: any): Promise<void> {
    const checkpointEntry: LogEntry = {
      level: "checkpoint",
      message: `Checkpoint: ${stage}`,
      timestamp: new Date().toISOString(),
      instanceId: this.instanceId,
      metadata: {
        stage,
        dataHash: this.calculateHash(data),
        dataSize: JSON.stringify(data).length,
      },
    };

    // Immediately flush checkpoint entries
    await this.redis.xadd(
      "stream:logs:checkpoints",
      "*",
      "entry",
      JSON.stringify(checkpointEntry)
    );
  }

  async recovery(instanceId: string): Promise<LogEntry[]> {
    // Retrieve logs for crashed instance
    const logEntries: LogEntry[] = [];

    // Get logs from Redis stream
    const messages = await this.redis.xrange(
      "stream:logs:all",
      "-",
      "+",
      "COUNT",
      "1000"
    );

    for (const [messageId, fields] of messages) {
      const entry = JSON.parse(fields[1]); // Assuming 'entry' is at index 1
      if (entry.instanceId === instanceId) {
        logEntries.push(entry);
      }
    }

    return logEntries.sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  private addLogEntry(level: string, message: string, metadata?: any): void {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      instanceId: this.instanceId,
      metadata: metadata || {},
    };

    this.logBuffer.push(entry);

    // Console output for immediate visibility
    console.log(
      `[${entry.timestamp}] ${level.toUpperCase()}: ${message}`,
      metadata ? JSON.stringify(metadata) : ""
    );

    // Flush immediately for errors
    if (level === "error") {
      this.flushLogs();
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // Write to Redis stream for persistence
      for (const entry of logsToFlush) {
        await this.redis.xadd(
          "stream:logs:all",
          "*",
          "entry",
          JSON.stringify(entry)
        );
      }

      // Update heartbeat
      await this.redis.setex(
        `heartbeat:${this.instanceId}`,
        30, // 30 second TTL
        new Date().toISOString()
      );
    } catch (error) {
      // Fallback to console if Redis fails
      console.error("Failed to flush logs to Redis:", error);
      for (const entry of logsToFlush) {
        console.log("FALLBACK LOG:", JSON.stringify(entry));
      }
    }
  }

  private async gracefulShutdown(): Promise<void> {
    clearInterval(this.flushInterval);
    await this.flushLogs();

    // Mark instance as shutdown
    await this.redis.setex(
      `shutdown:${this.instanceId}`,
      3600, // 1 hour TTL
      new Date().toISOString()
    );
  }

  private calculateHash(data: any): string {
    return crypto
      .createHash("sha256")
      .update(JSON.stringify(data))
      .digest("hex");
  }
}

interface LogEntry {
  level: string;
  message: string;
  timestamp: string;
  instanceId: string;
  metadata: any;
}
```

### Intelligent Scheduling System

**Problem Solved**: The core issue of repeatedly processing the same top domains is addressed through intelligent scheduling that varies strategies and focuses on change detection.

```typescript
interface IntelligentScheduler {
  scheduleDiscoveryRuns(): Promise<void>;
  determineOptimalStrategy(source: string, lastRun: Date): DiscoveryStrategy;
  getDiscoveryEffectiveness(strategy: DiscoveryStrategy): Promise<number>;
}

class SmartDiscoveryScheduler implements IntelligentScheduler {
  private strategies: DiscoveryStrategy[] = [
    "differential",
    "zone-new",
    "long-tail",
    "temporal",
  ];

  async scheduleDiscoveryRuns(): Promise<void> {
    const schedule = {
      // High-frequency differential analysis for top sources
      "tranco-differential": { cron: "0 2 * * *", strategy: "differential" }, // Daily at 2 AM
      "radar-differential": { cron: "0 3 * * *", strategy: "differential" }, // Daily at 3 AM

      // Zone file processing for new registrations
      "czds-zone-new": { cron: "0 4 * * *", strategy: "zone-new" }, // Daily at 4 AM

      // Long-tail exploration (less frequent, more comprehensive)
      "common-crawl-long-tail": { cron: "0 5 * * 0", strategy: "long-tail" }, // Weekly on Sunday
      "sonar-long-tail": { cron: "0 6 1 * *", strategy: "long-tail" }, // Monthly

      // Temporal analysis for rising domains
      "tranco-temporal": { cron: "0 7 * * 1", strategy: "temporal" }, // Weekly on Monday
      "radar-temporal": { cron: "0 8 * * 1", strategy: "temporal" },

      // Adaptive scheduling based on discovery effectiveness
      "adaptive-discovery": { cron: "0 1 * * *", strategy: "adaptive" }, // Daily adaptive run
    };

    for (const [jobName, config] of Object.entries(schedule)) {
      await this.scheduleJob(jobName, config.cron, config.strategy);
    }
  }

  async determineOptimalStrategy(
    source: string,
    lastRun: Date
  ): DiscoveryStrategy {
    const effectiveness = await Promise.all(
      this.strategies.map(async (strategy) => ({
        strategy,
        score: await this.getDiscoveryEffectiveness(strategy),
      }))
    );

    // Sort by effectiveness and return best strategy
    effectiveness.sort((a, b) => b.score - a.score);

    // Add some randomization to avoid getting stuck in local optima
    const topStrategies = effectiveness.slice(0, 2);
    return Math.random() < 0.8
      ? topStrategies[0].strategy
      : topStrategies[1].strategy;
  }

  async getDiscoveryEffectiveness(
    strategy: DiscoveryStrategy
  ): Promise<number> {
    // Calculate effectiveness based on recent discovery rates
    const recentStats = await this.getRecentDiscoveryStats(strategy, 7); // Last 7 days

    const totalDiscovered = recentStats.reduce(
      (sum, stat) => sum + stat.discovered_count,
      0
    );
    const avgConfidence =
      recentStats.reduce((sum, stat) => sum + stat.confidence_avg, 0) /
      recentStats.length;

    // Effectiveness = discovery rate * confidence * recency weight
    return (
      (totalDiscovered / 7) * avgConfidence * this.getRecencyWeight(strategy)
    );
  }

  private getRecencyWeight(strategy: DiscoveryStrategy): number {
    // Weight strategies based on how recently they've been effective
    const weights = {
      differential: 1.0, // Always relevant for new entries
      "zone-new": 0.9, // Slightly less frequent new registrations
      temporal: 0.8, // Rising domains are less frequent
      "long-tail": 0.7, // Long-tail discoveries are rarest but valuable
    };

    return weights[strategy] || 0.5;
  }
}
```

### Configuration Schema

```typescript
interface SeederConfig {
  // Source configuration
  SOURCES_ENABLED: string[];
  TRANCO_URL: string;
  CLOUDFLARE_API_KEY: string;
  UMBRELLA_API_KEY: string;
  CZDS_USERNAME: string;
  CZDS_PASSWORD: string;
  COMMON_CRAWL_INDEX_URL: string;
  SONAR_API_URL: string;

  // Discovery strategy configuration
  DIFFERENTIAL_ANALYSIS_ENABLED: boolean;
  ZONE_FILE_PROCESSING_ENABLED: boolean;
  LONG_TAIL_EXPLORATION_ENABLED: boolean;
  TEMPORAL_ANALYSIS_ENABLED: boolean;

  // Strategy-specific settings
  LONG_TAIL_START_RANK: number; // Default: 1000000
  TEMPORAL_ANALYSIS_DAYS: number; // Default: 7
  ZONE_NEW_CUTOFF_HOURS: number; // Default: 48
  DIFFERENTIAL_SNAPSHOT_RETENTION: number; // Default: 30 days

  // Rate limiting
  ENQUEUE_BATCH: number;
  ENQUEUE_INTERVAL_MS: number;
  MAX_NEW_PER_DAY: number;
  NEW_QUEUE_MAX_DEPTH: number;

  // Database
  DB_CHECK_BATCH: number;
  BLOOM_FP_RATE: number;

  // PSL updates
  PSL_UPDATE_INTERVAL_DAYS: number;

  // Adaptive scheduling
  ADAPTIVE_SCHEDULING_ENABLED: boolean;
  STRATEGY_EFFECTIVENESS_WINDOW_DAYS: number; // Default: 7
  MIN_DISCOVERY_RATE_THRESHOLD: number; // Default: 100 domains/day
}
```

## Error Handling

### Source Connector Resilience

```typescript
class ResilientSourceConnector {
  private maxRetries = 3;
  private baseDelay = 1000;

  async fetchWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === this.maxRetries) {
          break;
        }

        // Exponential backoff with jitter
        const delay = this.baseDelay * Math.pow(2, attempt - 1);
        const jitter = Math.random() * delay * 0.1;
        await this.delay(delay + jitter);

        this.logger.warn(
          `Source fetch attempt ${attempt} failed, retrying:`,
          error.message
        );
      }
    }

    throw lastError;
  }

  async *fetchDomainsWithFallback(
    options: FetchOptions
  ): AsyncIterable<DomainCandidate> {
    try {
      yield* this.fetchDomains(options);
    } catch (error) {
      this.logger.error(`Source ${this.name} failed completely:`, error);
      this.metrics.source_errors_total.inc({ source: this.name });

      // Continue with empty stream rather than failing entire pipeline
      return;
    }
  }
}
```

### Database Connection Resilience

```typescript
class ResilientDomainRepository {
  async batchHasDomainsWithFallback(
    domains: string[]
  ): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    try {
      return await this.batchHasDomains(domains);
    } catch (error) {
      this.logger.error("Primary DB check failed, using fallback:", error);

      // Fallback to bloom filter only
      for (const domain of domains) {
        results.set(domain, this.bloomCache.test(domain));
      }

      return results;
    }
  }
}
```

## Testing Strategy

### Unit Testing

```typescript
describe("DomainNormalizer", () => {
  let normalizer: PSLDomainNormalizer;

  beforeEach(() => {
    normalizer = new PSLDomainNormalizer();
  });

  test("should normalize domain to eTLD+1", () => {
    const result = normalizer.normalize("www.subdomain.example.com");
    expect(result?.etld1).toBe("example.com");
    expect(result?.normalized).toBe("subdomain.example.com");
  });

  test("should handle punycode domains", () => {
    const result = normalizer.normalize("münchen.de");
    expect(result?.normalized).toBe("xn--mnchen-3ya.de");
    expect(result?.etld1).toBe("xn--mnchen-3ya.de");
  });

  test("should reject IP addresses", () => {
    const result = normalizer.normalize("***********");
    expect(result).toBeNull();
  });
});

describe("RateLimitedDomainEnqueuer", () => {
  let enqueuer: RateLimitedDomainEnqueuer;
  let mockRedis: jest.Mocked<RedisClientWrapper>;

  beforeEach(() => {
    mockRedis = createMockRedis();
    enqueuer = new RateLimitedDomainEnqueuer(mockRedis, testConfig);
  });

  test("should respect rate limits", async () => {
    const domains = Array.from({ length: 2000 }, (_, i) => ({
      domain: `example${i}.com`,
      source: "test",
      tier: "100k" as const,
    }));

    const result = await enqueuer.enqueueDomains(domains);

    expect(result.enqueued).toBeLessThanOrEqual(testConfig.ENQUEUE_BATCH);
    expect(result.rateLimited).toBeGreaterThan(0);
  });

  test("should prevent duplicate enqueuing", async () => {
    mockRedis.exists.mockResolvedValue(1); // Domain already discovered

    const domains = [
      { domain: "example.com", source: "test", tier: "100k" as const },
    ];
    const result = await enqueuer.enqueueDomains(domains);

    expect(result.enqueued).toBe(0);
    expect(result.skipped).toBe(1);
  });
});
```

### Integration Testing

```typescript
describe("Domain Seeder Integration", () => {
  let seeder: DomainSeederService;
  let testDb: TestDatabaseManager;

  beforeAll(async () => {
    testDb = new TestDatabaseManager();
    await testDb.setup();
    seeder = new DomainSeederService(testDb);
  });

  afterAll(async () => {
    await testDb.cleanup();
  });

  test("should discover only new domains", async () => {
    // Pre-populate database with known domains
    await testDb.insertDomains(["example.com", "google.com"]);

    // Mock source to return mix of known and unknown domains
    const mockSource = new MockTrancoConnector([
      "example.com", // Known
      "newsite.com", // Unknown
      "google.com", // Known
      "another.com", // Unknown
    ]);

    seeder.addSource(mockSource);

    const result = await seeder.runDiscovery();

    expect(result.discovered).toBe(2);
    expect(result.skipped).toBe(2);

    // Verify only new domains were enqueued
    const queueContents = await testDb.getQueueContents("new:domains");
    expect(queueContents).toContain("newsite.com");
    expect(queueContents).toContain("another.com");
    expect(queueContents).not.toContain("example.com");
    expect(queueContents).not.toContain("google.com");
  });
});
```

### Load Testing

```typescript
describe("Domain Seeder Load Tests", () => {
  test("should handle 10M candidates within limits", async () => {
    const startTime = Date.now();
    const mockSource = new MockMassiveSource(10_000_000);

    const seeder = new DomainSeederService(testConfig);
    seeder.addSource(mockSource);

    const result = await seeder.runDiscovery();

    const duration = Date.now() - startTime;

    // Verify throughput constraints
    expect(result.enqueued).toBeLessThanOrEqual(testConfig.MAX_NEW_PER_DAY);
    expect(result.rateLimited).toBeGreaterThan(0);

    // Verify processing completed within reasonable time
    expect(duration).toBeLessThan(60000); // 1 minute max

    // Verify memory usage stayed reasonable
    const memUsage = process.memoryUsage();
    expect(memUsage.heapUsed).toBeLessThan(1024 * 1024 * 1024); // 1GB max
  });
});
```

## Deployment Architecture

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY tsconfig.json ./

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S seeder && \
    adduser -S seeder -u 1001

USER seeder

EXPOSE 3000

CMD ["node", "dist/index.js"]
```

### Environment Configuration

```bash
# .env.example
NODE_ENV=production
SERVICE_NAME=domain-seeder
SERVICE_PORT=3000

# Database connections
SCYLLA_HOSTS=scylla:9042
MARIA_HOST=mariadb
MARIA_PORT=3306
MARIA_USER=seeder
MARIA_PASSWORD=secure_password
MARIA_DATABASE=domain_ranking
REDIS_URL=redis://redis:6379/0
MANTICORE_HOST=manticore
MANTICORE_PORT=9308

# Source configuration
SOURCES_ENABLED=tranco,radar,czds,cc,sonar
TRANCO_URL=https://tranco-list.eu/top-1m.csv.zip
CLOUDFLARE_API_KEY=your_api_key
CZDS_USERNAME=your_username
CZDS_PASSWORD=your_password
COMMON_CRAWL_INDEX_URL=https://index.commoncrawl.org/
SONAR_API_URL=https://opendata.rapid7.com/

# Rate limiting
ENQUEUE_BATCH=1000
ENQUEUE_INTERVAL_MS=1000
MAX_NEW_PER_DAY=500000
NEW_QUEUE_MAX_DEPTH=200000

# Database tuning
DB_CHECK_BATCH=5000
BLOOM_FP_RATE=0.01

# Scheduling
PSL_UPDATE_INTERVAL_DAYS=7
TRANCO_SCHEDULE=0 2 * * *
RADAR_SCHEDULE=0 3 * * *
CZDS_SCHEDULE=0 4 * * *
COMMON_CRAWL_SCHEDULE=0 5 * * 0
SONAR_SCHEDULE=0 6 1 * *

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=info
```

### Health Checks and Monitoring

```typescript
class HealthChecker {
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabaseConnections(),
      this.checkRedisConnection(),
      this.checkQueueHealth(),
      this.checkSourceConnectivity(),
      this.checkRateLimiterHealth(),
    ]);

    const healthy = checks.every(
      (check) => check.status === "fulfilled" && check.value === true
    );

    return {
      status: healthy ? "healthy" : "unhealthy",
      timestamp: new Date().toISOString(),
      checks: {
        database: checks[0].status === "fulfilled" ? checks[0].value : false,
        redis: checks[1].status === "fulfilled" ? checks[1].value : false,
        queue: checks[2].status === "fulfilled" ? checks[2].value : false,
        sources: checks[3].status === "fulfilled" ? checks[3].value : false,
        rateLimiter: checks[4].status === "fulfilled" ? checks[4].value : false,
      },
    };
  }
}
```

This design provides a comprehensive, scalable, and resilient domain discovery system that efficiently identifies new domains while respecting infrastructure constraints and external service limitations.
