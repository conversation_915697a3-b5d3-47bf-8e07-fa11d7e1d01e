# Requirements Document

## Introduction

The domain-seeder service is a specialized discovery system that continuously identifies NEW domains absent from our database by aggregating candidates from external popularity sources. Unlike ranking systems, this service focuses purely on domain discovery and seeding - it finds domains we don't have yet, normalizes them to eTLD+1 format, and enqueues only missing domains for downstream processing. The system operates with strict backpressure controls and prioritizes high-popularity sources first, backfilling downward to ensure comprehensive coverage without overwhelming infrastructure.

## Requirements

### Requirement 1

**User Story:** As a system architect, I want the domain-seeder to aggregate candidate domains from multiple external sources prioritized by popularity, and generate initial preGenerated content, so that we discover high-value domains first and can present useful SEO-friendly information immediately while deferring live enrichment to the scheduler.

#### Acceptance Criteria

1. WHEN the service runs THEN it SHALL discover candidates using delta/registration methods in priority order: CZDS/registry zone DIFFs (creations only) → Differential snapshots of Tranco/Radar/Umbrella (new_today − seen_all_time) → RDAP incremental since last event → Passive DNS/FDNS since last timestamp → Long‑tail Common Crawl beyond rank cursor
2. WHEN processing each source THEN the system SHALL use per‑source cursors (date, offset, hash) and stream pages/chunks to handle large datasets efficiently
3. WHEN fetching from sources THEN the system SHALL process only deltas since the last successful cursor; for ranked sources process by native rank descending starting at the saved rank cursor
4. IF a source is unavailable or fails THEN the system SHALL continue with remaining sources, persist the last stable cursor, and log the failure with appropriate metrics

### Requirement 2

**User Story:** As a data engineer, I want all candidate domains canonicalized to eTLD+1 format and deduplicated, so that we maintain consistent domain representation and eliminate processing overhead from duplicates.

#### Acceptance Criteria

1. WHEN processing candidate domains THEN the system SHALL normalize each domain to eTLD+1 using the Public Suffix List (PSL)
2. WHEN normalizing domains THEN the system SHALL convert to lowercase and punycode format
3. WHEN processing domains THEN the system SHALL drop IP addresses and special TLDs that are not valid domains
4. WHEN deduplicating THEN the system SHALL use in-memory bloom filter or LFU cache to eliminate duplicates within processing batches

### Requirement 3

**User Story:** As a database administrator, I want the system to check against our existing database/indices and only keep domains that are missing, so that we avoid redundant processing and maintain system efficiency.

#### Acceptance Criteria

1. WHEN checking domain existence THEN the system SHALL batch query our Scylla/Maria/Manticore databases using shared database clients
2. WHEN performing existence checks THEN the system SHALL use batch sizes of up to 5000 domains per query (DB_CHECK_BATCH)
3. WHEN a domain exists in our database THEN the system SHALL exclude it from the enqueue list
4. IF database check fails THEN the system SHALL use fallback Bloom filter cache with false positive rate of 0.01 (BLOOM_FP_RATE)
5. WHEN caching recent positives THEN the system SHALL cache them in Redis for 24 hours to reduce DB load

### Requirement 4

**User Story:** As a system operator, I want strict backpressure controls and throttled additions, so that the domain-seeder doesn't overload downstream infrastructure while maintaining discovery throughput.

#### Acceptance Criteria

1. WHEN enqueuing domains THEN the system SHALL respect ENQUEUE_BATCH size limit (default 1000)
2. WHEN enqueuing domains THEN the system SHALL wait ENQUEUE_INTERVAL_MS (default 1000ms) between batches
3. WHEN daily limit is reached THEN the system SHALL stop enqueuing until the next day (MAX_NEW_PER_DAY default 500,000)
4. IF queue depth exceeds NEW_QUEUE_MAX_DEPTH (default 200,000) THEN the system SHALL skip enqueuing until queue drains
5. WHEN rate limiting is active THEN the system SHALL use Redis token bucket implementation with jitter sleeps to avoid bursts
6. WHEN checking idempotency THEN the system SHALL use Redis SETNX for "discovered:{domain}" and maintain a Scylla "seen_all_time" table to prevent re‑enqueuing across restarts
7. WHEN using queues THEN the system SHALL prefer Redis Streams with consumer groups and ACKs to achieve at-least-once delivery and avoid lost messages

### Requirement 5

**User Story:** As a downstream service consumer, I want discovered domains enqueued in Redis with lightweight provenance and initial content, so that the scheduler can consume and assign crawl jobs and we can display basic SEO-friendly info immediately.

#### Acceptance Criteria

1. WHEN enqueuing new domains THEN the system SHALL add them to Redis key "stream:new:domains" as a Redis Stream with consumer groups and ACKs, ensuring at‑least‑once delivery
2. WHEN enqueuing a domain THEN the message SHALL include {domain, firstSource, seenAt, preGenerated: true, content: {summary, category:{primary,secondary}, tags:[...]}} metadata
3. WHEN enqueuing top-tier domains THEN the system SHALL optionally use "new:domains:high" stream for prioritization
4. WHEN persisting provenance THEN the system SHALL store lightweight data: {domain, firstSeenAt, sources[], discoveryStrategy, preGeneratedContent} in DB or dedicated "domain_discovery" table
5. WHEN writing batches THEN the system SHALL write an append-only discovery log for crash recovery

### Requirement 6

**User Story:** As a system architect, I want prioritization policy that processes sources by popularity tiers without computing ranking, so that we seed high-value domains first while ensuring breadth across TLDs.

#### Acceptance Criteria

1. WHEN processing sources THEN the system SHALL follow source order: CZDS zone DIFFs (creations) → Differential snapshots of Tranco/Radar/Umbrella → RDAP incremental → Passive DNS/FDNS → Common Crawl long‑tail
2. WHEN processing within each source THEN the system SHALL handle domains by native rank or magnitude descending
3. WHEN enqueuing THEN the system SHALL process in tiers: new registrations first, then new‑to‑list differentials, then rising long‑tail; within tiers, honor top‑10k → 100k → 1M order where applicable
4. WHEN maintaining breadth THEN the system SHALL enforce per‑TLD quotas per cycle and rotate TLD shards to ensure coverage across TLDs

### Requirement 7

**User Story:** As a DevOps engineer, I want configurable scheduling and cadence for different source types, so that I can optimize resource usage and maintain appropriate data freshness.

#### Acceptance Criteria

1. WHEN configured for top sources THEN the system SHALL pull Tranco/Radar/Umbrella daily
2. WHEN configured for zone files THEN the system SHALL ingest CZDS zone diffs as provided (daily/weekly) to catch newly registered domains
3. WHEN configured for long-tail sources THEN the system SHALL sweep Common Crawl/Sonar weekly/monthly
4. WHEN reconciling THEN the system SHALL periodically re-check "discovered but not yet in DB" for N days to avoid re-adding

### Requirement 8

**User Story:** As a system monitor, I want comprehensive metrics and health endpoints, so that I can track discovery performance and detect issues proactively.

#### Acceptance Criteria

1. WHEN processing domains THEN the system SHALL expose Prometheus metrics: candidates_fetched_total{source}, candidates_after_normalize, known_in_db_total, new_discovered_total
2. WHEN generating preGenerated content THEN the system SHALL track content_generated_total{mode="self"}, content_validation_failures_total, seo_summary_length_hist; and record generator_version
3. WHEN enqueuing domains THEN the system SHALL track enqueue_attempts_total, enqueue_success_total, rate_limited_total
4. WHEN operating THEN the system SHALL monitor queue_depth, db_check_latency_ms, source_staleness_seconds
5. WHEN alerting THEN the system SHALL trigger alerts for: zero new discovered for 24h, content generation failures, DB check errors spike, queue depth > threshold for 1h

### Requirement 9

**User Story:** As a compliance officer, I want the system to respect Terms of Service and handle credentials securely, so that we maintain legal compliance and data security.

#### Acceptance Criteria

1. WHEN accessing external sources THEN the system SHALL respect ToS/licensing requirements for each source
2. WHEN using CZDS THEN the system SHALL load credentials via environment variables only
3. WHEN logging THEN the system SHALL never include secrets in logs
4. WHEN processing data THEN the system SHALL handle no PII processing and keep provenance data minimal
5. WHEN validating inputs THEN the system SHALL enforce strict schema validation (PSL, IDN/UTF-8 checks, ISO codes) and quarantine malformed entries for review

### Requirement 10

**User Story:** As a system administrator, I want CLI commands and API endpoints for manual operations, so that I can trigger runs, check status, and perform backfill operations.

#### Acceptance Criteria

1. WHEN running "pnpm seeder:run" THEN the system SHALL execute one full cycle respecting quotas
2. WHEN running "pnpm seeder:top" THEN the system SHALL ingest top sources only (Tranco/Radar) for quick seeding
3. WHEN running "pnpm seeder:status" THEN the system SHALL print discovery counts, queue depth, source freshness
4. WHEN running "pnpm seeder:backfill" THEN the system SHALL process zone/long-tail sources within MAX_NEW_PER_DAY budget
5. WHEN accessing HTTP endpoints THEN the system SHALL expose /health and /trigger endpoints

### Requirement 11

**User Story:** As a quality assurance engineer, I want comprehensive testing coverage including load testing, so that I can ensure system reliability and performance under high-volume scenarios.

#### Acceptance Criteria

1. WHEN testing normalization THEN unit tests SHALL verify PSL mapping, normalization, dedupe, batchHas stub, rate limiter
2. WHEN testing integration THEN tests SHALL mock sources and ensure only unknown domains enqueued while respecting quotas/backpressure
3. WHEN testing with golden datasets THEN tests SHALL produce fixed enqueued sets from fixed inputs and DB state
4. WHEN load testing THEN tests SHALL simulate 10M candidates and verify throughput and limits are maintained
5. WHEN testing reliability THEN tests SHALL verify exactly-once semantics with Redis Streams consumer ACKs and resume from checkpoints

### Requirement 12

**User Story:** As a system architect, I want the service to integrate properly with existing infrastructure, so that it fits seamlessly into our domain analysis pipeline.

#### Acceptance Criteria

1. WHEN outputting to queue THEN the system SHALL use Redis key "new:domains" (STREAM) with message format {domain, firstSource, seenAt, preGenerated, content}
2. WHEN integrating with scheduler THEN the scheduler SHALL consume from the stream using consumer groups and ACKs, and assign crawl jobs; later it SHALL trigger live content regeneration (preGenerated=false) when crawl data is ready
3. WHEN writing to database THEN DB writes SHALL occur downstream after successful crawl/ingest
4. WHEN using shared components THEN the system SHALL reuse shared database clients and follow repo conventions (TS, ESM, vitest, shared Config/Logger)
5. WHEN resuming after crash THEN the system SHALL resume using saved source cursors/checkpoints per connector
