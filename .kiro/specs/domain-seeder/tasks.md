# Implementation Plan

- [x] 1. Set up project structure and core interfaces

  - Create services/domain-seeder directory with package.json, tsconfig.json, vitest.config.ts, Dockerfile
  - Define core interfaces: SourceConnector, DiscoveryEngine, DomainNormalizer, ReliabilityManager
  - Set up shared imports from @shared package for DatabaseManager, Logger, Config, JobQueue
  - _Requirements: 12.1, 12.2_

- [x] 1.1 Integrate with existing shared services

  - Set up DatabaseManager integration for Scylla, Maria, Manticore, and Redis connections
  - Configure shared Logger integration with service-specific logging context
  - Integrate with shared Config system and extend with domain-seeder specific settings
  - Set up JobQueue integration for enqueueing discovered domains
  - Write tests for shared service integration and error handling
  - _Requirements: 12.1, 12.2, 12.4_

- [x] 1.2 Create service-specific extensions to shared components

  - Extend shared MetricsCollector with domain-seeder specific metrics
  - Create domain-seeder specific health checks extending shared HealthChecker
  - Implement CachingService integration for domain existence caching
  - Add DataSyncService integration for Manticore search index updates
  - Write tests for extended functionality and compatibility
  - _Requirements: 8.1, 8.2, 12.1_

- [x] 2. Implement reliability and resilience foundation
- [x] 2.1 Create crash-safe logging system

  - Implement ReliableLogger class with Redis stream-based persistence
  - Add heartbeat mechanism and graceful shutdown handling
  - Create log recovery functionality for crashed instances
  - Write unit tests for logging reliability under failure scenarios
  - _Requirements: 4.5, 4.6_

- [x] 2.2 Implement checkpoint and recovery system

  - Create CrashSafeReliabilityManager with checkpoint creation and restoration
  - Implement data integrity verification using SHA256 hashes
  - Add idempotency checking with Redis SETNX operations
  - Write tests for checkpoint corruption detection and recovery
  - _Requirements: 4.1, 4.2, 4.5_

- [x] 2.3 Build Redis Streams processing with ACK support

  - Implement ReliableStreamProcessor with consumer groups and message acknowledgment
  - Add retry logic with exponential backoff and dead letter queue handling
  - Create pending message recovery for stuck/failed processing
  - Write integration tests for stream reliability under various failure modes
  - _Requirements: 4.4, 4.5, 5.1_

- [x] 2.4 Update domain description schema and validation

  - Update docs/domain-description.schema.json with new requirements (320+ words, registration timestamps, ISO codes)
  - Modify shared/src/models/DomainDescription.ts to include preGenerated flag and enhanced metadata
  - Update shared/utils/DomainDescriptionValidator for new schema validation rules
  - Add validation for IDN indicators, social arrays, and category hierarchy changes
  - Write tests for schema compliance and backward compatibility
  - _Requirements: 5.4, 11.1_

- [x] 3. Create domain normalization and validation engine
- [x] 3.1 Implement PSL-based domain normalizer

  - Create PSLDomainNormalizer with eTLD+1 extraction and punycode conversion
  - Add automatic PSL updates with integrity verification
  - Implement IP address filtering and invalid TLD rejection
  - Write comprehensive tests for edge cases and internationalized domains
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 3.1.1 Build Public Suffix List management system

  - Create PSLManager with automatic PSL downloads and updates (7-day interval)
  - Add PSL integrity verification using checksums and digital signatures
  - Implement PSL version tracking and rollback capabilities
  - Create PSL parsing and caching for high-performance domain processing
  - Write tests for PSL update reliability and parsing accuracy
  - _Requirements: 2.1, 2.2_

- [x] 3.2 Add strict validation pipeline

  - Implement validation for each pipeline stage (source-fetch, normalization, existence-check, enqueue)
  - Create ValidationResult interface with errors, warnings, and sanitized data
  - Add data sanitization and error recovery mechanisms
  - Write property-based tests for validation correctness
  - _Requirements: 2.4, 11.1_

- [x] 4. Build intelligent discovery engine with multiple strategies
- [x] 4.1 Create discovery strategy framework

  - Implement IntelligentDiscoveryEngine with pluggable strategy processors
  - Create base StrategyProcessor interface and discovery result structures
  - Add strategy confidence scoring and discovery reason tracking
  - Write unit tests for strategy coordination and result aggregation
  - _Requirements: 1.1, 6.1, 6.2_

- [x] 4.2 Implement differential analysis strategy

  - Create DifferentialAnalysisProcessor for detecting new entries in rankings
  - Add snapshot storage and comparison logic using Redis/ScyllaDB
  - Implement historical snapshot management with configurable retention
  - Write tests with mock ranking data to verify new domain detection
  - _Requirements: 1.1, 6.1, 6.3_

- [x] 4.3 Build zone file processing strategy

  - Create ZoneFileProcessor for newly registered domain detection
  - Add CZDS zone file parsing with registration date extraction
  - Implement 48-hour cutoff filtering for recent registrations
  - Write tests with sample zone files and registration date scenarios
  - _Requirements: 1.1, 6.2, 7.2_

- [x] 4.4 Implement long-tail exploration strategy

  - Create LongTailProcessor for domains beyond top-1M rankings
  - Add rank threshold filtering (>1M) and confidence scoring
  - Implement Common Crawl and FDNS data processing for long-tail discovery
  - Write tests to verify long-tail domain identification and filtering
  - _Requirements: 1.1, 6.3, 7.3_

- [x] 4.5 Build temporal analysis strategy

  - Create TemporalAnalysisProcessor for rising domain detection
  - Add ranking history tracking and velocity calculation
  - Implement 50%+ improvement detection over configurable time windows
  - Write tests with synthetic ranking progression data
  - _Requirements: 1.1, 6.4, 7.4_

- [x] 5. Create source connectors with strategy support
- [x] 5.1 Implement Tranco connector with differential support

  - Create TrancoConnector with CSV streaming and strategy-aware fetching
  - Add support for full list (differential) vs long-tail (>1M rank) modes
  - Implement rate limiting and error handling with retry logic
  - Write integration tests with mock Tranco API responses
  - _Requirements: 1.1, 1.2, 9.1_

- [x] 5.2 Build Cloudflare Radar connector

  - Create RadarConnector with API authentication and pagination
  - Add differential analysis support and category metadata extraction
  - Implement temporal analysis with ranking history tracking
  - Write tests with mock Radar API responses and rate limiting scenarios
  - _Requirements: 1.1, 1.2, 9.1_

- [x] 5.3 Implement CZDS zone file connector

  - Create CZDSConnector with authentication and zone file download
  - Add zone file parsing with registration date extraction for zone-new strategy
  - Implement incremental processing of zone file diffs
  - Write tests with sample zone files and authentication scenarios
  - _Requirements: 1.1, 1.2, 9.2, 9.3_

- [x] 5.4 Build Common Crawl connector for long-tail discovery

  - Create CommonCrawlConnector with host frequency API integration
  - Add long-tail processing starting from rank 1M+ with streaming support
  - Implement monthly rollup processing and frequency-based ranking
  - Write tests with mock Common Crawl index responses
  - _Requirements: 1.1, 1.2, 7.3_

- [x] 5.5 Build Cisco Umbrella connector

  - Create UmbrellaConnector with API authentication and rate limiting
  - Add support for Umbrella 1M list processing with differential analysis
  - Implement ToS compliance checking and usage monitoring
  - Write tests with mock Umbrella API responses and authentication scenarios
  - _Requirements: 1.1, 1.2, 9.1_

- [x] 5.6 Create PIR .org registry connector

  - Create PIRConnector with registry API integration and authentication
  - Add .org domain processing with registration metadata extraction
  - Implement zone file processing for newly registered .org domains
  - Write tests with mock PIR registry responses and zone data
  - _Requirements: 1.1, 1.2, 7.2_

- [x] 5.7 Create Rapid7 FDNS/Sonar connector

  - Create SonarConnector with API integration and data streaming
  - Add long-tail domain discovery from DNS resolution data
  - Implement monthly processing cadence with backfill support
  - Write tests with mock FDNS data and API responses
  - _Requirements: 1.1, 1.2, 7.3_

- [x] 6. Implement database existence checking with batching
- [x] 6.1 Implement individual database repository classes

  - Create ScyllaDomainRepository with batch domain existence checking
  - Implement MariaDomainRepository with connection pooling and error handling
  - Build ManticoreDomainRepository with search index integration
  - Add database-specific optimization and query batching strategies
  - Write unit tests for each repository with mock database responses
  - _Requirements: 3.1, 3.2_

- [x] 6.2 Create composite domain repository

  - Implement CompositeDomainRepository with multi-database querying
  - Add batch existence checking across Scylla, Maria, and Manticore
  - Create bloom filter fallback for performance and resilience
  - Write tests for database failover scenarios and consistency
  - _Requirements: 3.1, 3.2, 3.4_

- [x] 6.3 Build bloom filter caching system

  - Implement BloomFilter class with configurable false positive rate (0.01)
  - Add Redis-backed bloom filter persistence and recovery
  - Create bloom filter warming strategies from database snapshots
  - Implement automatic bloom filter rebuilding and maintenance
  - Write tests for bloom filter accuracy and performance characteristics
  - _Requirements: 3.4, 8.3_

- [x] 6.4 Create snapshot storage system

  - Implement SnapshotStore for historical ranking data storage
  - Add snapshot compression and efficient storage in Redis/ScyllaDB
  - Create snapshot retrieval and comparison utilities for differential analysis
  - Implement snapshot cleanup and retention policies (30 days default)
  - Write tests for snapshot integrity and retrieval performance
  - _Requirements: 6.1, 6.3, 6.4_

- [x] 6.5 Add Redis caching layer

  - Implement 24-hour TTL caching for positive existence results
  - Add cache warming and invalidation strategies
  - Create cache hit/miss metrics and monitoring
  - Write tests for cache consistency and performance under load
  - _Requirements: 3.5, 8.3_

- [x] 6.6 Refactor domain existence checks to use Manticore Search as primary source

  - Eliminate Redis cache layer for domain existence and use Manticore Search as single source of truth
  - Leverage existing domain index for fast batch lookups (10K-100K domains per query)
  - Reduce memory usage from 5-10GB Redis cache to <100MB operational Redis
  - Implement shared lookup logic for both user searches and internal existence checks
  - Create scalable architecture supporting 100M+ domains without memory constraints
  - _Requirements: 3.1, 3.2, 3.4, 8.3_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.1 Create OptimizedDomainExistenceService

  - Implement service coordinating between Manticore Search and ScyllaDB
  - Add batch processing logic for 10K-100K domains per operation
  - Create error recovery and fallback scenarios
  - Implement connection pooling and query optimization
  - Write comprehensive tests for batch operations and error handling
  - _Requirements: 3.1, 3.2_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.2 Enhance ManticoreDomainIndex for existence checking

  - Optimize Manticore queries for domain existence using IN operator
  - Implement efficient batch operations with proper error handling
  - Add connection management and query result caching at client level
  - Create performance monitoring and query optimization
  - Write tests for large batch operations and concurrent access
  - _Requirements: 3.1, 3.4_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.3 Refactor Redis to operational-only usage

  - Remove domain existence caching from Redis
  - Implement deduplication and rate limiting only
  - Add recent negative results caching (short TTL, small memory footprint)
  - Create processing state management for concurrent operations
  - Write tests for operational efficiency and memory usage
  - _Requirements: 4.1, 4.2, 8.3_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.4 Update CompositeDomainRepository architecture

  - Remove Redis cache dependency for existence checks
  - Implement Manticore-first lookup with ScyllaDB fallback
  - Add intelligent error handling and automatic failover
  - Create performance monitoring and health checks
  - Write integration tests for repository coordination
  - _Requirements: 3.1, 3.2, 3.4_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.5 Optimize discovery pipeline for batch processing

  - Update all discovery engines to use batch existence checking
  - Implement proper backpressure handling for large batches
  - Add performance monitoring and batch size optimization
  - Create error handling and retry logic for batch operations
  - Write load tests for high-throughput domain processing
  - _Requirements: 7.1, 7.4, 8.1_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 6.6.6 Implement data consistency and synchronization

  - Create ScyllaDB to Manticore synchronization mechanisms
  - Add consistency monitoring and automatic repair
  - Implement conflict resolution with ScyllaDB as authoritative source
  - Create index rebuilding capabilities from ScyllaDB data
  - Write tests for data consistency and recovery scenarios
  - _Requirements: 3.1, 3.2, 4.1_
  - _Reference: [Domain Existence Optimization Architecture](../../../docs/domain-existence-optimization-architecture.md)_

- [x] 7. Build rate-limited enqueuer with backpressure
- [x] 7.1 Implement token bucket rate limiter

  - Create TokenBucket class with Redis-backed state and jitter
  - Add configurable refill rates and burst capacity
  - Implement backpressure detection and queue depth monitoring
  - Create distributed rate limiting across multiple service instances
  - Write tests for rate limiting accuracy and burst handling
  - _Requirements: 4.1, 4.2, 4.4_

- [x] 7.1.1 Build comprehensive backpressure system

  - Implement BackpressureController with multiple pressure indicators
  - Add queue depth monitoring, database latency tracking, and memory usage checks
  - Create adaptive rate limiting based on system performance metrics
  - Implement circuit breaker patterns for external service failures
  - Write tests for backpressure response and system stability
  - _Requirements: 4.4, 4.5_

- [x] 7.2 Create reliable domain enqueuer

  - Implement RateLimitedDomainEnqueuer with Redis Streams integration
  - Add idempotency checking and duplicate prevention
  - Create tiered queuing (high priority vs normal) with proper ACK handling
  - Write tests for enqueue reliability and backpressure scenarios
  - _Requirements: 4.3, 4.5, 5.1, 5.2_

- [x] 7.3 Add provenance tracking

  - Implement lightweight metadata storage for discovery audit trails
  - Add strategy tracking and confidence scoring in provenance data
  - Create discovery statistics aggregation by source and strategy
  - Write tests for provenance data integrity and query performance
  - _Requirements: 5.4, 6.1, 6.2_

- [-] 7.4 Implement domain description generation system
- [x] 7.4.1 Create domain content generation services

  - Implement CategoryTagger service with classifyCategory(), extractTags(), normalizeTags() methods
  - Create DomainSummaryService with generateBaseline() and generateFromContent() methods
  - Add support for both preGenerated (heuristic) and live analysis modes
  - Write unit tests for category classification and tag extraction accuracy
  - _Requirements: 1.1, 5.4_

- [x] 7.4.2 Build preGenerated content generation for seeder

  - Create preGenerated mode content generator using domain tokens and TLD heuristics
  - Implement 320+ word description generation with SEO optimization and keyword integration
  - Add category classification based on domain tokens and known brand cues
  - Generate 5-12 relevant tags from domain analysis and taxonomy synonyms
  - Write tests with various domain types to ensure quality and length requirements
  - _Requirements: 1.1, 5.4_

- [x] 7.4.3 Implement live content analysis for scheduler integration

  - Create live analysis mode with homepage fetching and content extraction
  - Add readability-based main content extraction and language detection
  - Implement keyword/NER analysis for offerings, audience, and geographic detection
  - Generate 320+ word descriptions with primary terms, benefits, and trust signals
  - Write integration tests with real website content and mock responses
  - _Requirements: 5.4_

- [x] 7.4.4 Add flexible generative AI integration for enhanced descriptions

  - Create AIProviderManager with support for multiple providers (OpenAI, Claude, Gemini, OpenRouter)
  - Implement flexible API key management with multiple keys per provider for load balancing
  - Add proxy IP support for AI API requests with rotation and failover
  - Create provider-specific prompt templates and response parsing
  - Implement AI-powered content enhancement with 320+ word requirement
  - Add intelligent fallback chain: primary provider → secondary provider → rule-based generation
  - Write tests for multi-provider reliability and content quality validation
  - _Requirements: 5.4_

- [x] ******* Build AI provider abstraction layer

  - Create AIProvider interface with standardized methods for all providers
  - Implement OpenAIProvider, ClaudeProvider, GeminiProvider, OpenRouterProvider classes
  - Add provider-specific configuration and authentication handling
  - Create provider health checking and automatic failover logic
  - Write unit tests for each provider implementation
  - _Requirements: 5.4_

- [x] ******* Implement proxy and rate limiting for AI requests

  - Create ProxyManager with IP rotation and health checking
  - Add per-provider rate limiting with different limits for different API keys
  - Implement request queuing and batching for efficiency
  - Create proxy failover and blacklist management
  - Write tests for proxy reliability and rate limiting accuracy
  - _Requirements: 5.4, 9.1_

- [x] ******* Add AI provider configuration and key management

  - Implement secure multi-key storage for each AI provider
  - Add automatic key rotation and usage balancing
  - Create provider priority and fallback configuration
  - Implement cost tracking and budget management per provider/key
  - Write tests for key management security and rotation
  - _Requirements: 5.4, 9.2, 12.2.2_

- [x] 7.4.5 Create domain description validation and schema compliance

  - Update shared/utils/DomainDescriptionValidator for new schema requirements
  - Implement validation for 320+ word descriptions, category presence, and tag count ≥5
  - Add schema validation for registration timestamps, ISO codes, and IDN indicators
  - Create content quality checks for SEO guidelines and readability standards
  - Write comprehensive tests for validation accuracy and edge cases
  - _Requirements: 5.4, 11.1_

- [x] 7.4.6 Build CLI command for domain description generation

  - Create services/domain-seeder/src/commands/describe.ts CLI command
  - Add support for both preGenerated and live analysis modes via command flags
  - Implement progress reporting and validation result display
  - Create batch processing capability for multiple domains
  - Write tests for CLI functionality and error handling scenarios
  - _Requirements: 10.1, 10.2_

- [x] 7.5 Integrate domain description generation with discovery pipeline

  - Add automatic preGenerated content creation when domains are discovered
  - Implement content generation as part of the enqueue process
  - Create scheduler job integration for live content analysis updates
  - Add content versioning and update tracking for preGenerated vs live content
  - Write integration tests for end-to-end content generation pipeline
  - _Requirements: 1.1, 5.4, 12.1_

- [ ] 8. Create intelligent scheduling system
- [x] 8.1 Implement adaptive scheduler

  - Create SmartDiscoveryScheduler with strategy effectiveness tracking
  - Add cron-based scheduling for different source/strategy combinations
  - Implement adaptive strategy selection based on recent discovery rates
  - Write tests for scheduling logic and strategy optimization
  - _Requirements: 7.1, 7.4, 10.1_

- [x] 8.2 Add discovery effectiveness monitoring

  - Implement discovery rate tracking and confidence scoring
  - Add strategy performance metrics and effectiveness calculation
  - Create automated strategy tuning based on historical performance
  - Write tests for effectiveness calculation and strategy adaptation
  - _Requirements: 7.4, 8.1, 8.2_

- [x] 8.3 Create scheduler integration for live content generation

  - Implement scheduler job to upgrade preGenerated content to live analysis
  - Add job queuing for domains that need live content analysis
  - Create priority system for high-value domains requiring immediate live analysis
  - Implement content update tracking and versioning system
  - Write tests for scheduler integration and job processing reliability
  - _Requirements: 5.4, 7.1, 12.1_

- [-] 9. Implement comprehensive metrics and monitoring
- [x] 9.1 Create Prometheus metrics collector

  - Implement MetricsCollector with all required metrics (candidates_fetched_total, new_discovered_total, etc.)
  - Add strategy-specific metrics and confidence tracking
  - Create queue depth and processing latency monitoring
  - Write tests for metrics accuracy and performance impact
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 9.2 Add health check endpoints

  - Implement comprehensive health checks for databases, Redis, and sources
  - Add readiness and liveness probes for container deployment
  - Create dependency health monitoring and alerting
  - Write tests for health check reliability and failure detection
  - _Requirements: 8.4, 10.5_

- [x] 9.3 Build alerting system

  - Implement alerts for zero discoveries, DB errors, and queue depth issues
  - Add source staleness monitoring and effectiveness degradation alerts
  - Create automated recovery triggers for common failure scenarios
  - Write tests for alert accuracy and false positive prevention
  - _Requirements: 8.4_

- [ ] 10. Create CLI commands and HTTP API
- [x] 10.1 Implement CLI interface

  - Create CLI commands: seeder:run, seeder:top, seeder:status, seeder:backfill
  - Add command-line argument parsing and configuration validation
  - Implement progress reporting and interactive status display
  - Write tests for CLI functionality and error handling
  - _Requirements: 10.1, 10.2, 10.3, 10.4_

- [x] 10.2 Build HTTP API endpoints

  - Implement /health and /trigger endpoints with proper error handling
  - Add /metrics endpoint for Prometheus scraping
  - Create /status endpoint with detailed system information
  - Write integration tests for API functionality and security
  - _Requirements: 10.5_

- [x] 11. Add comprehensive testing and validation
- [x] 11.1 Create unit test suite

  - Write unit tests for PSL mapping, normalization, deduplication, and rate limiting
  - Add tests for discovery strategies with mock data and edge cases
  - Create property-based tests for validation and idempotency
  - Achieve 90%+ code coverage with meaningful test scenarios
  - _Requirements: 11.1, 11.2_

- [x] 11.2 Add reliability and chaos testing

  - Add network partition and database failure simulation
  - Implement Redis failover and stream processing reliability tests
  - Write tests for graceful degradation under various failure modes
  - _Requirements: 11.1, 11.4_

- [x] 11.3 Build comprehensive error handling system

  - Implement ErrorHandler with categorized error types and recovery strategies
  - Add automatic retry logic with exponential backoff for transient failures
  - Create error aggregation and reporting for operational visibility
  - Implement graceful degradation patterns for partial system failures
  - _Requirements: 4.5, 8.4_

- [x] 12. Finalize deployment and documentation
- [x] 12.1 Create Docker configuration

  - Build optimized Dockerfile with multi-stage build and security hardening
  - Add docker-compose.yml for local development with all dependencies
  - Create production deployment scripts and environment setup
  - Write deployment documentation and troubleshooting guide
  - _Requirements: 12.4_

- [x] 12.2 Add configuration management

  - Implement comprehensive environment variable configuration
  - Add configuration validation and default value handling
  - Create configuration documentation with examples and best practices
  - Write tests for configuration parsing and validation
  - _Requirements: 12.1, 12.2_

- [x] 12.2.1 Build comprehensive configuration system

  - Create SeederConfig interface with all required configuration options
  - Implement configuration loading from environment variables with type validation
  - Add configuration schema validation and error reporting
  - Create configuration hot-reloading for non-critical settings
  - Write tests for configuration edge cases and validation scenarios
  - _Requirements: 12.1, 12.2_

- [x] 12.2.2 Add secrets and credential management

  - Implement secure credential loading for CZDS, APIs, and database connections
  - Add credential rotation support and expiration monitoring
  - Create credential validation and health checking
  - Implement secure credential storage and access patterns
  - _Requirements: 9.2, 9.3, 9.4_

- [x] 12.2.3 Add environment-specific configuration profiles

  - Create configuration profiles for development, staging, and production environments
  - Implement configuration inheritance and override mechanisms
  - Add configuration validation specific to each environment
  - Create configuration migration and upgrade utilities
  - Write tests for configuration profile switching and validation
  - _Requirements: 12.1, 12.2_

- [x] 12.3 Create comprehensive logging and audit trail

  - Implement structured logging with correlation IDs across all components
  - Add audit logging for all domain discoveries and processing decisions
  - Create log aggregation and analysis tools for operational insights
  - Implement log retention policies and archival strategies
  - _Requirements: 4.5, 8.4_

- [x] 12.4 Create operational documentation
  - Write README with setup instructions and architecture overview
  - Add runbook for common operational tasks and troubleshooting
  - Create monitoring and alerting setup guide
  - Document recovery procedures for various failure scenarios
  - _Requirements: 12.4_
