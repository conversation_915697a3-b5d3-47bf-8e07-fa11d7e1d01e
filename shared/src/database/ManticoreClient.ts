import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/Logger';
import { config } from '../utils/Config';

/**
 * Manticore Search client for full-text search and faceted filtering
 */
class ManticoreClient
{
	private client: AxiosInstance;

	private logger = logger.getLogger('ManticoreClient');

	private baseUrl: string;

	constructor()
	{
		this.baseUrl = config.get('MANTICORE_URL', 'http://manticore:9308');
		this.client = axios.create({
			baseURL: this.baseUrl,
			timeout: 30000,
			headers: {
				'Content-Type': 'application/json',
			},
		});
	}

	/**
	 * Connect to Manticore Search
	 */
	async connect(): Promise<void>
	{
		try
		{
			this.logger.info('Connecting to Manticore Search...');
			await this.healthCheck();
			this.logger.info('Connected to Manticore Search successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to connect to Manticore Search:', error);
			throw error;
		}
	}

	/**
	 * Disconnect from Manticore Search
	 */
	async disconnect(): Promise<void>
	{
		this.logger.info('Manticore Search client disconnected');
	}

	/**
	 * Health check for Manticore Search
	 */
	async healthCheck(): Promise<boolean>
	{
		try
		{
			const response = await this.client.get('/');
			return response.status === 200;
		}
		catch (error)
		{
			this.logger.error('Manticore health check failed:', error);
			return false;
		}
	}

	/**
	 * Search domains with faceted filtering
	 */
	async searchDomains(params: {
		query?: string;
		filters?: Record<string, unknown>;
		sort?: string;
		limit?: number;
		offset?: number;
		facets?: string[];
	}): Promise<{
		results: Record<string, unknown>[];
		total: number;
		facets: Record<string, unknown>;
		took: number;
	}>
	{
		try
		{
			const searchQuery = this.buildSearchQuery(params);

			this.logger.debug('Executing Manticore search:', searchQuery);

			const response = await this.client.post('/search', searchQuery);

			return this.parseSearchResponse(response.data);
		}
		catch (error)
		{
			this.logger.error('Domain search failed:', error);
			throw error;
		}
	}

	/**
	 * Get top domains by category
	 */
	async getTopDomains(params: {
		category?: string;
		country?: string;
		limit?: number;
		offset?: number;
	}): Promise<{
		results: Record<string, unknown>[];
		total: number;
	}>
	{
		try
		{
			const searchQuery = {
				index: 'domains_index',
				query: { match_all: {} },
				filter: this.buildFilters({
					category: params.category,
					country: params.country,
				}),
				sort: [{ global_rank: { order: 'asc' } }],
				limit: params.limit || 50,
				offset: params.offset || 0,
			};

			const response = await this.client.post('/search', searchQuery);
			return this.parseSearchResponse(response.data);
		}
		catch (error)
		{
			this.logger.error('Top domains query failed:', error);
			throw error;
		}
	}

	/**
	 * Compare multiple domains
	 */
	async compareDomains(domains: string[]): Promise<{
		results: Record<string, unknown>[];
		total: number;
	}>
	{
		try
		{
			const searchQuery = {
				index: 'domains_index',
				query: {
					terms: { domain: domains },
				},
				sort: [{ overall_score: { order: 'desc' } }],
				limit: domains.length,
			};

			const response = await this.client.post('/search', searchQuery);
			return this.parseSearchResponse(response.data);
		}
		catch (error)
		{
			this.logger.error('Domain comparison failed:', error);
			throw error;
		}
	}

	/**
	 * Build search query for Manticore
	 */
	private buildSearchQuery(params: {
		query?: string;
		filters?: Record<string, unknown>;
		sort?: string;
		limit?: number;
		offset?: number;
		facets?: string[];
	}): Record<string, unknown>
	{
		const searchQuery: Record<string, unknown> = {
			index: 'domains_index',
			limit: params.limit || 20,
			offset: params.offset || 0,
		};

		// Build query
		if (params.query && params.query.trim())
		{
			searchQuery.query = {
				bool: {
					should: [
						{ match: { domain: { query: params.query, boost: 3.0 } } },
						{ match: { title: { query: params.query, boost: 2.0 } } },
						{ match: { description: { query: params.query, boost: 1.5 } } },
						{ match: { content: { query: params.query, boost: 1.0 } } },
					],
					minimum_should_match: 1,
				},
			};
		}
		else
		{
			searchQuery.query = { match_all: {} };
		}

		// Build filters
		if (params.filters)
		{
			searchQuery.filter = this.buildFilters(params.filters);
		}

		// Build sort
		if (params.sort)
		{
			searchQuery.sort = this.buildSort(params.sort);
		}
		else
		{
			// Default sort by overall score and global rank
			searchQuery.sort = [
				{ overall_score: { order: 'desc' } },
				{ global_rank: { order: 'asc' } },
			];
		}

		// Build facets
		if (params.facets && params.facets.length > 0)
		{
			searchQuery.aggs = {};
			params.facets.forEach((facet) =>
			{
				searchQuery.aggs[facet] = {
					terms: { field: facet, size: 20 },
				};
			});
		}
		else
		{
			// Default facets
			searchQuery.aggs = {
				category: { terms: { field: 'category', size: 20 } },
				country: { terms: { field: 'country', size: 20 } },
				technologies: { terms: { field: 'technologies', size: 20 } },
				ssl_grade: { terms: { field: 'ssl_grade', size: 10 } },
			};
		}

		return searchQuery;
	}

	/**
	 * Build filters for Manticore query
	 */
	private buildFilters(filters: Record<string, unknown>): Record<string, unknown>
	{
		const manticoreFilters: Record<string, unknown> = {};

		Object.entries(filters).forEach(([key, value]) =>
		{
			if (value !== undefined && value !== null && value !== '')
			{
				switch (key)
				{
					case 'category':
						manticoreFilters.category = value;
						break;
					case 'country':
						manticoreFilters.country = value;
						break;
					case 'technologies':
						if (Array.isArray(value))
						{
							manticoreFilters.technologies = { in: value };
						}
						else
						{
							manticoreFilters.technologies = value;
						}
						break;
					case 'ssl_grade':
						manticoreFilters.ssl_grade = value;
						break;
					case 'min_rank':
						manticoreFilters.global_rank = { gte: parseInt(String(value), 10) };
						break;
					case 'max_rank':
						manticoreFilters.global_rank = {
							...manticoreFilters.global_rank as Record<string, unknown>,
							lte: parseInt(String(value), 10),
						};
						break;
					case 'min_score':
						manticoreFilters.overall_score = { gte: parseFloat(String(value)) };
						break;
					case 'max_score':
						manticoreFilters.overall_score = {
							...manticoreFilters.overall_score as Record<string, unknown>,
							lte: parseFloat(String(value)),
						};
						break;
					default:
						manticoreFilters[key] = value;
				}
			}
		});

		return manticoreFilters;
	}

	/**
	 * Build sort configuration for Manticore query
	 */
	private buildSort(sort: string): Array<Record<string, unknown>>
	{
		const sortOptions: Array<Record<string, unknown>> = [];

		switch (sort)
		{
			case 'rank':
				sortOptions.push({ global_rank: { order: 'asc' } });
				break;
			case 'score':
				sortOptions.push({ overall_score: { order: 'desc' } });
				break;
			case 'performance':
				sortOptions.push({ performance_score: { order: 'desc' } });
				break;
			case 'security':
				sortOptions.push({ security_score: { order: 'desc' } });
				break;
			case 'seo':
				sortOptions.push({ seo_score: { order: 'desc' } });
				break;
			case 'domain':
				sortOptions.push({ domain: { order: 'asc' } });
				break;
			case 'traffic':
				sortOptions.push({ traffic_estimate: { order: 'desc' } });
				break;
			default:
				// Default sort
				sortOptions.push({ overall_score: { order: 'desc' } });
				sortOptions.push({ global_rank: { order: 'asc' } });
		}

		return sortOptions;
	}

	/**
	 * Parse Manticore search response
	 */
	private parseSearchResponse(response: Record<string, unknown>): {
		results: Record<string, unknown>[];
		total: number;
		facets: Record<string, unknown>;
		took: number;
	}
	{
		type HitSource = {
			domain: string;
			title?: string;
			description?: string;
			category?: string;
			country?: string;
			language?: string;
			technologies?: string[];
			registrar?: string;
			domain_age_days?: number;
			global_rank?: number;
			overall_score?: number;
			performance_score?: number;
			security_score?: number;
			seo_score?: number;
			technical_score?: number;
			backlink_score?: number;
			traffic_estimate?: number;
			ssl_grade?: string;
			mobile_friendly_score?: number;
			accessibility_score?: number;
			last_updated?: string;
		};
		type Res = {
			hits?: { hits?: Array<{ _source: HitSource; _score: number }>; total?: { value: number } };
			aggregations?: Record<string, unknown>;
			took?: number;
		};
		const res = response as Res;
		const results = res.hits?.hits?.map(hit => ({
			domain: hit._source.domain,
			title: hit._source.title,
			description: hit._source.description,
			category: hit._source.category,
			country: hit._source.country,
			language: hit._source.language,
			technologies: hit._source.technologies || [],
			registrar: hit._source.registrar,
			domainAge: hit._source.domain_age_days,
			globalRank: hit._source.global_rank,
			scores: {
				overall: hit._source.overall_score,
				performance: hit._source.performance_score,
				security: hit._source.security_score,
				seo: hit._source.seo_score,
				technical: hit._source.technical_score,
				backlink: hit._source.backlink_score,
			},
			trafficEstimate: hit._source.traffic_estimate,
			sslGrade: hit._source.ssl_grade,
			mobileFriendlyScore: hit._source.mobile_friendly_score,
			accessibilityScore: hit._source.accessibility_score,
			lastUpdated: hit._source.last_updated,
			_score: hit._score,
		})) || [];

		const facets: Record<string, unknown> = {};
		if (response.aggregations)
		{
			Object.entries(response.aggregations).forEach(([key, agg]) =>
			{
				const a = agg as { buckets?: Array<{ key: string; doc_count: number }> };
				if (a.buckets)
				{
					facets[key] = a.buckets.map(bucket => ({ value: bucket.key, count: bucket.doc_count }));
				}
			});
		}

		return {
			results,
			total: res.hits?.total?.value || 0,
			facets,
			took: res.took || 0,
		};
	}

	/**
	 * Insert or update document in index
	 */
	async upsertDocument(index: string, id: string, document: Record<string, unknown>): Promise<void>
	{
		try
		{
			await this.client.post(`/${index}/_doc/${id}`, document);
			this.logger.debug(`Document upserted in ${index}:`, id);
		}
		catch (error)
		{
			this.logger.error(`Failed to upsert document in ${index}:`, error);
			throw error;
		}
	}

	/**
	 * Delete document from index
	 */
	async deleteDocument(index: string, id: string): Promise<void>
	{
		try
		{
			await this.client.delete(`/${index}/_doc/${id}`);
			this.logger.debug(`Document deleted from ${index}:`, id);
		}
		catch (error)
		{
			this.logger.error(`Failed to delete document from ${index}:`, error);
			throw error;
		}
	}

	/**
	 * Create index with mapping
	 */
	async createIndex(index: string, mapping: Record<string, unknown>): Promise<void>
	{
		try
		{
			await this.client.put(`/${index}`, { mappings: mapping });
			this.logger.info(`Index created: ${index}`);
		}
		catch (error)
		{
			this.logger.error(`Failed to create index ${index}:`, error);
			throw error;
		}
	}

	/**
	 * Check if index exists
	 */
	async indexExists(index: string): Promise<boolean>
	{
		try
		{
			const response = await this.client.head(`/${index}`);
			return response.status === 200;
		}
		catch (error)
		{
			return false;
		}
	}
}

export { ManticoreClient };

export default ManticoreClient;
