user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
	worker_connections 1024;
	use epoll;
	multi_accept on;
}

http {
	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	# Logging format
	log_format main '$remote_addr - $remote_user [$time_local] "$request" '
					'$status $body_bytes_sent "$http_referer" '
					'"$http_user_agent" "$http_x_forwarded_for" '
					'rt=$request_time uct="$upstream_connect_time" '
					'uht="$upstream_header_time" urt="$upstream_response_time"';

	access_log /var/log/nginx/access.log main;

	# Performance optimizations
	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	server_tokens off;

	# Gzip compression
	gzip on;
	gzip_vary on;
	gzip_min_length 1024;
	gzip_proxied any;
	gzip_comp_level 6;
	gzip_types
		text/plain
		text/css
		text/xml
		text/javascript
		application/json
		application/javascript
		application/xml+rss
		application/atom+xml
		image/svg+xml;

	# Rate limiting
	limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
	limit_req_zone $binary_remote_addr zone=search:10m rate=5r/s;

	# Upstream servers
	upstream web_app {
		server web-app:3000 max_fails=3 fail_timeout=30s;
		keepalive 32;
	}

	# Security headers
	add_header X-Frame-Options DENY always;
	add_header X-Content-Type-Options nosniff always;
	add_header X-XSS-Protection "1; mode=block" always;
	add_header Referrer-Policy "strict-origin-when-cross-origin" always;
	add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;

	# Include additional configurations
	include /etc/nginx/conf.d/*.conf;
}
