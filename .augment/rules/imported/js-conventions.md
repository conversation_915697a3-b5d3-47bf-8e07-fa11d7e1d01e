---
type: "manual"
---

# Javascript Rules v3 - 08.08.2025 03:25

## General Coding Rules

- Follow naming conventions: camelCase for variables/functions and related file names, PascalCase for classes/components and related file names, and UPPER_SNAKE_CASE for constants
- Always use `import type {}` for type-only imports and place them before regular imports
- Use only pnpm commands instead of npm: `pnpm install`, `pnpm test`, `pnpm dlx` - reject all npm/yarn usage
- Always use declaration-then-export pattern: declare functions/classes/variables first, then export them in grouped export statements at file end
- When organizing exports, group all type exports together in a single `export type { ... }` statement and all value exports together in a single `export { ... }` statement, placing type exports before value exports, followed by any default export last, while removing any inline `export` keywords from individual declarations
- Always use `export default` for the main purpose of the file
- Use TypeScript exclusively - generate .ts/.tsx files and proper tsconfig.json setup
- Always use `type` to define the types, `interface` only when required to extend by other interfaces
- Interfaces must be named like NameInterface and types must be named NameType
- Always include a trailing comma after the last item in multiline arrays, objects, destructuring, and function parameters
- Skip unit testing for now - focus on completing the working production project

## Backend Rules

- Execute TypeScript with tsx command

## React Rules

- Use `useCallback`, `useMemo`, and cleanup functions in `useEffect`
- Avoid using array index as React list keys
