{"version": 3, "sources": ["../src/index.ts", "../src/anthropic-provider.ts", "../src/anthropic-messages-language-model.ts", "../src/anthropic-error.ts", "../src/anthropic-prepare-tools.ts", "../src/convert-to-anthropic-messages-prompt.ts", "../src/map-anthropic-stop-reason.ts", "../src/anthropic-tools.ts"], "sourcesContent": ["export type { AnthropicProviderOptions } from './anthropic-messages-language-model';\nexport { anthropic, createAnthropic } from './anthropic-provider';\nexport type {\n  AnthropicProvider,\n  AnthropicProviderSettings,\n} from './anthropic-provider';\n", "import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { AnthropicMessagesLanguageModel } from './anthropic-messages-language-model';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { anthropicTools } from './anthropic-tools';\n\nexport interface AnthropicProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `.languageModel()` instead.\n*/\n  chat(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `.languageModel()` instead.\n   */\n  messages(\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nAnthropic-specific computer use tool.\n   */\n  tools: typeof anthropicTools;\n}\n\nexport interface AnthropicProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.anthropic.com/v1`.\n   */\n  baseURL?: string;\n\n  /**\nAPI key that is being send using the `x-api-key` header.\nIt defaults to the `ANTHROPIC_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  generateId?: () => string;\n}\n\n/**\nCreate an Anthropic provider instance.\n */\nexport function createAnthropic(\n  options: AnthropicProviderSettings = {},\n): AnthropicProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'https://api.anthropic.com/v1';\n\n  const getHeaders = () => ({\n    'anthropic-version': '2023-06-01',\n    'x-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'ANTHROPIC_API_KEY',\n      description: 'Anthropic',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings = {},\n  ) =>\n    new AnthropicMessagesLanguageModel(modelId, settings, {\n      provider: 'anthropic.messages',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n      supportsImageUrls: true,\n    });\n\n  const provider = function (\n    modelId: AnthropicMessagesModelId,\n    settings?: AnthropicMessagesSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Anthropic model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  provider.tools = anthropicTools;\n\n  return provider;\n}\n\n/**\nDefault Anthropic provider instance.\n */\nexport const anthropic = createAnthropic();\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionToolCall,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  Resolvable,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { anthropicFailedResponseHandler } from './anthropic-error';\nimport {\n  AnthropicMessagesModelId,\n  AnthropicMessagesSettings,\n} from './anthropic-messages-settings';\nimport { prepareTools } from './anthropic-prepare-tools';\nimport { convertToAnthropicMessagesPrompt } from './convert-to-anthropic-messages-prompt';\nimport { mapAnthropicStopReason } from './map-anthropic-stop-reason';\n\ntype AnthropicMessagesConfig = {\n  provider: string;\n  baseURL: string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  supportsImageUrls: boolean;\n  fetch?: FetchFunction;\n  buildRequestUrl?: (baseURL: string, isStreaming: boolean) => string;\n  transformRequestBody?: (args: Record<string, any>) => Record<string, any>;\n};\n\nexport class AnthropicMessagesLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'tool';\n\n  readonly modelId: AnthropicMessagesModelId;\n  readonly settings: AnthropicMessagesSettings;\n\n  private readonly config: AnthropicMessagesConfig;\n\n  constructor(\n    modelId: AnthropicMessagesModelId,\n    settings: AnthropicMessagesSettings,\n    config: AnthropicMessagesConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  supportsUrl(url: URL): boolean {\n    return url.protocol === 'https:';\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get supportsImageUrls(): boolean {\n    return this.config.supportsImageUrls;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens = 4096, // 4096: max model output tokens TODO update default in v5\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata: providerOptions,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { prompt: messagesPrompt, betas: messagesBetas } =\n      convertToAnthropicMessagesPrompt({\n        prompt,\n        sendReasoning: this.settings.sendReasoning ?? true,\n        warnings,\n      });\n\n    const anthropicOptions = parseProviderOptions({\n      provider: 'anthropic',\n      providerOptions,\n      schema: anthropicProviderOptionsSchema,\n    });\n\n    const isThinking = anthropicOptions?.thinking?.type === 'enabled';\n    const thinkingBudget = anthropicOptions?.thinking?.budgetTokens;\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_k: topK,\n      top_p: topP,\n      stop_sequences: stopSequences,\n\n      // provider specific settings:\n      ...(isThinking && {\n        thinking: { type: 'enabled', budget_tokens: thinkingBudget },\n      }),\n\n      // prompt:\n      system: messagesPrompt.system,\n      messages: messagesPrompt.messages,\n    };\n\n    if (isThinking) {\n      if (thinkingBudget == null) {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'thinking requires a budget',\n        });\n      }\n\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'temperature',\n          details: 'temperature is not supported when thinking is enabled',\n        });\n      }\n\n      if (topK != null) {\n        baseArgs.top_k = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topK',\n          details: 'topK is not supported when thinking is enabled',\n        });\n      }\n\n      if (topP != null) {\n        baseArgs.top_p = undefined;\n        warnings.push({\n          type: 'unsupported-setting',\n          setting: 'topP',\n          details: 'topP is not supported when thinking is enabled',\n        });\n      }\n\n      // adjust max tokens to account for thinking:\n      baseArgs.max_tokens = maxTokens + thinkingBudget;\n    }\n\n    switch (type) {\n      case 'regular': {\n        const {\n          tools,\n          tool_choice,\n          toolWarnings,\n          betas: toolsBetas,\n        } = prepareTools(mode);\n\n        return {\n          args: { ...baseArgs, tools, tool_choice },\n          warnings: [...warnings, ...toolWarnings],\n          betas: new Set([...messagesBetas, ...toolsBetas]),\n        };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'json-mode object generation',\n        });\n      }\n\n      case 'object-tool': {\n        const { name, description, parameters } = mode.tool;\n\n        return {\n          args: {\n            ...baseArgs,\n            tools: [{ name, description, input_schema: parameters }],\n            tool_choice: { type: 'tool', name },\n          },\n          warnings,\n          betas: messagesBetas,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  private async getHeaders({\n    betas,\n    headers,\n  }: {\n    betas: Set<string>;\n    headers: Record<string, string | undefined> | undefined;\n  }) {\n    return combineHeaders(\n      await resolve(this.config.headers),\n      betas.size > 0 ? { 'anthropic-beta': Array.from(betas).join(',') } : {},\n      headers,\n    );\n  }\n\n  private buildRequestUrl(isStreaming: boolean): string {\n    return (\n      this.config.buildRequestUrl?.(this.config.baseURL, isStreaming) ??\n      `${this.config.baseURL}/messages`\n    );\n  }\n\n  private transformRequestBody(args: Record<string, any>): Record<string, any> {\n    return this.config.transformRequestBody?.(args) ?? args;\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings, betas } = await this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: this.buildRequestUrl(false),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(args),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        anthropicMessagesResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    // extract text\n    let text = '';\n    for (const content of response.content) {\n      if (content.type === 'text') {\n        text += content.text;\n      }\n    }\n\n    // extract tool calls\n    let toolCalls: LanguageModelV1FunctionToolCall[] | undefined = undefined;\n    if (response.content.some(content => content.type === 'tool_use')) {\n      toolCalls = [];\n      for (const content of response.content) {\n        if (content.type === 'tool_use') {\n          toolCalls.push({\n            toolCallType: 'function',\n            toolCallId: content.id,\n            toolName: content.name,\n            args: JSON.stringify(content.input),\n          });\n        }\n      }\n    }\n\n    const reasoning = response.content\n      .filter(\n        content =>\n          content.type === 'redacted_thinking' || content.type === 'thinking',\n      )\n      .map(content =>\n        content.type === 'thinking'\n          ? {\n              type: 'text' as const,\n              text: content.thinking,\n              signature: content.signature,\n            }\n          : {\n              type: 'redacted' as const,\n              data: content.data,\n            },\n      );\n\n    return {\n      text,\n      reasoning: reasoning.length > 0 ? reasoning : undefined,\n      toolCalls,\n      finishReason: mapAnthropicStopReason(response.stop_reason),\n      usage: {\n        promptTokens: response.usage.input_tokens,\n        completionTokens: response.usage.output_tokens,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: {\n        headers: responseHeaders,\n        body: rawResponse,\n      },\n      response: {\n        id: response.id ?? undefined,\n        modelId: response.model ?? undefined,\n      },\n      warnings,\n      providerMetadata: {\n        anthropic: {\n          cacheCreationInputTokens:\n            response.usage.cache_creation_input_tokens ?? null,\n          cacheReadInputTokens: response.usage.cache_read_input_tokens ?? null,\n        },\n      },\n      request: { body: JSON.stringify(args) },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings, betas } = await this.getArgs(options);\n    const body = { ...args, stream: true };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: this.buildRequestUrl(true),\n      headers: await this.getHeaders({ betas, headers: options.headers }),\n      body: this.transformRequestBody(body),\n      failedResponseHandler: anthropicFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(\n        anthropicMessagesChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    const usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n\n    const toolCallContentBlocks: Record<\n      number,\n      {\n        toolCallId: string;\n        toolName: string;\n        jsonText: string;\n      }\n    > = {};\n\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined =\n      undefined;\n\n    let blockType:\n      | 'text'\n      | 'thinking'\n      | 'tool_use'\n      | 'redacted_thinking'\n      | undefined = undefined;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof anthropicMessagesChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            switch (value.type) {\n              case 'ping': {\n                return; // ignored\n              }\n\n              case 'content_block_start': {\n                const contentBlockType = value.content_block.type;\n\n                blockType = contentBlockType;\n\n                switch (contentBlockType) {\n                  case 'text':\n                  case 'thinking': {\n                    return; // ignored\n                  }\n\n                  case 'redacted_thinking': {\n                    controller.enqueue({\n                      type: 'redacted-reasoning',\n                      data: value.content_block.data,\n                    });\n                    return;\n                  }\n\n                  case 'tool_use': {\n                    toolCallContentBlocks[value.index] = {\n                      toolCallId: value.content_block.id,\n                      toolName: value.content_block.name,\n                      jsonText: '',\n                    };\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = contentBlockType;\n                    throw new Error(\n                      `Unsupported content block type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'content_block_stop': {\n                // when finishing a tool call block, send the full tool call:\n                if (toolCallContentBlocks[value.index] != null) {\n                  const contentBlock = toolCallContentBlocks[value.index];\n\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: contentBlock.toolCallId,\n                    toolName: contentBlock.toolName,\n                    args: contentBlock.jsonText,\n                  });\n\n                  delete toolCallContentBlocks[value.index];\n                }\n\n                blockType = undefined; // reset block type\n\n                return;\n              }\n\n              case 'content_block_delta': {\n                const deltaType = value.delta.type;\n                switch (deltaType) {\n                  case 'text_delta': {\n                    controller.enqueue({\n                      type: 'text-delta',\n                      textDelta: value.delta.text,\n                    });\n\n                    return;\n                  }\n\n                  case 'thinking_delta': {\n                    controller.enqueue({\n                      type: 'reasoning',\n                      textDelta: value.delta.thinking,\n                    });\n\n                    return;\n                  }\n\n                  case 'signature_delta': {\n                    // signature are only supported on thinking blocks:\n                    if (blockType === 'thinking') {\n                      controller.enqueue({\n                        type: 'reasoning-signature',\n                        signature: value.delta.signature,\n                      });\n                    }\n\n                    return;\n                  }\n\n                  case 'input_json_delta': {\n                    const contentBlock = toolCallContentBlocks[value.index];\n\n                    controller.enqueue({\n                      type: 'tool-call-delta',\n                      toolCallType: 'function',\n                      toolCallId: contentBlock.toolCallId,\n                      toolName: contentBlock.toolName,\n                      argsTextDelta: value.delta.partial_json,\n                    });\n\n                    contentBlock.jsonText += value.delta.partial_json;\n\n                    return;\n                  }\n\n                  default: {\n                    const _exhaustiveCheck: never = deltaType;\n                    throw new Error(\n                      `Unsupported delta type: ${_exhaustiveCheck}`,\n                    );\n                  }\n                }\n              }\n\n              case 'message_start': {\n                usage.promptTokens = value.message.usage.input_tokens;\n                usage.completionTokens = value.message.usage.output_tokens;\n\n                providerMetadata = {\n                  anthropic: {\n                    cacheCreationInputTokens:\n                      value.message.usage.cache_creation_input_tokens ?? null,\n                    cacheReadInputTokens:\n                      value.message.usage.cache_read_input_tokens ?? null,\n                  },\n                };\n\n                controller.enqueue({\n                  type: 'response-metadata',\n                  id: value.message.id ?? undefined,\n                  modelId: value.message.model ?? undefined,\n                });\n\n                return;\n              }\n\n              case 'message_delta': {\n                usage.completionTokens = value.usage.output_tokens;\n                finishReason = mapAnthropicStopReason(value.delta.stop_reason);\n                return;\n              }\n\n              case 'message_stop': {\n                controller.enqueue({\n                  type: 'finish',\n                  finishReason,\n                  usage,\n                  providerMetadata,\n                });\n                return;\n              }\n\n              case 'error': {\n                controller.enqueue({ type: 'error', error: value.error });\n                return;\n              }\n\n              default: {\n                const _exhaustiveCheck: never = value;\n                throw new Error(`Unsupported chunk type: ${_exhaustiveCheck}`);\n              }\n            }\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) },\n    };\n  }\n}\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesResponseSchema = z.object({\n  type: z.literal('message'),\n  id: z.string().nullish(),\n  model: z.string().nullish(),\n  content: z.array(\n    z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking'),\n        thinking: z.string(),\n        signature: z.string(),\n      }),\n      z.object({\n        type: z.literal('redacted_thinking'),\n        data: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n        input: z.unknown(),\n      }),\n    ]),\n  ),\n  stop_reason: z.string().nullish(),\n  usage: z.object({\n    input_tokens: z.number(),\n    output_tokens: z.number(),\n    cache_creation_input_tokens: z.number().nullish(),\n    cache_read_input_tokens: z.number().nullish(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst anthropicMessagesChunkSchema = z.discriminatedUnion('type', [\n  z.object({\n    type: z.literal('message_start'),\n    message: z.object({\n      id: z.string().nullish(),\n      model: z.string().nullish(),\n      usage: z.object({\n        input_tokens: z.number(),\n        output_tokens: z.number(),\n        cache_creation_input_tokens: z.number().nullish(),\n        cache_read_input_tokens: z.number().nullish(),\n      }),\n    }),\n  }),\n  z.object({\n    type: z.literal('content_block_start'),\n    index: z.number(),\n    content_block: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('text'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking'),\n        thinking: z.string(),\n      }),\n      z.object({\n        type: z.literal('tool_use'),\n        id: z.string(),\n        name: z.string(),\n      }),\n      z.object({\n        type: z.literal('redacted_thinking'),\n        data: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_delta'),\n    index: z.number(),\n    delta: z.discriminatedUnion('type', [\n      z.object({\n        type: z.literal('input_json_delta'),\n        partial_json: z.string(),\n      }),\n      z.object({\n        type: z.literal('text_delta'),\n        text: z.string(),\n      }),\n      z.object({\n        type: z.literal('thinking_delta'),\n        thinking: z.string(),\n      }),\n      z.object({\n        type: z.literal('signature_delta'),\n        signature: z.string(),\n      }),\n    ]),\n  }),\n  z.object({\n    type: z.literal('content_block_stop'),\n    index: z.number(),\n  }),\n  z.object({\n    type: z.literal('error'),\n    error: z.object({\n      type: z.string(),\n      message: z.string(),\n    }),\n  }),\n  z.object({\n    type: z.literal('message_delta'),\n    delta: z.object({ stop_reason: z.string().nullish() }),\n    usage: z.object({ output_tokens: z.number() }),\n  }),\n  z.object({\n    type: z.literal('message_stop'),\n  }),\n  z.object({\n    type: z.literal('ping'),\n  }),\n]);\n\nconst anthropicProviderOptionsSchema = z.object({\n  thinking: z\n    .object({\n      type: z.union([z.literal('enabled'), z.literal('disabled')]),\n      budgetTokens: z.number().optional(),\n    })\n    .optional(),\n});\n\nexport type AnthropicProviderOptions = z.infer<\n  typeof anthropicProviderOptionsSchema\n>;\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst anthropicErrorDataSchema = z.object({\n  type: z.literal('error'),\n  error: z.object({\n    type: z.string(),\n    message: z.string(),\n  }),\n});\n\nexport type AnthropicErrorData = z.infer<typeof anthropicErrorDataSchema>;\n\nexport const anthropicFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: anthropicErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { AnthropicTool, AnthropicToolChoice } from './anthropic-api-types';\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n): {\n  tools: Array<AnthropicTool> | undefined;\n  tool_choice: AnthropicToolChoice | undefined;\n  toolWarnings: LanguageModelV1CallWarning[];\n  betas: Set<string>;\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n  const betas = new Set<string>();\n\n  if (tools == null) {\n    return { tools: undefined, tool_choice: undefined, toolWarnings, betas };\n  }\n\n  const anthropicTools: AnthropicTool[] = [];\n\n  for (const tool of tools) {\n    switch (tool.type) {\n      case 'function':\n        anthropicTools.push({\n          name: tool.name,\n          description: tool.description,\n          input_schema: tool.parameters,\n        });\n        break;\n      case 'provider-defined':\n        switch (tool.id) {\n          case 'anthropic.computer_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'computer_20250124',\n              display_width_px: tool.args.displayWidthPx as number,\n              display_height_px: tool.args.displayHeightPx as number,\n              display_number: tool.args.displayNumber as number,\n            });\n            break;\n          case 'anthropic.computer_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'computer_20241022',\n              display_width_px: tool.args.displayWidthPx as number,\n              display_height_px: tool.args.displayHeightPx as number,\n              display_number: tool.args.displayNumber as number,\n            });\n            break;\n          case 'anthropic.text_editor_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'text_editor_20250124',\n            });\n            break;\n          case 'anthropic.text_editor_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'text_editor_20241022',\n            });\n            break;\n          case 'anthropic.bash_20250124':\n            betas.add('computer-use-2025-01-24');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'bash_20250124',\n            });\n            break;\n          case 'anthropic.bash_20241022':\n            betas.add('computer-use-2024-10-22');\n            anthropicTools.push({\n              name: tool.name,\n              type: 'bash_20241022',\n            });\n            break;\n          default:\n            toolWarnings.push({ type: 'unsupported-tool', tool });\n            break;\n        }\n        break;\n      default:\n        toolWarnings.push({ type: 'unsupported-tool', tool });\n        break;\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return {\n      tools: anthropicTools,\n      tool_choice: undefined,\n      toolWarnings,\n      betas,\n    };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'auto' },\n        toolWarnings,\n        betas,\n      };\n    case 'required':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'any' },\n        toolWarnings,\n        betas,\n      };\n    case 'none':\n      // Anthropic does not support 'none' tool choice, so we remove the tools:\n      return { tools: undefined, tool_choice: undefined, toolWarnings, betas };\n    case 'tool':\n      return {\n        tools: anthropicTools,\n        tool_choice: { type: 'tool', name: toolChoice.toolName },\n        toolWarnings,\n        betas,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  LanguageModelV1CallWarning,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1ProviderMetadata,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  AnthropicAssistantMessage,\n  AnthropicCacheControl,\n  AnthropicMessagesPrompt,\n  AnthropicUserMessage,\n} from './anthropic-api-types';\n\nexport function convertToAnthropicMessagesPrompt({\n  prompt,\n  sendReasoning,\n  warnings,\n}: {\n  prompt: LanguageModelV1Prompt;\n  sendReasoning: boolean;\n  warnings: LanguageModelV1CallWarning[];\n}): {\n  prompt: AnthropicMessagesPrompt;\n  betas: Set<string>;\n} {\n  const betas = new Set<string>();\n  const blocks = groupIntoBlocks(prompt);\n\n  let system: AnthropicMessagesPrompt['system'] = undefined;\n  const messages: AnthropicMessagesPrompt['messages'] = [];\n\n  function getCacheControl(\n    providerMetadata: LanguageModelV1ProviderMetadata | undefined,\n  ): AnthropicCacheControl | undefined {\n    const anthropic = providerMetadata?.anthropic;\n\n    // allow both cacheControl and cache_control:\n    const cacheControlValue =\n      anthropic?.cacheControl ?? anthropic?.cache_control;\n\n    // Pass through value assuming it is of the correct type.\n    // The Anthropic API will validate the value.\n    return cacheControlValue as AnthropicCacheControl | undefined;\n  }\n\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const isLastBlock = i === blocks.length - 1;\n    const type = block.type;\n\n    switch (type) {\n      case 'system': {\n        if (system != null) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'Multiple system messages that are separated by user/assistant messages',\n          });\n        }\n\n        system = block.messages.map(({ content, providerMetadata }) => ({\n          type: 'text',\n          text: content,\n          cache_control: getCacheControl(providerMetadata),\n        }));\n\n        break;\n      }\n\n      case 'user': {\n        // combines all user and tool messages in this block into a single message:\n        const anthropicContent: AnthropicUserMessage['content'] = [];\n\n        for (const message of block.messages) {\n          const { role, content } = message;\n          switch (role) {\n            case 'user': {\n              for (let j = 0; j < content.length; j++) {\n                const part = content[j];\n\n                // cache control: first add cache control from part.\n                // for the last part of a message,\n                // check also if the message has cache control.\n                const isLastPart = j === content.length - 1;\n\n                const cacheControl =\n                  getCacheControl(part.providerMetadata) ??\n                  (isLastPart\n                    ? getCacheControl(message.providerMetadata)\n                    : undefined);\n\n                switch (part.type) {\n                  case 'text': {\n                    anthropicContent.push({\n                      type: 'text',\n                      text: part.text,\n                      cache_control: cacheControl,\n                    });\n                    break;\n                  }\n\n                  case 'image': {\n                    anthropicContent.push({\n                      type: 'image',\n                      source:\n                        part.image instanceof URL\n                          ? {\n                              type: 'url',\n                              url: part.image.toString(),\n                            }\n                          : {\n                              type: 'base64',\n                              media_type: part.mimeType ?? 'image/jpeg',\n                              data: convertUint8ArrayToBase64(part.image),\n                            },\n                      cache_control: cacheControl,\n                    });\n\n                    break;\n                  }\n\n                  case 'file': {\n                    if (part.mimeType !== 'application/pdf') {\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'Non-PDF files in user messages',\n                      });\n                    }\n\n                    betas.add('pdfs-2024-09-25');\n\n                    anthropicContent.push({\n                      type: 'document',\n                      source:\n                        part.data instanceof URL\n                          ? {\n                              type: 'url',\n                              url: part.data.toString(),\n                            }\n                          : {\n                              type: 'base64',\n                              media_type: 'application/pdf',\n                              data: part.data,\n                            },\n                      cache_control: cacheControl,\n                    });\n\n                    break;\n                  }\n                }\n              }\n\n              break;\n            }\n            case 'tool': {\n              for (let i = 0; i < content.length; i++) {\n                const part = content[i];\n\n                // cache control: first add cache control from part.\n                // for the last part of a message,\n                // check also if the message has cache control.\n                const isLastPart = i === content.length - 1;\n\n                const cacheControl =\n                  getCacheControl(part.providerMetadata) ??\n                  (isLastPart\n                    ? getCacheControl(message.providerMetadata)\n                    : undefined);\n\n                const toolResultContent =\n                  part.content != null\n                    ? part.content.map(part => {\n                        switch (part.type) {\n                          case 'text':\n                            return {\n                              type: 'text' as const,\n                              text: part.text,\n                              cache_control: undefined,\n                            };\n                          case 'image':\n                            return {\n                              type: 'image' as const,\n                              source: {\n                                type: 'base64' as const,\n                                media_type: part.mimeType ?? 'image/jpeg',\n                                data: part.data,\n                              },\n                              cache_control: undefined,\n                            };\n                        }\n                      })\n                    : JSON.stringify(part.result);\n\n                anthropicContent.push({\n                  type: 'tool_result',\n                  tool_use_id: part.toolCallId,\n                  content: toolResultContent,\n                  is_error: part.isError,\n                  cache_control: cacheControl,\n                });\n              }\n\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n\n        messages.push({ role: 'user', content: anthropicContent });\n\n        break;\n      }\n\n      case 'assistant': {\n        // combines multiple assistant messages in this block into a single message:\n        const anthropicContent: AnthropicAssistantMessage['content'] = [];\n\n        for (let j = 0; j < block.messages.length; j++) {\n          const message = block.messages[j];\n          const isLastMessage = j === block.messages.length - 1;\n          const { content } = message;\n\n          for (let k = 0; k < content.length; k++) {\n            const part = content[k];\n            const isLastContentPart = k === content.length - 1;\n\n            // cache control: first add cache control from part.\n            // for the last part of a message,\n            // check also if the message has cache control.\n            const cacheControl =\n              getCacheControl(part.providerMetadata) ??\n              (isLastContentPart\n                ? getCacheControl(message.providerMetadata)\n                : undefined);\n\n            switch (part.type) {\n              case 'text': {\n                anthropicContent.push({\n                  type: 'text',\n                  text:\n                    // trim the last text part if it's the last message in the block\n                    // because Anthropic does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    isLastBlock && isLastMessage && isLastContentPart\n                      ? part.text.trim()\n                      : part.text,\n\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n\n              case 'reasoning': {\n                if (sendReasoning) {\n                  anthropicContent.push({\n                    type: 'thinking',\n                    thinking: part.text,\n                    signature: part.signature!,\n                    cache_control: cacheControl,\n                  });\n                } else {\n                  warnings.push({\n                    type: 'other',\n                    message:\n                      'sending reasoning content is disabled for this model',\n                  });\n                }\n                break;\n              }\n\n              case 'redacted-reasoning': {\n                anthropicContent.push({\n                  type: 'redacted_thinking',\n                  data: part.data,\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n\n              case 'tool-call': {\n                anthropicContent.push({\n                  type: 'tool_use',\n                  id: part.toolCallId,\n                  name: part.toolName,\n                  input: part.args,\n                  cache_control: cacheControl,\n                });\n                break;\n              }\n            }\n          }\n        }\n\n        messages.push({ role: 'assistant', content: anthropicContent });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return {\n    prompt: { system, messages },\n    betas,\n  };\n}\n\ntype SystemBlock = {\n  type: 'system';\n  messages: Array<LanguageModelV1Message & { role: 'system' }>;\n};\ntype AssistantBlock = {\n  type: 'assistant';\n  messages: Array<LanguageModelV1Message & { role: 'assistant' }>;\n};\ntype UserBlock = {\n  type: 'user';\n  messages: Array<LanguageModelV1Message & { role: 'user' | 'tool' }>;\n};\n\nfunction groupIntoBlocks(\n  prompt: LanguageModelV1Prompt,\n): Array<SystemBlock | AssistantBlock | UserBlock> {\n  const blocks: Array<SystemBlock | AssistantBlock | UserBlock> = [];\n  let currentBlock: SystemBlock | AssistantBlock | UserBlock | undefined =\n    undefined;\n\n  for (const message of prompt) {\n    const { role } = message;\n    switch (role) {\n      case 'system': {\n        if (currentBlock?.type !== 'system') {\n          currentBlock = { type: 'system', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'assistant': {\n        if (currentBlock?.type !== 'assistant') {\n          currentBlock = { type: 'assistant', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'user': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'tool': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return blocks;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapAnthropicStopReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'end_turn':\n    case 'stop_sequence':\n      return 'stop';\n    case 'tool_use':\n      return 'tool-calls';\n    case 'max_tokens':\n      return 'length';\n    default:\n      return 'unknown';\n  }\n}\n", "import { z } from 'zod';\n\n// Copied from ai package\ntype ExecuteFunction<PARAMETERS, RESULT> =\n  | undefined\n  | ((\n      args: PARAMETERS,\n      options: { abortSignal?: AbortSignal },\n    ) => Promise<RESULT>);\n\n// Copied from ai package\nexport type ToolResultContent = Array<\n  | {\n      type: 'text';\n      text: string;\n    }\n  | {\n      type: 'image';\n      data: string; // base64 encoded png image, e.g. screenshot\n      mimeType?: string; // e.g. 'image/png';\n    }\n>;\n\nconst Bash20241022Parameters = z.object({\n  command: z.string(),\n  restart: z.boolean().optional(),\n});\n\n/**\n * Creates a tool for running a bash command. Must have name \"bash\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction bashTool_20241022<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The bash command to run. Required unless the tool is being restarted.\n         */\n        command: string;\n\n        /**\n         * Specifying true will restart this tool. Otherwise, leave this unspecified.\n         */\n        restart?: boolean;\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.bash_20241022';\n  args: {};\n  parameters: typeof Bash20241022Parameters;\n  execute: ExecuteFunction<z.infer<typeof Bash20241022Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.bash_20241022',\n    args: {},\n    parameters: Bash20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Bash20250124Parameters = z.object({\n  command: z.string(),\n  restart: z.boolean().optional(),\n});\n\n/**\n * Creates a tool for running a bash command. Must have name \"bash\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction bashTool_20250124<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The bash command to run. Required unless the tool is being restarted.\n         */\n        command: string;\n\n        /**\n         * Specifying true will restart this tool. Otherwise, leave this unspecified.\n         */\n        restart?: boolean;\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.bash_20250124';\n  args: {};\n  parameters: typeof Bash20250124Parameters;\n  execute: ExecuteFunction<z.infer<typeof Bash20250124Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.bash_20250124',\n    args: {},\n    parameters: Bash20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst TextEditor20241022Parameters = z.object({\n  command: z.enum(['view', 'create', 'str_replace', 'insert', 'undo_edit']),\n  path: z.string(),\n  file_text: z.string().optional(),\n  insert_line: z.number().int().optional(),\n  new_str: z.string().optional(),\n  old_str: z.string().optional(),\n  view_range: z.array(z.number().int()).optional(),\n});\n\n/**\n * Creates a tool for editing text. Must have name \"str_replace_editor\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction textEditorTool_20241022<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.\n         */\n        command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';\n\n        /**\n         * Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.\n         */\n        path: string;\n\n        /**\n         * Required parameter of `create` command, with the content of the file to be created.\n         */\n        file_text?: string;\n\n        /**\n         * Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.\n         */\n        insert_line?: number;\n\n        /**\n         * Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.\n         */\n        new_str?: string;\n\n        /**\n         * Required parameter of `str_replace` command containing the string in `path` to replace.\n         */\n        old_str?: string;\n\n        /**\n         * Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.\n         */\n        view_range?: number[];\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.text_editor_20241022';\n  args: {};\n  parameters: typeof TextEditor20241022Parameters;\n  execute: ExecuteFunction<\n    z.infer<typeof TextEditor20241022Parameters>,\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.text_editor_20241022',\n    args: {},\n    parameters: TextEditor20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst TextEditor20250124Parameters = z.object({\n  command: z.enum(['view', 'create', 'str_replace', 'insert', 'undo_edit']),\n  path: z.string(),\n  file_text: z.string().optional(),\n  insert_line: z.number().int().optional(),\n  new_str: z.string().optional(),\n  old_str: z.string().optional(),\n  view_range: z.array(z.number().int()).optional(),\n});\n\n/**\n * Creates a tool for editing text. Must have name \"str_replace_editor\".\n *\n * Image results are supported.\n *\n * @param execute - The function to execute the tool. Optional.\n */\nfunction textEditorTool_20250124<RESULT>(\n  options: {\n    execute?: ExecuteFunction<\n      {\n        /**\n         * The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.\n         */\n        command: 'view' | 'create' | 'str_replace' | 'insert' | 'undo_edit';\n\n        /**\n         * Absolute path to file or directory, e.g. `/repo/file.py` or `/repo`.\n         */\n        path: string;\n\n        /**\n         * Required parameter of `create` command, with the content of the file to be created.\n         */\n        file_text?: string;\n\n        /**\n         * Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.\n         */\n        insert_line?: number;\n\n        /**\n         * Optional parameter of `str_replace` command containing the new string (if not given, no string will be added). Required parameter of `insert` command containing the string to insert.\n         */\n        new_str?: string;\n\n        /**\n         * Required parameter of `str_replace` command containing the string in `path` to replace.\n         */\n        old_str?: string;\n\n        /**\n         * Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.\n         */\n        view_range?: number[];\n      },\n      RESULT\n    >;\n    experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n  } = {},\n): {\n  type: 'provider-defined';\n  id: 'anthropic.text_editor_20250124';\n  args: {};\n  parameters: typeof TextEditor20250124Parameters;\n  execute: ExecuteFunction<\n    z.infer<typeof TextEditor20250124Parameters>,\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.text_editor_20250124',\n    args: {},\n    parameters: TextEditor20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Computer20241022Parameters = z.object({\n  action: z.enum([\n    'key',\n    'type',\n    'mouse_move',\n    'left_click',\n    'left_click_drag',\n    'right_click',\n    'middle_click',\n    'double_click',\n    'screenshot',\n    'cursor_position',\n  ]),\n  coordinate: z.array(z.number().int()).optional(),\n  text: z.string().optional(),\n});\n\n/**\n * Creates a tool for executing actions on a computer. Must have name \"computer\".\n *\n * Image results are supported.\n *\n * @param displayWidthPx - The width of the display being controlled by the model in pixels.\n * @param displayHeightPx - The height of the display being controlled by the model in pixels.\n * @param displayNumber - The display number to control (only relevant for X11 environments). If specified, the tool will be provided a display number in the tool definition.\n * @param execute - The function to execute the tool. Optional.\n */\nfunction computerTool_20241022<RESULT>(options: {\n  displayWidthPx: number;\n  displayHeightPx: number;\n  displayNumber?: number;\n  execute?: ExecuteFunction<\n    {\n      /**\n       * The action to perform. The available actions are:\n       * - `key`: Press a key or key-combination on the keyboard.\n       *   - This supports xdotool's `key` syntax.\n       *   - Examples: \"a\", \"Return\", \"alt+Tab\", \"ctrl+s\", \"Up\", \"KP_0\" (for the numpad 0 key).\n       * - `type`: Type a string of text on the keyboard.\n       * - `cursor_position`: Get the current (x, y) pixel coordinate of the cursor on the screen.\n       * - `mouse_move`: Move the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `left_click`: Click the left mouse button.\n       * - `left_click_drag`: Click and drag the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `right_click`: Click the right mouse button.\n       * - `middle_click`: Click the middle mouse button.\n       * - `double_click`: Double-click the left mouse button.\n       * - `screenshot`: Take a screenshot of the screen.\n       */\n      action:\n        | 'key'\n        | 'type'\n        | 'mouse_move'\n        | 'left_click'\n        | 'left_click_drag'\n        | 'right_click'\n        | 'middle_click'\n        | 'double_click'\n        | 'screenshot'\n        | 'cursor_position';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to move the mouse to. Required only by `action=mouse_move` and `action=left_click_drag`.\n       */\n      coordinate?: number[];\n\n      /**\n       * Required only by `action=type` and `action=key`.\n       */\n      text?: string;\n    },\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n}): {\n  type: 'provider-defined';\n  id: 'anthropic.computer_20241022';\n  args: {};\n  parameters: typeof Computer20241022Parameters;\n  execute: ExecuteFunction<z.infer<typeof Computer20241022Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.computer_20241022',\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber,\n    },\n    parameters: Computer20241022Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nconst Computer20250124Parameters = z.object({\n  action: z.enum([\n    'key',\n    'hold_key',\n    'type',\n    'cursor_position',\n    'mouse_move',\n    'left_mouse_down',\n    'left_mouse_up',\n    'left_click',\n    'left_click_drag',\n    'right_click',\n    'middle_click',\n    'double_click',\n    'triple_click',\n    'scroll',\n    'wait',\n    'screenshot',\n  ]),\n  coordinate: z.tuple([z.number().int(), z.number().int()]).optional(),\n  duration: z.number().optional(),\n  scroll_amount: z.number().optional(),\n  scroll_direction: z.enum(['up', 'down', 'left', 'right']).optional(),\n  start_coordinate: z.tuple([z.number().int(), z.number().int()]).optional(),\n  text: z.string().optional(),\n});\n\n/**\n * Creates a tool for executing actions on a computer. Must have name \"computer\".\n *\n * Image results are supported.\n *\n * @param displayWidthPx - The width of the display being controlled by the model in pixels.\n * @param displayHeightPx - The height of the display being controlled by the model in pixels.\n * @param displayNumber - The display number to control (only relevant for X11 environments). If specified, the tool will be provided a display number in the tool definition.\n * @param execute - The function to execute the tool. Optional.\n */\nfunction computerTool_20250124<RESULT>(options: {\n  displayWidthPx: number;\n  displayHeightPx: number;\n  displayNumber?: number;\n  execute?: ExecuteFunction<\n    {\n      /**\n       * - `key`: Press a key or key-combination on the keyboard.\n       *   - This supports xdotool's `key` syntax.\n       *   - Examples: \"a\", \"Return\", \"alt+Tab\", \"ctrl+s\", \"Up\", \"KP_0\" (for the numpad 0 key).\n       * - `hold_key`: Hold down a key or multiple keys for a specified duration (in seconds). Supports the same syntax as `key`.\n       * - `type`: Type a string of text on the keyboard.\n       * - `cursor_position`: Get the current (x, y) pixel coordinate of the cursor on the screen.\n       * - `mouse_move`: Move the cursor to a specified (x, y) pixel coordinate on the screen.\n       * - `left_mouse_down`: Press the left mouse button.\n       * - `left_mouse_up`: Release the left mouse button.\n       * - `left_click`: Click the left mouse button at the specified (x, y) pixel coordinate on the screen. You can also include a key combination to hold down while clicking using the `text` parameter.\n       * - `left_click_drag`: Click and drag the cursor from `start_coordinate` to a specified (x, y) pixel coordinate on the screen.\n       * - `right_click`: Click the right mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `middle_click`: Click the middle mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `double_click`: Double-click the left mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `triple_click`: Triple-click the left mouse button at the specified (x, y) pixel coordinate on the screen.\n       * - `scroll`: Scroll the screen in a specified direction by a specified amount of clicks of the scroll wheel, at the specified (x, y) pixel coordinate. DO NOT use PageUp/PageDown to scroll.\n       * - `wait`: Wait for a specified duration (in seconds).\n       * - `screenshot`: Take a screenshot of the screen.\n       */\n      action:\n        | 'key'\n        | 'hold_key'\n        | 'type'\n        | 'cursor_position'\n        | 'mouse_move'\n        | 'left_mouse_down'\n        | 'left_mouse_up'\n        | 'left_click'\n        | 'left_click_drag'\n        | 'right_click'\n        | 'middle_click'\n        | 'double_click'\n        | 'triple_click'\n        | 'scroll'\n        | 'wait'\n        | 'screenshot';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to move the mouse to. Required only by `action=mouse_move` and `action=left_click_drag`.\n       */\n      coordinate?: [number, number];\n\n      /**\n       * The duration to hold the key down for. Required only by `action=hold_key` and `action=wait`.\n       */\n      duration?: number;\n\n      /**\n       * The number of 'clicks' to scroll. Required only by `action=scroll`.\n       */\n      scroll_amount?: number;\n\n      /**\n       * The direction to scroll the screen. Required only by `action=scroll`.\n       */\n      scroll_direction?: 'up' | 'down' | 'left' | 'right';\n\n      /**\n       * (x, y): The x (pixels from the left edge) and y (pixels from the top edge) coordinates to start the drag from. Required only by `action=left_click_drag`.\n       */\n      start_coordinate?: [number, number];\n\n      /**\n       * Required only by `action=type`, `action=key`, and `action=hold_key`. Can also be used by click or scroll actions to hold down keys while clicking or scrolling.\n       */\n      text?: string;\n    },\n    RESULT\n  >;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n}): {\n  type: 'provider-defined';\n  id: 'anthropic.computer_20250124';\n  args: {};\n  parameters: typeof Computer20250124Parameters;\n  execute: ExecuteFunction<z.infer<typeof Computer20250124Parameters>, RESULT>;\n  experimental_toToolResultContent?: (result: RESULT) => ToolResultContent;\n} {\n  return {\n    type: 'provider-defined',\n    id: 'anthropic.computer_20250124',\n    args: {\n      displayWidthPx: options.displayWidthPx,\n      displayHeightPx: options.displayHeightPx,\n      displayNumber: options.displayNumber,\n    },\n    parameters: Computer20250124Parameters,\n    execute: options.execute,\n    experimental_toToolResultContent: options.experimental_toToolResultContent,\n  };\n}\n\nexport const anthropicTools = {\n  bash_20241022: bashTool_20241022,\n  bash_20250124: bashTool_20250124,\n  textEditor_20241022: textEditorTool_20241022,\n  textEditor_20250124: textEditorTool_20250124,\n  computer_20241022: computerTool_20241022,\n  computer_20250124: computerTool_20250124,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,mBAIO;AACP,IAAAC,yBAIO;;;ACTP,IAAAC,mBAQO;AACP,IAAAC,yBAUO;AACP,IAAAC,cAAkB;;;ACpBlB,4BAA+C;AAC/C,iBAAkB;AAElB,IAAM,2BAA2B,aAAE,OAAO;AAAA,EACxC,MAAM,aAAE,QAAQ,OAAO;AAAA,EACvB,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,OAAO;AAAA,IACf,SAAS,aAAE,OAAO;AAAA,EACpB,CAAC;AACH,CAAC;AAIM,IAAM,qCAAiC,sDAA+B;AAAA,EAC3E,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK,MAAM;AACrC,CAAC;;;AChBD,sBAIO;AAGA,SAAS,aACd,MAQA;AAhBF;AAkBE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAEhD,QAAM,eAA6C,CAAC;AACpD,QAAM,QAAQ,oBAAI,IAAY;AAE9B,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,aAAa,QAAW,cAAc,MAAM;AAAA,EACzE;AAEA,QAAMC,kBAAkC,CAAC;AAEzC,aAAW,QAAQ,OAAO;AACxB,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,QAAAA,gBAAe,KAAK;AAAA,UAClB,MAAM,KAAK;AAAA,UACX,aAAa,KAAK;AAAA,UAClB,cAAc,KAAK;AAAA,QACrB,CAAC;AACD;AAAA,MACF,KAAK;AACH,gBAAQ,KAAK,IAAI;AAAA,UACf,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,cACN,kBAAkB,KAAK,KAAK;AAAA,cAC5B,mBAAmB,KAAK,KAAK;AAAA,cAC7B,gBAAgB,KAAK,KAAK;AAAA,YAC5B,CAAC;AACD;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,cACN,kBAAkB,KAAK,KAAK;AAAA,cAC5B,mBAAmB,KAAK,KAAK;AAAA,cAC7B,gBAAgB,KAAK,KAAK;AAAA,YAC5B,CAAC;AACD;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF,KAAK;AACH,kBAAM,IAAI,yBAAyB;AACnC,YAAAA,gBAAe,KAAK;AAAA,cAClB,MAAM,KAAK;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF;AACE,yBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AACpD;AAAA,QACJ;AACA;AAAA,MACF;AACE,qBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AACpD;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,MACL,OAAOA;AAAA,MACP,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,OAAOA;AAAA,QACP,aAAa,EAAE,MAAM,OAAO;AAAA,QAC5B;AAAA,QACA;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAOA;AAAA,QACP,aAAa,EAAE,MAAM,MAAM;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,IACF,KAAK;AAEH,aAAO,EAAE,OAAO,QAAW,aAAa,QAAW,cAAc,MAAM;AAAA,IACzE,KAAK;AACH,aAAO;AAAA,QACL,OAAOA;AAAA,QACP,aAAa,EAAE,MAAM,QAAQ,MAAM,WAAW,SAAS;AAAA,QACvD;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,8CAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AChJA,IAAAC,mBAMO;AACP,IAAAC,yBAA0C;AAQnC,SAAS,iCAAiC;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AACF,GAOE;AA1BF;AA2BE,QAAM,QAAQ,oBAAI,IAAY;AAC9B,QAAM,SAAS,gBAAgB,MAAM;AAErC,MAAI,SAA4C;AAChD,QAAM,WAAgD,CAAC;AAEvD,WAAS,gBACP,kBACmC;AAnCvC,QAAAC;AAoCI,UAAMC,aAAY,qDAAkB;AAGpC,UAAM,qBACJD,MAAAC,cAAA,gBAAAA,WAAW,iBAAX,OAAAD,MAA2BC,cAAA,gBAAAA,WAAW;AAIxC,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,cAAc,MAAM,OAAO,SAAS;AAC1C,UAAM,OAAO,MAAM;AAEnB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,UAAU,MAAM;AAClB,gBAAM,IAAI,+CAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,iBAAS,MAAM,SAAS,IAAI,CAAC,EAAE,SAAS,iBAAiB,OAAO;AAAA,UAC9D,MAAM;AAAA,UACN,MAAM;AAAA,UACN,eAAe,gBAAgB,gBAAgB;AAAA,QACjD,EAAE;AAEF;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AAEX,cAAM,mBAAoD,CAAC;AAE3D,mBAAW,WAAW,MAAM,UAAU;AACpC,gBAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,kBAAQ,MAAM;AAAA,YACZ,KAAK,QAAQ;AACX,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,sBAAM,OAAO,QAAQ,CAAC;AAKtB,sBAAM,aAAa,MAAM,QAAQ,SAAS;AAE1C,sBAAM,gBACJ,qBAAgB,KAAK,gBAAgB,MAArC,YACC,aACG,gBAAgB,QAAQ,gBAAgB,IACxC;AAEN,wBAAQ,KAAK,MAAM;AAAA,kBACjB,KAAK,QAAQ;AACX,qCAAiB,KAAK;AAAA,sBACpB,MAAM;AAAA,sBACN,MAAM,KAAK;AAAA,sBACX,eAAe;AAAA,oBACjB,CAAC;AACD;AAAA,kBACF;AAAA,kBAEA,KAAK,SAAS;AACZ,qCAAiB,KAAK;AAAA,sBACpB,MAAM;AAAA,sBACN,QACE,KAAK,iBAAiB,MAClB;AAAA,wBACE,MAAM;AAAA,wBACN,KAAK,KAAK,MAAM,SAAS;AAAA,sBAC3B,IACA;AAAA,wBACE,MAAM;AAAA,wBACN,aAAY,UAAK,aAAL,YAAiB;AAAA,wBAC7B,UAAM,kDAA0B,KAAK,KAAK;AAAA,sBAC5C;AAAA,sBACN,eAAe;AAAA,oBACjB,CAAC;AAED;AAAA,kBACF;AAAA,kBAEA,KAAK,QAAQ;AACX,wBAAI,KAAK,aAAa,mBAAmB;AACvC,4BAAM,IAAI,+CAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,0BAAM,IAAI,iBAAiB;AAE3B,qCAAiB,KAAK;AAAA,sBACpB,MAAM;AAAA,sBACN,QACE,KAAK,gBAAgB,MACjB;AAAA,wBACE,MAAM;AAAA,wBACN,KAAK,KAAK,KAAK,SAAS;AAAA,sBAC1B,IACA;AAAA,wBACE,MAAM;AAAA,wBACN,YAAY;AAAA,wBACZ,MAAM,KAAK;AAAA,sBACb;AAAA,sBACN,eAAe;AAAA,oBACjB,CAAC;AAED;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,uBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,sBAAM,OAAO,QAAQA,EAAC;AAKtB,sBAAM,aAAaA,OAAM,QAAQ,SAAS;AAE1C,sBAAM,gBACJ,qBAAgB,KAAK,gBAAgB,MAArC,YACC,aACG,gBAAgB,QAAQ,gBAAgB,IACxC;AAEN,sBAAM,oBACJ,KAAK,WAAW,OACZ,KAAK,QAAQ,IAAI,CAAAC,UAAQ;AA3K/C,sBAAAH;AA4KwB,0BAAQG,MAAK,MAAM;AAAA,oBACjB,KAAK;AACH,6BAAO;AAAA,wBACL,MAAM;AAAA,wBACN,MAAMA,MAAK;AAAA,wBACX,eAAe;AAAA,sBACjB;AAAA,oBACF,KAAK;AACH,6BAAO;AAAA,wBACL,MAAM;AAAA,wBACN,QAAQ;AAAA,0BACN,MAAM;AAAA,0BACN,aAAYH,MAAAG,MAAK,aAAL,OAAAH,MAAiB;AAAA,0BAC7B,MAAMG,MAAK;AAAA,wBACb;AAAA,wBACA,eAAe;AAAA,sBACjB;AAAA,kBACJ;AAAA,gBACF,CAAC,IACD,KAAK,UAAU,KAAK,MAAM;AAEhC,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN,aAAa,KAAK;AAAA,kBAClB,SAAS;AAAA,kBACT,UAAU,KAAK;AAAA,kBACf,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAEA;AAAA,YACF;AAAA,YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,iBAAiB,CAAC;AAEzD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAEhB,cAAM,mBAAyD,CAAC;AAEhE,iBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,QAAQ,KAAK;AAC9C,gBAAM,UAAU,MAAM,SAAS,CAAC;AAChC,gBAAM,gBAAgB,MAAM,MAAM,SAAS,SAAS;AACpD,gBAAM,EAAE,QAAQ,IAAI;AAEpB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAM,OAAO,QAAQ,CAAC;AACtB,kBAAM,oBAAoB,MAAM,QAAQ,SAAS;AAKjD,kBAAM,gBACJ,qBAAgB,KAAK,gBAAgB,MAArC,YACC,oBACG,gBAAgB,QAAQ,gBAAgB,IACxC;AAEN,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN;AAAA;AAAA;AAAA;AAAA,oBAIE,eAAe,iBAAiB,oBAC5B,KAAK,KAAK,KAAK,IACf,KAAK;AAAA;AAAA,kBAEX,eAAe;AAAA,gBACjB,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,oBAAI,eAAe;AACjB,mCAAiB,KAAK;AAAA,oBACpB,MAAM;AAAA,oBACN,UAAU,KAAK;AAAA,oBACf,WAAW,KAAK;AAAA,oBAChB,eAAe;AAAA,kBACjB,CAAC;AAAA,gBACH,OAAO;AACL,2BAAS,KAAK;AAAA,oBACZ,MAAM;AAAA,oBACN,SACE;AAAA,kBACJ,CAAC;AAAA,gBACH;AACA;AAAA,cACF;AAAA,cAEA,KAAK,sBAAsB;AACzB,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN,MAAM,KAAK;AAAA,kBACX,eAAe;AAAA,gBACjB,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,iCAAiB,KAAK;AAAA,kBACpB,MAAM;AAAA,kBACN,IAAI,KAAK;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,OAAO,KAAK;AAAA,kBACZ,eAAe;AAAA,gBACjB,CAAC;AACD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,aAAa,SAAS,iBAAiB,CAAC;AAE9D;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,QAAQ,EAAE,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACF;AAeA,SAAS,gBACP,QACiD;AACjD,QAAM,SAA0D,CAAC;AACjE,MAAI,eACF;AAEF,aAAW,WAAW,QAAQ;AAC5B,UAAM,EAAE,KAAK,IAAI;AACjB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,aAAI,6CAAc,UAAS,UAAU;AACnC,yBAAe,EAAE,MAAM,UAAU,UAAU,CAAC,EAAE;AAC9C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,aAAI,6CAAc,UAAS,aAAa;AACtC,yBAAe,EAAE,MAAM,aAAa,UAAU,CAAC,EAAE;AACjD,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC3XO,SAAS,uBACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AJwBO,IAAM,iCAAN,MAAgE;AAAA,EASrE,YACE,SACA,UACA,QACA;AAZF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AAYrC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAEA,YAAY,KAAmB;AAC7B,WAAO,IAAI,aAAa;AAAA,EAC1B;AAAA,EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,oBAA6B;AAC/B,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAc,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA,YAAY;AAAA;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,EACpB,GAAiD;AApFnD;AAqFI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,oBAAoB,MAAM;AAC5B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,mBAAmB,MAAM;AAC3B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,kBAAkB,QAAQ,eAAe,SAAS,QAAQ;AAC5D,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,QAAQ,gBAAgB,OAAO,cAAc,IACnD,iCAAiC;AAAA,MAC/B;AAAA,MACA,gBAAe,UAAK,SAAS,kBAAd,YAA+B;AAAA,MAC9C;AAAA,IACF,CAAC;AAEH,UAAM,uBAAmB,6CAAqB;AAAA,MAC5C,UAAU;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,eAAa,0DAAkB,aAAlB,mBAA4B,UAAS;AACxD,UAAM,kBAAiB,0DAAkB,aAAlB,mBAA4B;AAEnD,UAAM,WAAW;AAAA;AAAA,MAEf,OAAO,KAAK;AAAA;AAAA,MAGZ,YAAY;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,MACP,gBAAgB;AAAA;AAAA,MAGhB,GAAI,cAAc;AAAA,QAChB,UAAU,EAAE,MAAM,WAAW,eAAe,eAAe;AAAA,MAC7D;AAAA;AAAA,MAGA,QAAQ,eAAe;AAAA,MACvB,UAAU,eAAe;AAAA,IAC3B;AAEA,QAAI,YAAY;AACd,UAAI,kBAAkB,MAAM;AAC1B,cAAM,IAAI,+CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,eAAe,MAAM;AAChC,iBAAS,cAAc;AACvB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAEA,UAAI,QAAQ,MAAM;AAChB,iBAAS,QAAQ;AACjB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAEA,UAAI,QAAQ,MAAM;AAChB,iBAAS,QAAQ;AACjB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAGA,eAAS,aAAa,YAAY;AAAA,IACpC;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,IAAI,aAAa,IAAI;AAErB,eAAO;AAAA,UACL,MAAM,EAAE,GAAG,UAAU,OAAO,YAAY;AAAA,UACxC,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,UACvC,OAAO,oBAAI,IAAI,CAAC,GAAG,eAAe,GAAG,UAAU,CAAC;AAAA,QAClD;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,IAAI,+CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,EAAE,MAAM,aAAa,WAAW,IAAI,KAAK;AAE/C,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,OAAO,CAAC,EAAE,MAAM,aAAa,cAAc,WAAW,CAAC;AAAA,YACvD,aAAa,EAAE,MAAM,QAAQ,KAAK;AAAA,UACpC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAc,WAAW;AAAA,IACvB;AAAA,IACA;AAAA,EACF,GAGG;AACD,eAAO;AAAA,MACL,UAAM,gCAAQ,KAAK,OAAO,OAAO;AAAA,MACjC,MAAM,OAAO,IAAI,EAAE,kBAAkB,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,IAAI,CAAC;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,gBAAgB,aAA8B;AA1PxD;AA2PI,YACE,sBAAK,QAAO,oBAAZ,4BAA8B,KAAK,OAAO,SAAS,iBAAnD,YACA,GAAG,KAAK,OAAO,OAAO;AAAA,EAE1B;AAAA,EAEQ,qBAAqB,MAAgD;AAjQ/E;AAkQI,YAAO,sBAAK,QAAO,yBAAZ,4BAAmC,UAAnC,YAA4C;AAAA,EACrD;AAAA,EAEA,MAAM,WACJ,SAC6D;AAvQjE;AAwQI,UAAM,EAAE,MAAM,UAAU,MAAM,IAAI,MAAM,KAAK,QAAQ,OAAO;AAE5D,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,IAAI,UAAM,sCAAc;AAAA,MACtB,KAAK,KAAK,gBAAgB,KAAK;AAAA,MAC/B,SAAS,MAAM,KAAK,WAAW,EAAE,OAAO,SAAS,QAAQ,QAAQ,CAAC;AAAA,MAClE,MAAM,KAAK,qBAAqB,IAAI;AAAA,MACpC,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAGhD,QAAI,OAAO;AACX,eAAW,WAAW,SAAS,SAAS;AACtC,UAAI,QAAQ,SAAS,QAAQ;AAC3B,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AAGA,QAAI,YAA2D;AAC/D,QAAI,SAAS,QAAQ,KAAK,aAAW,QAAQ,SAAS,UAAU,GAAG;AACjE,kBAAY,CAAC;AACb,iBAAW,WAAW,SAAS,SAAS;AACtC,YAAI,QAAQ,SAAS,YAAY;AAC/B,oBAAU,KAAK;AAAA,YACb,cAAc;AAAA,YACd,YAAY,QAAQ;AAAA,YACpB,UAAU,QAAQ;AAAA,YAClB,MAAM,KAAK,UAAU,QAAQ,KAAK;AAAA,UACpC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,UAAM,YAAY,SAAS,QACxB;AAAA,MACC,aACE,QAAQ,SAAS,uBAAuB,QAAQ,SAAS;AAAA,IAC7D,EACC;AAAA,MAAI,aACH,QAAQ,SAAS,aACb;AAAA,QACE,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,WAAW,QAAQ;AAAA,MACrB,IACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,MAChB;AAAA,IACN;AAEF,WAAO;AAAA,MACL;AAAA,MACA,WAAW,UAAU,SAAS,IAAI,YAAY;AAAA,MAC9C;AAAA,MACA,cAAc,uBAAuB,SAAS,WAAW;AAAA,MACzD,OAAO;AAAA,QACL,cAAc,SAAS,MAAM;AAAA,QAC7B,kBAAkB,SAAS,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,UAAU;AAAA,QACR,KAAI,cAAS,OAAT,YAAe;AAAA,QACnB,UAAS,cAAS,UAAT,YAAkB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,QAChB,WAAW;AAAA,UACT,2BACE,cAAS,MAAM,gCAAf,YAA8C;AAAA,UAChD,uBAAsB,cAAS,MAAM,4BAAf,YAA0C;AAAA,QAClE;AAAA,MACF;AAAA,MACA,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,UAAU,MAAM,IAAI,MAAM,KAAK,QAAQ,OAAO;AAC5D,UAAM,OAAO,EAAE,GAAG,MAAM,QAAQ,KAAK;AAErC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,KAAK,gBAAgB,IAAI;AAAA,MAC9B,SAAS,MAAM,KAAK,WAAW,EAAE,OAAO,SAAS,QAAQ,QAAQ,CAAC;AAAA,MAClE,MAAM,KAAK,qBAAqB,IAAI;AAAA,MACpC,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,UAAM,QAA4D;AAAA,MAChE,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AAEA,UAAM,wBAOF,CAAC;AAEL,QAAI,mBACF;AAEF,QAAI,YAKY;AAEhB,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AAvZvC;AAwZY,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,oBAAQ,MAAM,MAAM;AAAA,cAClB,KAAK,QAAQ;AACX;AAAA,cACF;AAAA,cAEA,KAAK,uBAAuB;AAC1B,sBAAM,mBAAmB,MAAM,cAAc;AAE7C,4BAAY;AAEZ,wBAAQ,kBAAkB;AAAA,kBACxB,KAAK;AAAA,kBACL,KAAK,YAAY;AACf;AAAA,kBACF;AAAA,kBAEA,KAAK,qBAAqB;AACxB,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,MAAM,MAAM,cAAc;AAAA,oBAC5B,CAAC;AACD;AAAA,kBACF;AAAA,kBAEA,KAAK,YAAY;AACf,0CAAsB,MAAM,KAAK,IAAI;AAAA,sBACnC,YAAY,MAAM,cAAc;AAAA,sBAChC,UAAU,MAAM,cAAc;AAAA,sBAC9B,UAAU;AAAA,oBACZ;AACA;AAAA,kBACF;AAAA,kBAEA,SAAS;AACP,0BAAM,mBAA0B;AAChC,0BAAM,IAAI;AAAA,sBACR,mCAAmC,gBAAgB;AAAA,oBACrD;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cAEA,KAAK,sBAAsB;AAEzB,oBAAI,sBAAsB,MAAM,KAAK,KAAK,MAAM;AAC9C,wBAAM,eAAe,sBAAsB,MAAM,KAAK;AAEtD,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,cAAc;AAAA,oBACd,YAAY,aAAa;AAAA,oBACzB,UAAU,aAAa;AAAA,oBACvB,MAAM,aAAa;AAAA,kBACrB,CAAC;AAED,yBAAO,sBAAsB,MAAM,KAAK;AAAA,gBAC1C;AAEA,4BAAY;AAEZ;AAAA,cACF;AAAA,cAEA,KAAK,uBAAuB;AAC1B,sBAAM,YAAY,MAAM,MAAM;AAC9B,wBAAQ,WAAW;AAAA,kBACjB,KAAK,cAAc;AACjB,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,WAAW,MAAM,MAAM;AAAA,oBACzB,CAAC;AAED;AAAA,kBACF;AAAA,kBAEA,KAAK,kBAAkB;AACrB,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,WAAW,MAAM,MAAM;AAAA,oBACzB,CAAC;AAED;AAAA,kBACF;AAAA,kBAEA,KAAK,mBAAmB;AAEtB,wBAAI,cAAc,YAAY;AAC5B,iCAAW,QAAQ;AAAA,wBACjB,MAAM;AAAA,wBACN,WAAW,MAAM,MAAM;AAAA,sBACzB,CAAC;AAAA,oBACH;AAEA;AAAA,kBACF;AAAA,kBAEA,KAAK,oBAAoB;AACvB,0BAAM,eAAe,sBAAsB,MAAM,KAAK;AAEtD,+BAAW,QAAQ;AAAA,sBACjB,MAAM;AAAA,sBACN,cAAc;AAAA,sBACd,YAAY,aAAa;AAAA,sBACzB,UAAU,aAAa;AAAA,sBACvB,eAAe,MAAM,MAAM;AAAA,oBAC7B,CAAC;AAED,iCAAa,YAAY,MAAM,MAAM;AAErC;AAAA,kBACF;AAAA,kBAEA,SAAS;AACP,0BAAM,mBAA0B;AAChC,0BAAM,IAAI;AAAA,sBACR,2BAA2B,gBAAgB;AAAA,oBAC7C;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cAEA,KAAK,iBAAiB;AACpB,sBAAM,eAAe,MAAM,QAAQ,MAAM;AACzC,sBAAM,mBAAmB,MAAM,QAAQ,MAAM;AAE7C,mCAAmB;AAAA,kBACjB,WAAW;AAAA,oBACT,2BACE,WAAM,QAAQ,MAAM,gCAApB,YAAmD;AAAA,oBACrD,uBACE,WAAM,QAAQ,MAAM,4BAApB,YAA+C;AAAA,kBACnD;AAAA,gBACF;AAEA,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,KAAI,WAAM,QAAQ,OAAd,YAAoB;AAAA,kBACxB,UAAS,WAAM,QAAQ,UAAd,YAAuB;AAAA,gBAClC,CAAC;AAED;AAAA,cACF;AAAA,cAEA,KAAK,iBAAiB;AACpB,sBAAM,mBAAmB,MAAM,MAAM;AACrC,+BAAe,uBAAuB,MAAM,MAAM,WAAW;AAC7D;AAAA,cACF;AAAA,cAEA,KAAK,gBAAgB;AACnB,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,SAAS;AACZ,2BAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,cACF;AAAA,cAEA,SAAS;AACP,sBAAM,mBAA0B;AAChC,sBAAM,IAAI,MAAM,2BAA2B,gBAAgB,EAAE;AAAA,cAC/D;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,MACA,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,IACxC;AAAA,EACF;AACF;AAIA,IAAM,kCAAkC,cAAE,OAAO;AAAA,EAC/C,MAAM,cAAE,QAAQ,SAAS;AAAA,EACzB,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,EACvB,OAAO,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC1B,SAAS,cAAE;AAAA,IACT,cAAE,mBAAmB,QAAQ;AAAA,MAC3B,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,MAAM;AAAA,QACtB,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,UAAU,cAAE,OAAO;AAAA,QACnB,WAAW,cAAE,OAAO;AAAA,MACtB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,mBAAmB;AAAA,QACnC,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,IAAI,cAAE,OAAO;AAAA,QACb,MAAM,cAAE,OAAO;AAAA,QACf,OAAO,cAAE,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,cAAE,OAAO,EAAE,QAAQ;AAAA,EAChC,OAAO,cAAE,OAAO;AAAA,IACd,cAAc,cAAE,OAAO;AAAA,IACvB,eAAe,cAAE,OAAO;AAAA,IACxB,6BAA6B,cAAE,OAAO,EAAE,QAAQ;AAAA,IAChD,yBAAyB,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC9C,CAAC;AACH,CAAC;AAID,IAAM,+BAA+B,cAAE,mBAAmB,QAAQ;AAAA,EAChE,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,eAAe;AAAA,IAC/B,SAAS,cAAE,OAAO;AAAA,MAChB,IAAI,cAAE,OAAO,EAAE,QAAQ;AAAA,MACvB,OAAO,cAAE,OAAO,EAAE,QAAQ;AAAA,MAC1B,OAAO,cAAE,OAAO;AAAA,QACd,cAAc,cAAE,OAAO;AAAA,QACvB,eAAe,cAAE,OAAO;AAAA,QACxB,6BAA6B,cAAE,OAAO,EAAE,QAAQ;AAAA,QAChD,yBAAyB,cAAE,OAAO,EAAE,QAAQ;AAAA,MAC9C,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,qBAAqB;AAAA,IACrC,OAAO,cAAE,OAAO;AAAA,IAChB,eAAe,cAAE,mBAAmB,QAAQ;AAAA,MAC1C,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,MAAM;AAAA,QACtB,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,UAAU,cAAE,OAAO;AAAA,MACrB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,UAAU;AAAA,QAC1B,IAAI,cAAE,OAAO;AAAA,QACb,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,mBAAmB;AAAA,QACnC,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,qBAAqB;AAAA,IACrC,OAAO,cAAE,OAAO;AAAA,IAChB,OAAO,cAAE,mBAAmB,QAAQ;AAAA,MAClC,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,kBAAkB;AAAA,QAClC,cAAc,cAAE,OAAO;AAAA,MACzB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,YAAY;AAAA,QAC5B,MAAM,cAAE,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,gBAAgB;AAAA,QAChC,UAAU,cAAE,OAAO;AAAA,MACrB,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,MAAM,cAAE,QAAQ,iBAAiB;AAAA,QACjC,WAAW,cAAE,OAAO;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,oBAAoB;AAAA,IACpC,OAAO,cAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,OAAO;AAAA,IACvB,OAAO,cAAE,OAAO;AAAA,MACd,MAAM,cAAE,OAAO;AAAA,MACf,SAAS,cAAE,OAAO;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,eAAe;AAAA,IAC/B,OAAO,cAAE,OAAO,EAAE,aAAa,cAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAAA,IACrD,OAAO,cAAE,OAAO,EAAE,eAAe,cAAE,OAAO,EAAE,CAAC;AAAA,EAC/C,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,cAAc;AAAA,EAChC,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,MAAM,cAAE,QAAQ,MAAM;AAAA,EACxB,CAAC;AACH,CAAC;AAED,IAAM,iCAAiC,cAAE,OAAO;AAAA,EAC9C,UAAU,cACP,OAAO;AAAA,IACN,MAAM,cAAE,MAAM,CAAC,cAAE,QAAQ,SAAS,GAAG,cAAE,QAAQ,UAAU,CAAC,CAAC;AAAA,IAC3D,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,CAAC,EACA,SAAS;AACd,CAAC;;;AKrtBD,IAAAC,cAAkB;AAuBlB,IAAM,yBAAyB,cAAE,OAAO;AAAA,EACtC,SAAS,cAAE,OAAO;AAAA,EAClB,SAAS,cAAE,QAAQ,EAAE,SAAS;AAChC,CAAC;AASD,SAAS,kBACP,UAgBI,CAAC,GAQL;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC;AAAA,IACP,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEA,IAAM,yBAAyB,cAAE,OAAO;AAAA,EACtC,SAAS,cAAE,OAAO;AAAA,EAClB,SAAS,cAAE,QAAQ,EAAE,SAAS;AAChC,CAAC;AASD,SAAS,kBACP,UAgBI,CAAC,GAQL;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC;AAAA,IACP,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEA,IAAM,+BAA+B,cAAE,OAAO;AAAA,EAC5C,SAAS,cAAE,KAAK,CAAC,QAAQ,UAAU,eAAe,UAAU,WAAW,CAAC;AAAA,EACxE,MAAM,cAAE,OAAO;AAAA,EACf,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,aAAa,cAAE,OAAO,EAAE,IAAI,EAAE,SAAS;AAAA,EACvC,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,YAAY,cAAE,MAAM,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS;AACjD,CAAC;AASD,SAAS,wBACP,UAyCI,CAAC,GAWL;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC;AAAA,IACP,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEA,IAAM,+BAA+B,cAAE,OAAO;AAAA,EAC5C,SAAS,cAAE,KAAK,CAAC,QAAQ,UAAU,eAAe,UAAU,WAAW,CAAC;AAAA,EACxE,MAAM,cAAE,OAAO;AAAA,EACf,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,aAAa,cAAE,OAAO,EAAE,IAAI,EAAE,SAAS;AAAA,EACvC,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,YAAY,cAAE,MAAM,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS;AACjD,CAAC;AASD,SAAS,wBACP,UAyCI,CAAC,GAWL;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM,CAAC;AAAA,IACP,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEA,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC1C,QAAQ,cAAE,KAAK;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA,EACD,YAAY,cAAE,MAAM,cAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EAC/C,MAAM,cAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAYD,SAAS,sBAA8B,SAqDrC;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM;AAAA,MACJ,gBAAgB,QAAQ;AAAA,MACxB,iBAAiB,QAAQ;AAAA,MACzB,eAAe,QAAQ;AAAA,IACzB;AAAA,IACA,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEA,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC1C,QAAQ,cAAE,KAAK;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA,EACD,YAAY,cAAE,MAAM,CAAC,cAAE,OAAO,EAAE,IAAI,GAAG,cAAE,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,SAAS;AAAA,EACnE,UAAU,cAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,kBAAkB,cAAE,KAAK,CAAC,MAAM,QAAQ,QAAQ,OAAO,CAAC,EAAE,SAAS;AAAA,EACnE,kBAAkB,cAAE,MAAM,CAAC,cAAE,OAAO,EAAE,IAAI,GAAG,cAAE,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,SAAS;AAAA,EACzE,MAAM,cAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAYD,SAAS,sBAA8B,SAoFrC;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM;AAAA,MACJ,gBAAgB,QAAQ;AAAA,MACxB,iBAAiB,QAAQ;AAAA,MACzB,eAAe,QAAQ;AAAA,IACzB;AAAA,IACA,YAAY;AAAA,IACZ,SAAS,QAAQ;AAAA,IACjB,kCAAkC,QAAQ;AAAA,EAC5C;AACF;AAEO,IAAM,iBAAiB;AAAA,EAC5B,eAAe;AAAA,EACf,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,mBAAmB;AACrB;;;ANjbO,SAAS,gBACd,UAAqC,CAAC,GACnB;AAxFrB;AAyFE,QAAM,WACJ,sDAAqB,QAAQ,OAAO,MAApC,YAAyC;AAE3C,QAAM,aAAa,OAAO;AAAA,IACxB,qBAAqB;AAAA,IACrB,iBAAa,mCAAW;AAAA,MACtB,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAAA,IACD,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAsC,CAAC,MAEvC,IAAI,+BAA+B,SAAS,UAAU;AAAA,IACpD,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,IACf,mBAAmB;AAAA,EACrB,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,WAAW;AACpB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,kCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,WAAS,QAAQ;AAEjB,SAAO;AACT;AAKO,IAAM,YAAY,gBAAgB;", "names": ["import_provider", "import_provider_utils", "import_provider", "import_provider_utils", "import_zod", "anthropicTools", "import_provider", "import_provider_utils", "_a", "anthropic", "i", "part", "import_zod"]}