#!/bin/bash

# Domain Ranking System - Monitoring Setup Script
# This script sets up monitoring infrastructure for all services

set -e

echo "🚀 Setting up Domain Ranking System Monitoring..."

# Configuration
MONITORING_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$MONITORING_DIR")"
DOCKER_COMPOSE_FILE="$MONITORING_DIR/docker-compose.monitoring.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Create monitoring directories
create_directories() {
    log_info "Creating monitoring directories..."

    mkdir -p "$MONITORING_DIR/grafana/dashboards"
    mkdir -p "$MONITORING_DIR/grafana/provisioning/dashboards"
    mkdir -p "$MONITORING_DIR/grafana/provisioning/datasources"
    mkdir -p "$MONITORING_DIR/prometheus/config"
    mkdir -p "$MONITORING_DIR/alertmanager/config"
    mkdir -p "$MONITORING_DIR/logs"

    log_success "Monitoring directories created"
}

# Generate Prometheus configuration
generate_prometheus_config() {
    log_info "Generating Prometheus configuration..."

    cat > "$MONITORING_DIR/prometheus/config/prometheus.yml" << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'web-app'
    static_configs:
      - targets: ['web-app:3000']
    metrics_path: '/health/metrics'
    scrape_interval: 30s

  - job_name: 'crawler'
    static_configs:
      - targets: ['crawler:3001']
    metrics_path: '/health/metrics'
    scrape_interval: 30s

  - job_name: 'ranking-engine'
    static_configs:
      - targets: ['ranking-engine:3002']
    metrics_path: '/health/metrics'
    scrape_interval: 30s

  - job_name: 'scheduler'
    static_configs:
      - targets: ['scheduler:3003']
    metrics_path: '/health/metrics'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'scylladb'
    static_configs:
      - targets: ['scylla:9180']

  - job_name: 'mariadb'
    static_configs:
      - targets: ['mariadb:3306']

  - job_name: 'manticore'
    static_configs:
      - targets: ['manticore:9308']
EOF

    log_success "Prometheus configuration generated"
}

# Generate alert rules
generate_alert_rules() {
    log_info "Generating Prometheus alert rules..."

    cat > "$MONITORING_DIR/prometheus/config/alert_rules.yml" << 'EOF'
groups:
  - name: domain_ranking_system
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.job }} service on {{ $labels.instance }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_errors_total[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_bucket[5m])) > 5000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}ms for {{ $labels.job }}"

      - alert: DatabaseConnectionLost
        expr: database_status != 1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection lost"
          description: "{{ $labels.database }} connection is not healthy"

      - alert: QueueBacklog
        expr: queue_size > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Job queue backlog detected"
          description: "Queue {{ $labels.queue }} has {{ $value }} pending jobs"

      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"
EOF

    log_success "Alert rules generated"
}

# Generate Alertmanager configuration
generate_alertmanager_config() {
    log_info "Generating Alertmanager configuration..."

    cat > "$MONITORING_DIR/alertmanager/config/alertmanager.yml" << 'EOF'
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/'
        send_resolved: true

  - name: 'email'
    email_configs:
      - to: '<EMAIL>'
        subject: 'Domain Ranking System Alert'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
EOF

    log_success "Alertmanager configuration generated"
}

# Generate Grafana datasource configuration
generate_grafana_datasource() {
    log_info "Generating Grafana datasource configuration..."

    cat > "$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml" << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    log_success "Grafana datasource configuration generated"
}

# Generate Grafana dashboard provisioning
generate_grafana_dashboards() {
    log_info "Generating Grafana dashboard provisioning..."

    cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboards.yml" << 'EOF'
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    # Copy dashboard configuration
    cp "$MONITORING_DIR/dashboard-config.json" "$MONITORING_DIR/grafana/dashboards/"

    log_success "Grafana dashboard provisioning generated"
}

# Generate Docker Compose for monitoring stack
generate_docker_compose() {
    log_info "Generating Docker Compose configuration for monitoring stack..."

    cat > "$DOCKER_COMPOSE_FILE" << 'EOF'
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/config:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager/config:/etc/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - monitoring

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
EOF

    log_success "Docker Compose configuration generated"
}

# Start monitoring stack
start_monitoring() {
    log_info "Starting monitoring stack..."

    cd "$MONITORING_DIR"
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d

    log_success "Monitoring stack started"
    log_info "Services available at:"
    log_info "  - Prometheus: http://localhost:9090"
    log_info "  - Grafana: http://localhost:3001 (admin/admin)"
    log_info "  - Alertmanager: http://localhost:9093"
}

# Create monitoring script
create_monitoring_script() {
    log_info "Creating monitoring management script..."

    cat > "$MONITORING_DIR/monitor.sh" << 'EOF'
#!/bin/bash

# Domain Ranking System Monitoring Management Script

MONITORING_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_COMPOSE_FILE="$MONITORING_DIR/docker-compose.monitoring.yml"

case "$1" in
    start)
        echo "Starting monitoring stack..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
        ;;
    stop)
        echo "Stopping monitoring stack..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        ;;
    restart)
        echo "Restarting monitoring stack..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" restart
        ;;
    status)
        echo "Monitoring stack status:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" ps
        ;;
    logs)
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f
        ;;
    clean)
        echo "Cleaning up monitoring stack..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down -v
        docker system prune -f
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|clean}"
        exit 1
        ;;
esac
EOF

    chmod +x "$MONITORING_DIR/monitor.sh"

    log_success "Monitoring management script created"
}

# Main execution
main() {
    log_info "Starting Domain Ranking System monitoring setup..."

    check_prerequisites
    create_directories
    generate_prometheus_config
    generate_alert_rules
    generate_alertmanager_config
    generate_grafana_datasource
    generate_grafana_dashboards
    generate_docker_compose
    create_monitoring_script

    log_success "Monitoring setup completed!"
    log_info ""
    log_info "To start the monitoring stack, run:"
    log_info "  cd $MONITORING_DIR && ./monitor.sh start"
    log_info ""
    log_info "Or start it now? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        start_monitoring
    fi
}

# Run main function
main "$@"
