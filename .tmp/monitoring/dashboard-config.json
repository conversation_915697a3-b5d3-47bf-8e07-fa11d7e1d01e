{"dashboard": {"title": "Domain Ranking System Monitoring", "description": "Comprehensive monitoring dashboard for all services", "refreshInterval": 30, "timeRange": {"default": "1h", "options": ["5m", "15m", "1h", "6h", "24h", "7d"]}}, "panels": [{"id": "system-overview", "title": "System Overview", "type": "stat", "gridPos": {"x": 0, "y": 0, "w": 24, "h": 4}, "targets": [{"metric": "system_status", "label": "Overall Status"}, {"metric": "total_domains", "label": "Total Domains"}, {"metric": "active_crawl_jobs", "label": "Active Crawl Jobs"}, {"metric": "ranking_calculations_today", "label": "Rankings Today"}]}, {"id": "service-health", "title": "Service Health Status", "type": "table", "gridPos": {"x": 0, "y": 4, "w": 12, "h": 8}, "targets": [{"services": ["web-app", "crawler", "ranking-engine", "scheduler"], "metrics": ["status", "uptime", "response_time", "error_rate"]}]}, {"id": "database-health", "title": "Database Health", "type": "stat", "gridPos": {"x": 12, "y": 4, "w": 12, "h": 8}, "targets": [{"metric": "scylladb_status", "label": "ScyllaDB"}, {"metric": "mariadb_status", "label": "MariaDB"}, {"metric": "redis_status", "label": "Redis"}, {"metric": "manticore_status", "label": "Manticore"}]}, {"id": "http-requests", "title": "HTTP Requests", "type": "graph", "gridPos": {"x": 0, "y": 12, "w": 12, "h": 8}, "targets": [{"metric": "http_requests_total", "groupBy": ["method", "status_code"], "aggregation": "rate"}], "yAxes": [{"label": "Requests/sec", "min": 0}]}, {"id": "response-times", "title": "Response Times", "type": "graph", "gridPos": {"x": 12, "y": 12, "w": 12, "h": 8}, "targets": [{"metric": "http_request_duration", "percentiles": [50, 95, 99], "aggregation": "histogram"}], "yAxes": [{"label": "Duration (ms)", "min": 0}]}, {"id": "crawl-jobs", "title": "Crawl Jobs", "type": "graph", "gridPos": {"x": 0, "y": 20, "w": 8, "h": 8}, "targets": [{"metric": "crawl_jobs_total", "groupBy": ["job_type", "success"], "aggregation": "rate"}]}, {"id": "ranking-calculations", "title": "Ranking Calculations", "type": "graph", "gridPos": {"x": 8, "y": 20, "w": 8, "h": 8}, "targets": [{"metric": "ranking_calculations_total", "groupBy": ["category"], "aggregation": "rate"}]}, {"id": "queue-sizes", "title": "Queue Sizes", "type": "graph", "gridPos": {"x": 16, "y": 20, "w": 8, "h": 8}, "targets": [{"metric": "queue_size", "groupBy": ["queue"], "aggregation": "latest"}]}, {"id": "database-operations", "title": "Database Operations", "type": "graph", "gridPos": {"x": 0, "y": 28, "w": 12, "h": 8}, "targets": [{"metric": "database_queries_total", "groupBy": ["database", "operation"], "aggregation": "rate"}]}, {"id": "cache-performance", "title": "<PERSON><PERSON>", "type": "graph", "gridPos": {"x": 12, "y": 28, "w": 12, "h": 8}, "targets": [{"metric": "cache_operations_total", "groupBy": ["operation", "result"], "aggregation": "rate"}]}, {"id": "system-resources", "title": "System Resources", "type": "graph", "gridPos": {"x": 0, "y": 36, "w": 24, "h": 8}, "targets": [{"metric": "system_memory_usage_bytes", "label": "Memory Usage"}, {"metric": "system_cpu_usage_percent", "label": "CPU Usage"}], "yAxes": [{"label": "Usage %", "min": 0, "max": 100}]}, {"id": "error-rates", "title": "Error Rates", "type": "graph", "gridPos": {"x": 0, "y": 44, "w": 12, "h": 8}, "targets": [{"metric": "http_errors_total", "groupBy": ["error_type", "status_code"], "aggregation": "rate"}]}, {"id": "external-services", "title": "External Services", "type": "stat", "gridPos": {"x": 12, "y": 44, "w": 12, "h": 8}, "targets": [{"metric": "browserless_status", "label": "Browserless"}, {"metric": "image_proxy_status", "label": "Image Proxy"}]}], "alerts": [{"name": "Service Down", "condition": "service_status != 'healthy'", "severity": "critical", "description": "One or more services are not healthy"}, {"name": "High Error Rate", "condition": "http_error_rate > 0.05", "severity": "warning", "description": "HTTP error rate is above 5%"}, {"name": "High Response Time", "condition": "http_response_time_p95 > 5000", "severity": "warning", "description": "95th percentile response time is above 5 seconds"}, {"name": "Database Connection Lost", "condition": "database_status != 'healthy'", "severity": "critical", "description": "Database connection is not healthy"}, {"name": "Queue Backlog", "condition": "queue_size > 1000", "severity": "warning", "description": "Job queue has more than 1000 pending jobs"}, {"name": "High Memory Usage", "condition": "system_memory_usage_percent > 90", "severity": "warning", "description": "System memory usage is above 90%"}, {"name": "High CPU Usage", "condition": "system_cpu_usage_percent > 80", "severity": "warning", "description": "System CPU usage is above 80%"}], "notifications": {"channels": [{"name": "email", "type": "email", "settings": {"addresses": ["<EMAIL>"]}}, {"name": "slack", "type": "slack", "settings": {"webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#alerts"}}], "rules": [{"severity": "critical", "channels": ["email", "slack"]}, {"severity": "warning", "channels": ["slack"]}]}}