import {
	DatabaseManager, logger, Config, Constants,
} from '@shared';
import { v4 as uuidv4 } from 'uuid';
import type { JobData, JobOptions } from './JobScheduler';

import JobScheduler from './JobScheduler';

interface CrawlJobRequest
{
	domain: string;
	crawlType: 'full' | 'quick' | 'security' | 'performance' | 'content';
	priority: 'low' | 'medium' | 'high';
	requestedBy?: string;
	metadata?: Record<string, any>;
}

interface CrawlJobStatus
{
	id: string;
	domain: string;
	crawlType: string;
	priority: string;
	status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
	progress: number; // 0-100
	retryCount: number;
	maxRetries: number;
	createdAt: Date;
	queuedAt?: Date;
	startedAt?: Date;
	completedAt?: Date;
	errorMessage?: string;
	result?: any;
	metadata?: Record<string, any>;
}

interface CrawlJobProgress
{
	jobId: string;
	domain: string;
	stage: string;
	progress: number;
	message: string;
	timestamp: Date;
	details?: Record<string, any>;
}

interface JobQueueStats
{
	queueName: string;
	totalJobs: number;
	pendingJobs: number;
	processingJobs: number;
	completedJobs: number;
	failedJobs: number;
	averageProcessingTime: number;
	lastProcessedAt?: Date;
}

/**
 * Crawl Job Manager
 * Handles creation, queuing, status tracking, and monitoring of domain crawl jobs
 */
class CrawlJobManager
{
	private jobScheduler: JobScheduler;

	private dbManager: DatabaseManager;

	private logger = logger.getLogger('CrawlJobManager');

	private config = Config.getAll();

	private jobStatuses = new Map<string, CrawlJobStatus>();

	private jobProgress = new Map<string, CrawlJobProgress[]>();

	private isInitialized = false;

	constructor(jobScheduler: JobScheduler)
	{
		this.jobScheduler = jobScheduler;
		this.dbManager = new DatabaseManager();
	}

	/**
	 * Initialize crawl job manager
	 */
	async initialize(): Promise<void>
	{
		try
		{
			this.logger.info('Initializing Crawl Job Manager...');

			// Initialize database connection
			await this.dbManager.initialize();

			// Load existing job statuses from database
			await this.loadJobStatuses();

			// Register job handlers
			await this.registerJobHandlers();

			this.isInitialized = true;
			this.logger.info('Crawl Job Manager initialized successfully');
		}
		catch (error)
		{
			this.logger.error('Failed to initialize Crawl Job Manager:', error);
			throw error;
		}
	}

	/**
	 * Load existing job statuses from database
	 */
	private async loadJobStatuses(): Promise<void>
	{
		try
		{
			// Query recent job statuses from ScyllaDB
			const query = `
				SELECT job_id, domain, crawl_type, priority, status, retry_count,
					   scheduled_at, started_at, completed_at, error_message
				FROM domain_crawl_jobs
				WHERE scheduled_at > ?
				ALLOW FILTERING
			`;

			// Load jobs from last 7 days
			const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
			const result = await this.dbManager.scyllaClient.execute(query, [sevenDaysAgo]);

			for (const row of result.rows)
			{
				const jobStatus: CrawlJobStatus = {
					id: row.job_id,
					domain: row.domain,
					crawlType: row.crawl_type,
					priority: row.priority,
					status: row.status,
					progress: this.calculateProgress(row.status),
					retryCount: row.retry_count || 0,
					maxRetries: 3,
					createdAt: row.scheduled_at,
					startedAt: row.started_at,
					completedAt: row.completed_at,
					errorMessage: row.error_message,
				};

				this.jobStatuses.set(jobStatus.id, jobStatus);
			}

			this.logger.info(`Loaded ${this.jobStatuses.size} existing job statuses`);
		}
		catch (error)
		{
			this.logger.error('Failed to load job statuses:', error);
			// Continue initialization even if loading fails
		}
	}

	/**
	 * Register job handlers with the job scheduler
	 */
	private async registerJobHandlers(): Promise<void>
	{
		// Register domain crawl job handler
		await this.jobScheduler.registerJobHandler(
			Constants.JOB_QUEUES.DOMAIN_CRAWL,
			this.handleDomainCrawlJob.bind(this),
		);

		this.logger.info('Job handlers registered successfully');
	}

	/**
	 * Handle domain crawl job processing
	 */
	private async handleDomainCrawlJob(jobData: JobData): Promise<void>
	{
		const jobId = jobData.id;
		const domain = jobData.domain!;

		try
		{
			// Update job status to processing
			await this.updateJobStatus(jobId, 'processing', {
				startedAt: new Date(),
			});

			// Update progress
			await this.updateJobProgress(jobId, domain, 'started', 10, 'Job processing started');

			// Simulate crawl processing stages
			await this.simulateCrawlProcessing(jobId, domain, jobData.crawlType!);

			// Mark job as completed
			await this.updateJobStatus(jobId, 'completed', {
				completedAt: new Date(),
				progress: 100,
				result: { success: true, crawledAt: new Date() },
			});

			await this.updateJobProgress(jobId, domain, 'completed', 100, 'Job completed successfully');

			this.logger.info(`Crawl job ${jobId} for domain ${domain} completed successfully`);
		}
		catch (error)
		{
			this.logger.error(`Crawl job ${jobId} for domain ${domain} failed:`, error);

			// Update job status to failed
			await this.updateJobStatus(jobId, 'failed', {
				errorMessage: (error as Error).message,
				completedAt: new Date(),
			});

			await this.updateJobProgress(
				jobId,
				domain,
				'failed',
				0,
				`Job failed: ${(error as Error).message}`,
			);

			throw error; // Re-throw to trigger retry mechanism
		}
	}

	/**
	 * Simulate crawl processing stages
	 */
	private async simulateCrawlProcessing(
		jobId: string,
		domain: string,
		crawlType: string,
	): Promise<void>
	{
		const stages = this.getCrawlStages(crawlType);

		for (let i = 0; i < stages.length; i++)
		{
			const stage = stages[i];
			const progress = Math.round(((i + 1) / stages.length) * 90) + 10; // 10-100%

			await this.updateJobProgress(
				jobId,
				domain,
				stage.name,
				progress,
				stage.description,
			);

			// Simulate processing time
			await this.delay(stage.duration);

			this.logger.debug(`Job ${jobId}: Completed stage ${stage.name} (${progress}%)`);
		}
	}

	/**
	 * Get crawl stages based on crawl type
	 */
	private getCrawlStages(crawlType: string): Array<{ name: string; description: string; duration: number }>
	{
		const baseStages = [
			{ name: 'dns_lookup', description: 'Performing DNS lookup', duration: 500 },
			{ name: 'robots_check', description: 'Checking robots.txt', duration: 300 },
			{ name: 'ssl_analysis', description: 'Analyzing SSL certificate', duration: 400 },
		];

		switch (crawlType)
		{
			case 'full':
				return [
					...baseStages,
					{ name: 'homepage_analysis', description: 'Analyzing homepage', duration: 1000 },
					{ name: 'screenshot_capture', description: 'Capturing screenshot', duration: 2000 },
					{ name: 'performance_audit', description: 'Running performance audit', duration: 3000 },
					{ name: 'content_analysis', description: 'Analyzing content', duration: 1500 },
					{ name: 'finalization', description: 'Finalizing results', duration: 500 },
				];

			case 'quick':
				return [
					...baseStages,
					{ name: 'basic_analysis', description: 'Basic website analysis', duration: 800 },
					{ name: 'finalization', description: 'Finalizing results', duration: 200 },
				];

			case 'security':
				return [
					...baseStages,
					{ name: 'security_headers', description: 'Analyzing security headers', duration: 600 },
					{ name: 'vulnerability_scan', description: 'Scanning for vulnerabilities', duration: 2000 },
					{ name: 'finalization', description: 'Finalizing results', duration: 300 },
				];

			case 'performance':
				return [
					...baseStages,
					{ name: 'core_web_vitals', description: 'Measuring Core Web Vitals', duration: 3000 },
					{ name: 'resource_analysis', description: 'Analyzing resources', duration: 1500 },
					{ name: 'finalization', description: 'Finalizing results', duration: 400 },
				];

			case 'content':
				return [
					...baseStages,
					{ name: 'content_extraction', description: 'Extracting content', duration: 1200 },
					{ name: 'seo_analysis', description: 'Analyzing SEO factors', duration: 800 },
					{ name: 'finalization', description: 'Finalizing results', duration: 300 },
				];

			default:
				return baseStages;
		}
	}

	/**
	 * Create and queue a new crawl job
	 */
	async createCrawlJob(request: CrawlJobRequest): Promise<string>
	{
		if (!this.isInitialized)
		{
			throw new Error('Crawl Job Manager not initialized');
		}

		try
		{
			const jobId = uuidv4();
			const now = new Date();

			// Create job status record
			const jobStatus: CrawlJobStatus = {
				id: jobId,
				domain: request.domain,
				crawlType: request.crawlType,
				priority: request.priority,
				status: 'pending',
				progress: 0,
				retryCount: 0,
				maxRetries: 3,
				createdAt: now,
				metadata: request.metadata,
			};

			// Store job status
			this.jobStatuses.set(jobId, jobStatus);

			// Save to database
			await this.saveJobStatus(jobStatus);

			// Queue the job
			await this.jobScheduler.scheduleJob(
				Constants.JOB_QUEUES.DOMAIN_CRAWL,
				{
					id: jobId,
					domain: request.domain,
					crawlType: request.crawlType,
					priority: request.priority,
					metadata: request.metadata,
				},
				{
					priority: request.priority,
					maxRetries: 3,
				},
			);

			// Update status to queued
			await this.updateJobStatus(jobId, 'queued', {
				queuedAt: new Date(),
			});

			this.logger.info(`Crawl job created and queued: ${jobId} for domain ${request.domain}`);
			return jobId;
		}
		catch (error)
		{
			this.logger.error(`Failed to create crawl job for domain ${request.domain}:`, error);
			throw error;
		}
	}

	/**
	 * Create multiple crawl jobs in batch
	 */
	async createBatchCrawlJobs(requests: CrawlJobRequest[]): Promise<string[]>
	{
		const jobIds: string[] = [];

		for (const request of requests)
		{
			try
			{
				const jobId = await this.createCrawlJob(request);
				jobIds.push(jobId);
			}
			catch (error)
			{
				this.logger.error(`Failed to create batch crawl job for domain ${request.domain}:`, error);
				// Continue with other jobs even if one fails
			}
		}

		this.logger.info(`Created ${jobIds.length} out of ${requests.length} batch crawl jobs`);
		return jobIds;
	}

	/**
	 * Get job status by ID
	 */
	getJobStatus(jobId: string): CrawlJobStatus | undefined
	{
		return this.jobStatuses.get(jobId);
	}

	/**
	 * Get job statuses by domain
	 */
	getJobStatusesByDomain(domain: string): CrawlJobStatus[]
	{
		return Array.from(this.jobStatuses.values())
			.filter(job => job.domain === domain)
			.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
	}

	/**
	 * Get job progress history
	 */
	getJobProgress(jobId: string): CrawlJobProgress[]
	{
		return this.jobProgress.get(jobId) || [];
	}

	/**
	 * Update job status
	 */
	async updateJobStatus(
		jobId: string,
		status: CrawlJobStatus['status'],
		updates: Partial<CrawlJobStatus> = {},
	): Promise<void>
	{
		const jobStatus = this.jobStatuses.get(jobId);
		if (!jobStatus)
		{
			this.logger.warn(`Job status not found for job ID: ${jobId}`);
			return;
		}

		// Update status
		jobStatus.status = status;
		jobStatus.progress = updates.progress ?? this.calculateProgress(status);

		// Apply other updates
		Object.assign(jobStatus, updates);

		// Save to database
		await this.saveJobStatus(jobStatus);

		this.logger.debug(`Job status updated: ${jobId} -> ${status}`);
	}

	/**
	 * Update job progress
	 */
	async updateJobProgress(
		jobId: string,
		domain: string,
		stage: string,
		progress: number,
		message: string,
		details?: Record<string, any>,
	): Promise<void>
	{
		const progressEntry: CrawlJobProgress = {
			jobId,
			domain,
			stage,
			progress,
			message,
			timestamp: new Date(),
			details,
		};

		// Add to progress history
		if (!this.jobProgress.has(jobId))
		{
			this.jobProgress.set(jobId, []);
		}
		this.jobProgress.get(jobId)!.push(progressEntry);

		// Keep only last 50 progress entries per job
		const progressHistory = this.jobProgress.get(jobId)!;
		if (progressHistory.length > 50)
		{
			progressHistory.splice(0, progressHistory.length - 50);
		}

		// Update job status progress
		const jobStatus = this.jobStatuses.get(jobId);
		if (jobStatus)
		{
			jobStatus.progress = progress;
		}

		this.logger.debug(`Job progress updated: ${jobId} -> ${stage} (${progress}%): ${message}`);
	}

	/**
	 * Cancel a job
	 */
	async cancelJob(jobId: string): Promise<boolean>
	{
		const jobStatus = this.jobStatuses.get(jobId);
		if (!jobStatus)
		{
			return false;
		}

		if (jobStatus.status === 'processing')
		{
			this.logger.warn(`Cannot cancel job ${jobId} - already processing`);
			return false;
		}

		await this.updateJobStatus(jobId, 'cancelled', {
			completedAt: new Date(),
		});

		this.logger.info(`Job cancelled: ${jobId}`);
		return true;
	}

	/**
	 * Retry a failed job
	 */
	async retryJob(jobId: string): Promise<boolean>
	{
		const jobStatus = this.jobStatuses.get(jobId);
		if (!jobStatus || jobStatus.status !== 'failed')
		{
			return false;
		}

		if (jobStatus.retryCount >= jobStatus.maxRetries)
		{
			this.logger.warn(`Cannot retry job ${jobId} - max retries exceeded`);
			return false;
		}

		try
		{
			// Reset job status
			await this.updateJobStatus(jobId, 'pending', {
				retryCount: jobStatus.retryCount + 1,
				errorMessage: undefined,
				startedAt: undefined,
				completedAt: undefined,
				progress: 0,
			});

			// Requeue the job
			await this.jobScheduler.scheduleJob(
				Constants.JOB_QUEUES.DOMAIN_CRAWL,
				{
					id: jobId,
					domain: jobStatus.domain,
					crawlType: jobStatus.crawlType,
					priority: jobStatus.priority,
					metadata: jobStatus.metadata,
				},
				{
					priority: jobStatus.priority as any,
					maxRetries: jobStatus.maxRetries - jobStatus.retryCount,
				},
			);

			await this.updateJobStatus(jobId, 'queued', {
				queuedAt: new Date(),
			});

			this.logger.info(`Job retried: ${jobId} (attempt ${jobStatus.retryCount + 1})`);
			return true;
		}
		catch (error)
		{
			this.logger.error(`Failed to retry job ${jobId}:`, error);
			return false;
		}
	}

	/**
	 * Get queue statistics
	 */
	async getQueueStats(): Promise<JobQueueStats[]>
	{
		const stats: JobQueueStats[] = [];
		const queueNames = Object.values(Constants.JOB_QUEUES);

		for (const queueName of queueNames)
		{
			const jobStats = this.jobScheduler.getJobStats(queueName);
			if (jobStats)
			{
				stats.push({
					queueName,
					totalJobs: jobStats.completedJobs + jobStats.failedJobs + jobStats.pendingJobs + jobStats.processingJobs,
					pendingJobs: jobStats.pendingJobs,
					processingJobs: jobStats.processingJobs,
					completedJobs: jobStats.completedJobs,
					failedJobs: jobStats.failedJobs,
					averageProcessingTime: 0, // Would need to calculate from historical data
					lastProcessedAt: undefined, // Would need to track this
				});
			}
		}

		return stats;
	}

	/**
	 * Get job statistics by status
	 */
	getJobStatsByStatus(): Record<string, number>
	{
		const stats: Record<string, number> = {
			pending: 0,
			queued: 0,
			processing: 0,
			completed: 0,
			failed: 0,
			cancelled: 0,
		};

		for (const jobStatus of this.jobStatuses.values())
		{
			stats[jobStatus.status] = (stats[jobStatus.status] || 0) + 1;
		}

		return stats;
	}

	/**
	 * Clean up old job records
	 */
	async cleanupOldJobs(olderThanDays: number = 30): Promise<number>
	{
		const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
		let cleanedCount = 0;

		// Clean up in-memory records
		for (const [jobId, jobStatus] of this.jobStatuses)
		{
			if (jobStatus.createdAt < cutoffDate &&
				(jobStatus.status === 'completed' || jobStatus.status === 'failed' || jobStatus.status === 'cancelled'))
			{
				this.jobStatuses.delete(jobId);
				this.jobProgress.delete(jobId);
				cleanedCount++;
			}
		}

		// Clean up database records
		try
		{
			const deleteQuery = `
				DELETE FROM domain_crawl_jobs
				WHERE scheduled_at < ? AND status IN ('completed', 'failed', 'cancelled')
			`;
			await this.dbManager.scyllaClient.execute(deleteQuery, [cutoffDate]);
		}
		catch (error)
		{
			this.logger.error('Failed to cleanup old jobs from database:', error);
		}

		this.logger.info(`Cleaned up ${cleanedCount} old job records`);
		return cleanedCount;
	}

	/**
	 * Save job status to database
	 */
	private async saveJobStatus(jobStatus: CrawlJobStatus): Promise<void>
	{
		try
		{
			const query = `
				INSERT INTO domain_crawl_jobs (
					job_id, domain, crawl_type, priority, status, retry_count,
					scheduled_at, started_at, completed_at, error_message, user_agent
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			const values = [
				jobStatus.id,
				jobStatus.domain,
				jobStatus.crawlType,
				jobStatus.priority,
				jobStatus.status,
				jobStatus.retryCount,
				jobStatus.createdAt,
				jobStatus.startedAt || null,
				jobStatus.completedAt || null,
				jobStatus.errorMessage || null,
				'domain-ranking-crawler/1.0',
			];

			await this.dbManager.scyllaClient.execute(query, values);
		}
		catch (error)
		{
			this.logger.error(`Failed to save job status for job ${jobStatus.id}:`, error);
			// Don't throw - continue operation even if database save fails
		}
	}

	/**
	 * Calculate progress percentage based on status
	 */
	private calculateProgress(status: string): number
	{
		switch (status)
		{
			case 'pending': return 0;
			case 'queued': return 5;
			case 'processing': return 50;
			case 'completed': return 100;
			case 'failed': return 0;
			case 'cancelled': return 0;
			default: return 0;
		}
	}

	/**
	 * Health check for crawl job manager
	 */
	async healthCheck(): Promise<{
		status: 'healthy' | 'unhealthy';
		activeJobs: number;
		queueStats: JobQueueStats[];
		lastJobProcessed?: Date;
	}>
	{
		try
		{
			const activeJobs = Array.from(this.jobStatuses.values())
				.filter(job => job.status === 'processing' || job.status === 'queued').length;

			const queueStats = await this.getQueueStats();

			const lastCompletedJob = Array.from(this.jobStatuses.values())
				.filter(job => job.completedAt)
				.sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))[0];

			return {
				status: 'healthy',
				activeJobs,
				queueStats,
				lastJobProcessed: lastCompletedJob?.completedAt,
			};
		}
		catch (error)
		{
			this.logger.error('Health check failed:', error);
			return {
				status: 'unhealthy',
				activeJobs: 0,
				queueStats: [],
			};
		}
	}

	/**
	 * Shutdown crawl job manager
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			this.logger.info('Shutting down Crawl Job Manager...');

			// Save any pending job statuses
			const savePromises = Array.from(this.jobStatuses.values())
				.map(jobStatus => this.saveJobStatus(jobStatus));

			await Promise.allSettled(savePromises);

			// Close database connection
			await this.dbManager.close();

			// Clear collections
			this.jobStatuses.clear();
			this.jobProgress.clear();

			this.isInitialized = false;
			this.logger.info('Crawl Job Manager shut down successfully');
		}
		catch (error)
		{
			this.logger.error('Error shutting down Crawl Job Manager:', error);
			throw error;
		}
	}

	/**
	 * Delay utility
	 */
	private delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

export type {
	CrawlJobRequest,
	CrawlJobStatus,
	CrawlJobProgress,
	JobQueueStats,
};

export { CrawlJobManager };

export default CrawlJobManager;
