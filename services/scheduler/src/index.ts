import {
	DatabaseManager, logger, Config, Constants,
} from '@shared';
import * as cron from 'node-cron';
import JobScheduler from './JobScheduler';
import CrawlJobManager, { CrawlJobRequest } from './CrawlJobManager';

const schedulerLogger = logger.getLogger('Scheduler');
const config = Config;

type ScheduledJob = {
	id: string;
	name: string;
	schedule: string;
	enabled: boolean;
	lastRun?: Date;
	nextRun?: Date;
};

type HealthCheck = {
	service: string;
	status: string;
	databases: any;
	scheduledJobs: ScheduledJob[];
	jobScheduler: any;
	crawlJobManager: any;
	timestamp: string;
};

/**
 * Scheduler Service
 * Handles job scheduling and management for domain crawling and ranking
 */
class SchedulerService
{
	private dbManager: DatabaseManager;

	private jobScheduler: JobScheduler;

	private crawlJobManager: CrawlJobManager;

	private isRunning: boolean;

	private scheduledJobs: Map<string, cron.ScheduledTask>;

	constructor()
	{
		this.dbManager = new DatabaseManager();
		this.jobScheduler = new JobScheduler();
		this.crawlJobManager = new CrawlJobManager(this.jobScheduler);
		this.isRunning = false;
		this.scheduledJobs = new Map();
	}

	/**
	 * Initialize the scheduler service
	 */
	async initialize(): Promise<void>
	{
		try
		{
			logger.info('Initializing Scheduler Service...');

			// Validate configuration
			config.validate();

			// Initialize database connections
			await this.dbManager.initialize();

			// Initialize job scheduler
			await this.jobScheduler.initialize();

			// Initialize crawl job manager
			await this.crawlJobManager.initialize();

			// Setup scheduled jobs
			await this.setupScheduledJobs();

			logger.info('Scheduler Service initialized successfully');
		}
		catch (error)
		{
			logger.error('Failed to initialize Scheduler Service:', error);
			throw error;
		}
	}

	/**
	 * Setup scheduled jobs
	 */
	async setupScheduledJobs(): Promise<void>
	{
		// Daily domain crawl scheduling
		this.scheduleJob('daily-crawl-scheduling', '0 2 * * *', async () =>
		{
			await this.scheduleDailyCrawls();
		});

		// Hourly priority domain crawls
		this.scheduleJob('priority-crawl-scheduling', '0 * * * *', async () =>
		{
			await this.schedulePriorityCrawls();
		});

		// Weekly ranking recalculation
		this.scheduleJob('weekly-ranking-update', '0 3 * * 0', async () =>
		{
			await this.scheduleRankingUpdates();
		});

		// Cleanup old jobs
		this.scheduleJob('cleanup-old-jobs', '0 4 * * *', async () =>
		{
			await this.cleanupOldJobs();
		});

		logger.info('Scheduled jobs set up successfully');
	}

	/**
	 * Schedule a cron job
	 */
	scheduleJob(name: string, schedule: string, task: () => Promise<void>): void
	{
		const scheduledTask = cron.schedule(schedule, async () =>
		{
			logger.info(`Running scheduled job: ${name}`);
			try
			{
				await task();
				logger.info(`Completed scheduled job: ${name}`);
			}
			catch (error)
			{
				logger.error(`Failed scheduled job: ${name}:`, error);
			}
		}, {
			scheduled: false,
			timezone: config.get('TIMEZONE', 'UTC'),
		});

		this.scheduledJobs.set(name, scheduledTask);
		logger.info(`Scheduled job registered: ${name} (${schedule})`);
	}

	/**
	 * Schedule daily domain crawls
	 */
	async scheduleDailyCrawls(): Promise<void>
	{
		logger.info('Scheduling daily domain crawls...');

		try
		{
			// Get domains that need crawling
			const domains = await this.getDomainsForCrawling();

			const crawlRequests: CrawlJobRequest[] = domains.map(domain => ({
				domain: domain.name,
				crawlType: 'full',
				priority: (domain.priority === 'high' ? 'high' : domain.priority === 'low' ? 'low' : 'medium') as 'low' | 'medium' | 'high',
				requestedBy: 'scheduler',
				metadata: { scheduledType: 'daily' },
			}));

			const jobIds = await this.crawlJobManager.createBatchCrawlJobs(crawlRequests);
			logger.info(`Scheduled ${jobIds.length} daily crawl jobs`);
		}
		catch (error)
		{
			logger.error('Failed to schedule daily crawls:', error);
			throw error;
		}
	}

	/**
	 * Schedule priority domain crawls
	 */
	async schedulePriorityCrawls(): Promise<void>
	{
		logger.info('Scheduling priority domain crawls...');

		try
		{
			// Get high-priority domains
			const priorityDomains = await this.getPriorityDomainsForCrawling();

			const crawlRequests: CrawlJobRequest[] = priorityDomains.map(domain => ({
				domain: domain.name,
				crawlType: 'quick',
				priority: 'high',
				requestedBy: 'scheduler',
				metadata: { scheduledType: 'priority' },
			}));

			const jobIds = await this.crawlJobManager.createBatchCrawlJobs(crawlRequests);
			logger.info(`Scheduled ${jobIds.length} priority crawl jobs`);
		}
		catch (error)
		{
			logger.error('Failed to schedule priority crawls:', error);
			throw error;
		}
	}

	/**
	 * Schedule ranking updates
	 */
	async scheduleRankingUpdates(): Promise<void>
	{
		logger.info('Scheduling ranking updates...');

		try
		{
			// Get domains that need ranking updates
			const domains = await this.getDomainsForRankingUpdate();

			const jobIds: string[] = [];
			for (const domain of domains)
			{
				const jobId = await this.jobScheduler.scheduleRankingUpdateJob(
					domain.name,
					'full',
					'medium',
				);
				jobIds.push(jobId);
			}

			logger.info(`Scheduled ${jobIds.length} ranking update jobs`);
		}
		catch (error)
		{
			logger.error('Failed to schedule ranking updates:', error);
			throw error;
		}
	}

	/**
	 * Cleanup old jobs
	 */
	async cleanupOldJobs(): Promise<void>
	{
		logger.info('Cleaning up old jobs...');

		try
		{
			// Cleanup old crawl jobs (older than 30 days)
			const cleanedCount = await this.crawlJobManager.cleanupOldJobs(30);
			logger.info(`Old jobs cleanup completed - removed ${cleanedCount} jobs`);
		}
		catch (error)
		{
			logger.error('Failed to cleanup old jobs:', error);
			throw error;
		}
	}

	/**
	 * Get domains that need crawling
	 */
	async getDomainsForCrawling(): Promise<Array<{ name: string; priority?: string }>>
	{
		try
		{
			// Placeholder implementation
			// In real implementation, this would query the database
			return [
				{ name: 'example.com', priority: 'normal' },
				{ name: 'test.com', priority: 'high' },
			];
		}
		catch (error)
		{
			logger.error('Failed to get domains for crawling:', error);
			throw error;
		}
	}

	/**
	 * Get priority domains for crawling
	 */
	async getPriorityDomainsForCrawling(): Promise<Array<{ name: string }>>
	{
		try
		{
			// Placeholder implementation
			// In real implementation, this would query high-priority domains
			return [
				{ name: 'priority-example.com' },
			];
		}
		catch (error)
		{
			logger.error('Failed to get priority domains:', error);
			throw error;
		}
	}

	/**
	 * Get domains that need ranking updates
	 */
	async getDomainsForRankingUpdate(): Promise<Array<{ name: string }>>
	{
		try
		{
			// Placeholder implementation
			// In real implementation, this would query domains needing ranking updates
			return [
				{ name: 'example.com' },
				{ name: 'test.com' },
			];
		}
		catch (error)
		{
			logger.error('Failed to get domains for ranking update:', error);
			throw error;
		}
	}

	/**
	 * Start all scheduled jobs
	 */
	startScheduledJobs(): void
	{
		for (const [name, task] of this.scheduledJobs)
		{
			task.start();
			logger.info(`Started scheduled job: ${name}`);
		}
	}

	/**
	 * Stop all scheduled jobs
	 */
	stopScheduledJobs(): void
	{
		for (const [name, task] of this.scheduledJobs)
		{
			task.stop();
			logger.info(`Stopped scheduled job: ${name}`);
		}
	}

	/**
	 * Start the scheduler service
	 */
	async start(): Promise<void>
	{
		try
		{
			this.isRunning = true;
			this.startScheduledJobs();
			logger.info('Scheduler Service started and jobs are running');

			// Keep the service running
			while (this.isRunning)
			{
				await this.delay(1000);
			}
		}
		catch (error)
		{
			logger.error('Error in scheduler service:', error);
			throw error;
		}
	}

	/**
	 * Stop the scheduler service
	 */
	async stop(): Promise<void>
	{
		this.isRunning = false;
		this.stopScheduledJobs();
		logger.info('Scheduler Service stopped');
	}

	/**
	 * Graceful shutdown
	 */
	async shutdown(): Promise<void>
	{
		try
		{
			logger.info('Shutting down Scheduler Service...');
			await this.stop();
			await this.crawlJobManager.shutdown();
			await this.jobScheduler.shutdown();
			await this.dbManager.close();
			logger.info('Scheduler Service shut down successfully');
		}
		catch (error)
		{
			logger.error('Error during shutdown:', error);
			throw error;
		}
	}

	/**
	 * Health check
	 */
	async healthCheck(): Promise<HealthCheck>
	{
		const scheduledJobsInfo: ScheduledJob[] = [];

		for (const [name, task] of this.scheduledJobs)
		{
			scheduledJobsInfo.push({
				id: name,
				name,
				schedule: 'configured',
				enabled: task.running || false,
			});
		}

		const health: HealthCheck = {
			service: 'scheduler',
			status: this.isRunning ? 'running' : 'stopped',
			databases: await this.dbManager.healthCheck(),
			scheduledJobs: scheduledJobsInfo,
			jobScheduler: await this.jobScheduler.healthCheck(),
			crawlJobManager: await this.crawlJobManager.healthCheck(),
			timestamp: new Date().toISOString(),
		};

		return health;
	}

	/**
	 * Delay utility
	 */
	delay(ms: number): Promise<void>
	{
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

// Initialize and start the service
async function main(): Promise<void>
{
	const scheduler = new SchedulerService();

	try
	{
		await scheduler.initialize();

		// Graceful shutdown handling
		process.on('SIGTERM', async () =>
		{
			await scheduler.shutdown();
			process.exit(0);
		});

		process.on('SIGINT', async () =>
		{
			await scheduler.shutdown();
			process.exit(0);
		});

		await scheduler.start();
	}
	catch (error)
	{
		logger.error('Failed to start Scheduler Service:', error);
		process.exit(1);
	}
}

// Start the service if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`)
{
	main();
}

export default SchedulerService;
