import type {
	DiscoveredDomain,
	SnapshotStore,
	DomainSnapshot,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidate } from '../../interfaces/SourceConnector';

import { BaseStrategyProcessor } from './BaseStrategyProcessor';

/**
 * Differential Analysis Strategy Processor
 * Identifies domains that newly appear in ranking lists by comparing
 * current snapshots with historical snapshots
 */
class DifferentialAnalysisProcessor extends BaseStrategyProcessor
{
	constructor()
	{
		super('differential');
	}

	/**
	 * Process candidates using differential analysis
	 * Compares current candidates against historical snapshots to find new entries
	 */
	async process(
		candidates: DomainCandidate[],
		snapshotStore: SnapshotStore,
	): Promise<DiscoveredDomain[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info('Starting differential analysis', {
				candidateCount: candidates.length,
			});

			// Validate candidates
			const validCandidates = this.validateCandidates(candidates);
			if (validCandidates.length === 0)
			{
				this.logger.warn('No valid candidates for differential analysis');
				return [];
			}

			// Group candidates by source
			const candidatesBySource = this.groupCandidatesBySource(validCandidates);
			const discovered: DiscoveredDomain[] = [];

			// Process each source separately
			const sourcePromises = Array.from(candidatesBySource.entries()).map(
				async ([source, sourceCandidates]) => this.processSourceCandidates(
					source,
					sourceCandidates,
					snapshotStore,
				),
			);

			const sourceResults = await Promise.allSettled(sourcePromises);
			for (const result of sourceResults)
			{
				if (result.status === 'fulfilled')
				{
					discovered.push(...result.value);
				}
			}

			// Store current snapshots for future comparisons
			await this.storeCurrentSnapshots(candidatesBySource, snapshotStore);

			const processingTime = Date.now() - startTime;
			this.logResults(validCandidates.length, discovered.length, processingTime, {
				sourceCount: candidatesBySource.size,
			});

			return discovered;
		}
		catch (error)
		{
			this.handleError(error as Error, {
				candidateCount: candidates.length,
			});
		}
	}

	/**
	 * Process candidates for a specific source
	 */
	private async processSourceCandidates(
		source: string,
		candidates: DomainCandidate[],
		snapshotStore: SnapshotStore,
	): Promise<DiscoveredDomain[]>
	{
		try
		{
			// Get historical snapshot (yesterday)
			const yesterday = this.getYesterdayDate();
			const historicalSnapshot = await snapshotStore.getSnapshot(source, yesterday);

			if (!historicalSnapshot)
			{
				this.logger.info('No historical snapshot found for differential analysis', {
					source,
					date: yesterday,
				});
				// First run - no comparison possible
				return [];
			}

			// Find new domains not in historical snapshot
			const discovered = this.findNewDomains(candidates, historicalSnapshot);

			this.logger.debug('Source differential analysis completed', {
				source,
				inputCandidates: candidates.length,
				historicalDomains: historicalSnapshot.domains.length,
				newDomains: discovered.length,
			});

			return discovered;
		}
		catch (error)
		{
			this.logger.error('Failed to process source candidates', {
				source,
				candidateCount: candidates.length,
				error: error instanceof Error ? error.message : String(error),
			});
			return [];
		}
	}

	/**
	 * Find domains in current candidates that weren't in historical snapshot
	 */
	private findNewDomains(
		currentCandidates: DomainCandidate[],
		historicalSnapshot: DomainSnapshot,
	): DiscoveredDomain[]
	{
		// Create set of historical domains for fast lookup
		const historicalDomains = new Set(
			historicalSnapshot.domains.map(d => d.domain.toLowerCase()),
		);

		const discovered: DiscoveredDomain[] = [];

		for (const candidate of currentCandidates)
		{
			const domain = candidate.domain.toLowerCase();

			if (!historicalDomains.has(domain))
			{
				// This is a new domain not in historical snapshot
				const confidence = this.calculateDifferentialConfidence(candidate, historicalSnapshot);
				const discoveryReason = this.buildDiscoveryReason(candidate, historicalSnapshot);

				discovered.push(this.createDiscoveredDomain(
					candidate,
					confidence,
					discoveryReason,
				));
			}
		}

		return discovered;
	}

	/**
	 * Calculate confidence for differential discovery
	 */
	private calculateDifferentialConfidence(
		candidate: DomainCandidate,
		historicalSnapshot: DomainSnapshot,
	): number
	{
		const factors = {
			baseConfidence: 0.7, // Base confidence for differential analysis
			rank: candidate.rank,
			sourceReliability: this.getSourceReliability(candidate.source),
			hasMetadata: !!(candidate.metadata && Object.keys(candidate.metadata).length > 0),
			isRecent: this.isRecentSnapshot(historicalSnapshot),
		};

		return this.calculateConfidence(factors);
	}

	/**
	 * Build discovery reason for differential analysis
	 */
	private buildDiscoveryReason(
		candidate: DomainCandidate,
		historicalSnapshot: DomainSnapshot,
	): string
	{
		const rankInfo = candidate.rank ? ` at rank ${candidate.rank}` : '';
		const dateInfo = historicalSnapshot.date;

		return `New entry in ${candidate.source} rankings${rankInfo} (not in snapshot from ${dateInfo})`;
	}

	/**
	 * Group candidates by source for separate processing
	 */
	private groupCandidatesBySource(
		candidates: DomainCandidate[],
	): Map<string, DomainCandidate[]>
	{
		const grouped = new Map<string, DomainCandidate[]>();

		for (const candidate of candidates)
		{
			const source = candidate.source;
			if (!grouped.has(source))
			{
				grouped.set(source, []);
			}
			grouped.get(source)!.push(candidate);
		}

		return grouped;
	}

	/**
	 * Store current snapshots for future differential analysis
	 */
	private async storeCurrentSnapshots(
		candidatesBySource: Map<string, DomainCandidate[]>,
		snapshotStore: SnapshotStore,
	): Promise<void>
	{
		const storePromises = Array.from(candidatesBySource.entries()).map(
			async ([source, candidates]) =>
			{
				try
				{
					await snapshotStore.storeSnapshot(source, candidates);
					this.logger.debug('Snapshot stored for future differential analysis', {
						source,
						domainCount: candidates.length,
					});
				}
				catch (error)
				{
					this.logger.error('Failed to store snapshot', {
						source,
						error: error instanceof Error ? error.message : String(error),
					});
				}
			},
		);

		await Promise.allSettled(storePromises);
	}

	/**
	 * Get yesterday's date in YYYY-MM-DD format
	 */
	private getYesterdayDate(): string
	{
		const yesterday = new Date();
		yesterday.setDate(yesterday.getDate() - 1);
		return yesterday.toISOString().split('T')[0];
	}

	/**
	 * Get source reliability score (0-1)
	 */
	private getSourceReliability(source: string): number
	{
		const reliabilityMap: Record<string, number> = {
			tranco: 0.95,
			radar: 0.90,
			umbrella: 0.85,
			czds: 0.80,
			'common-crawl': 0.70,
			sonar: 0.65,
		};

		return reliabilityMap[source.toLowerCase()] || 0.5;
	}

	/**
	 * Check if snapshot is recent (within last 3 days)
	 */
	private isRecentSnapshot(snapshot: DomainSnapshot): boolean
	{
		const snapshotDate = new Date(snapshot.date);
		const threeDaysAgo = new Date();
		threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

		return snapshotDate >= threeDaysAgo;
	}
}

export { DifferentialAnalysisProcessor };

export default DifferentialAnalysisProcessor;
