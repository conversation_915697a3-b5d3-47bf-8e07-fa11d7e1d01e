import type {
	DiscoveredDomain,
	SnapshotStore,
	DomainSnapshot,
} from '../../interfaces/DiscoveryEngine';
import type { DomainCandidate } from '../../interfaces/SourceConnector';
import { BaseStrategyProcessor } from './BaseStrategyProcessor';

interface RankingHistory
{
	domain: string;
	source: string;
	rankings: Array<{
		rank: number;
		date: string;
	}>;
}

interface TemporalConfig
{
	minHistoryDays: number;
	improvementThreshold: number; // 0.5 = 50% improvement
	maxRankForAnalysis: number;
	timeWindowDays: number;
}

/**
 * Temporal Analysis Strategy Processor
 * Identifies rapidly rising domains by tracking ranking velocity
 * and detecting domains with significant upward movement (50%+ improvement)
 */
class TemporalAnalysisProcessor extends BaseStrategyProcessor
{
	private readonly config: TemporalConfig;

	constructor(config?: Partial<TemporalConfig>)
	{
		super('temporal');

		this.config = {
			minHistoryDays: 2, // Minimum days of history required
			improvementThreshold: 0.5, // 50% improvement threshold
			maxRankForAnalysis: 5000000, // Don't analyze domains ranked worse than 5M
			timeWindowDays: 7, // Look back 7 days for comparison
			...config,
		};

		this.logger.info('TemporalAnalysisProcessor initialized', {
			config: this.config,
		});
	}

	/**
	 * Process candidates using temporal analysis
	 * Identifies domains with significant ranking improvements over time
	 */
	async process(
		candidates: DomainCandidate[],
		snapshotStore: SnapshotStore,
	): Promise<DiscoveredDomain[]>
	{
		const startTime = Date.now();

		try
		{
			this.logger.info('Starting temporal analysis', {
				candidateCount: candidates.length,
				config: this.config,
			});

			// Validate candidates
			const validCandidates = this.validateCandidates(candidates);
			if (validCandidates.length === 0)
			{
				this.logger.warn('No valid candidates for temporal analysis');
				return [];
			}

			// Group candidates by source for separate analysis
			const candidatesBySource = this.groupCandidatesBySource(validCandidates);
			const discovered: DiscoveredDomain[] = [];

			// Process each source separately
			const sourceProcessingPromises = Array.from(candidatesBySource.entries()).map(
				async ([source, sourceCandidates]) =>
				{
					try
					{
						return await this.processSourceCandidates(
							source,
							sourceCandidates,
							snapshotStore,
						);
					}
					catch (error)
					{
						this.logger.error('Failed to process source candidates', {
							source,
							error: error instanceof Error ? error.message : String(error),
						});
						return [];
					}
				},
			);

			const sourceResults = await Promise.all(sourceProcessingPromises);
			for (const sourceDiscovered of sourceResults)
			{
				discovered.push(...sourceDiscovered);
			}

			const processingTime = Date.now() - startTime;
			this.logResults(validCandidates.length, discovered.length, processingTime, {
				sourceCount: candidatesBySource.size,
				avgConfidence: this.calculateAverageConfidence(discovered),
			});

			return discovered;
		}
		catch (error)
		{
			this.handleError(error as Error, {
				candidateCount: candidates.length,
			});
		}
	}

	/**
	 * Process candidates for a specific source
	 */
	private async processSourceCandidates(
		source: string,
		candidates: DomainCandidate[],
		snapshotStore: SnapshotStore,
	): Promise<DiscoveredDomain[]>
	{
		// Get historical snapshots for temporal analysis
		const snapshots = await snapshotStore.getRecentSnapshots(
			source,
			this.config.timeWindowDays,
		);

		// We need at least (minHistoryDays - 1) snapshots since current candidate provides 1 data point
		const requiredSnapshots = this.config.minHistoryDays - 1;
		if (snapshots.length < requiredSnapshots)
		{
			this.logger.debug('Insufficient historical data for temporal analysis', {
				source,
				availableSnapshots: snapshots.length,
				requiredSnapshots,
			});
			return [];
		}

		// Build ranking history for all domains
		const rankingHistories = this.buildRankingHistories(snapshots, candidates);

		// Identify rising domains
		const risingDomains = this.identifyRisingDomains(rankingHistories);

		this.logger.debug('Temporal analysis completed for source', {
			source,
			inputCandidates: candidates.length,
			historiesBuilt: rankingHistories.length,
			risingDomains: risingDomains.length,
		});

		return risingDomains;
	}

	/**
	 * Build ranking histories from snapshots
	 */
	private buildRankingHistories(
		snapshots: DomainSnapshot[],
		currentCandidates: DomainCandidate[],
	): RankingHistory[]
	{
		const historyMap = new Map<string, RankingHistory>();

		// Determine current date (most recent snapshot date + 1 day, or today)
		const sortedSnapshots = snapshots.sort(
			(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
		);

		let currentDate: string;
		if (sortedSnapshots.length > 0)
		{
			const mostRecentDate = new Date(sortedSnapshots[0].date);
			mostRecentDate.setDate(mostRecentDate.getDate() + 1);
			currentDate = mostRecentDate.toISOString().split('T')[0];
		}
		else
		{
			currentDate = new Date().toISOString().split('T')[0];
		}

		// Initialize histories for current candidates
		for (const candidate of currentCandidates)
		{
			if (!candidate.rank || candidate.rank > this.config.maxRankForAnalysis)
			{
				// Skip domains without rank or ranked too low
				this.logger.debug('Skipping domain for temporal analysis', {
					domain: candidate.domain,
					rank: candidate.rank,
					reason: !candidate.rank ? 'no_rank' : 'rank_too_low',
					maxRankForAnalysis: this.config.maxRankForAnalysis,
				});
				continue;
			}
			else
			{
				const key = `${candidate.domain}:${candidate.source}`;
				historyMap.set(key, {
					domain: candidate.domain,
					source: candidate.source,
					rankings: [{
						rank: candidate.rank,
						date: currentDate,
					}],
				});
			}
		}

		// Add historical rankings from snapshots (use already sorted snapshots)
		for (const snapshot of sortedSnapshots)
		{
			for (const domain of snapshot.domains)
			{
				if (!domain.rank)
				{
					// Skip domains without rank
					this.logger.debug('Skipping historical domain without rank', {
						domain: domain.domain,
						source: domain.source,
						snapshotDate: snapshot.date,
					});
					continue;
				}
				else
				{
					const key = `${domain.domain}:${domain.source}`;
					const history = historyMap.get(key);

					if (history)
					{
						// Add historical ranking if not already present
						const existingDate = history.rankings.find(r => r.date === snapshot.date);
						if (!existingDate)
						{
							history.rankings.push({
								rank: domain.rank,
								date: snapshot.date,
							});
						}
					}
				}
			}
		}

		// Sort rankings by date (oldest first) and filter histories with sufficient data
		const validHistories: RankingHistory[] = [];

		for (const history of historyMap.values())
		{
			history.rankings.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

			// Only keep histories with at least minimum required data points
			if (history.rankings.length >= this.config.minHistoryDays)
			{
				validHistories.push(history);
			}
		}

		return validHistories;
	}

	/**
	 * Identify domains with significant ranking improvements
	 */
	private identifyRisingDomains(histories: RankingHistory[]): DiscoveredDomain[]
	{
		const risingDomains: DiscoveredDomain[] = [];

		for (const history of histories)
		{
			const analysis = this.analyzeRankingTrend(history);

			if (analysis.isRising)
			{
				const confidence = this.calculateTemporalConfidence(history, analysis);
				const discoveryReason = this.buildTemporalDiscoveryReason(history, analysis);

				// Create discovered domain from the most recent ranking
				const latestRanking = history.rankings[history.rankings.length - 1];
				const candidate: DomainCandidate = {
					domain: history.domain,
					source: history.source,
					rank: latestRanking.rank,
					metadata: {
						temporalAnalysis: analysis,
						rankingHistory: history.rankings,
					},
				};

				risingDomains.push(this.createDiscoveredDomain(
					candidate,
					confidence,
					discoveryReason,
				));
			}
		}

		return risingDomains;
	}

	/**
	 * Analyze ranking trend for a domain
	 */
	private analyzeRankingTrend(history: RankingHistory): TemporalAnalysis
	{
		const rankings = history.rankings;
		if (rankings.length < 2)
		{
			return {
				isRising: false,
				improvement: 0,
				velocity: 0,
				consistency: 0,
			};
		}

		const currentRank = rankings[rankings.length - 1].rank;
		const previousRank = rankings[rankings.length - 2].rank;

		// Calculate improvement (lower rank number = better position)
		const improvement = previousRank > currentRank
			? (previousRank - currentRank) / previousRank
			: 0;

		// Calculate velocity (rank change per day)
		const daysDiff = this.calculateDaysDifference(
			rankings[rankings.length - 2].date,
			rankings[rankings.length - 1].date,
		);
		const velocity = daysDiff > 0 ? (previousRank - currentRank) / daysDiff : 0;

		// Calculate consistency (how consistent is the upward trend)
		const consistency = this.calculateTrendConsistency(rankings);

		// Check if domain meets rising criteria
		const isRising = improvement >= this.config.improvementThreshold &&
			velocity > 0 &&
			consistency >= 0.5; // At least 50% consistent upward movement

		return {
			isRising,
			improvement,
			velocity,
			consistency,
			currentRank,
			previousRank,
			daysDiff,
		};
	}

	/**
	 * Calculate trend consistency (0-1 score)
	 */
	private calculateTrendConsistency(rankings: Array<{ rank: number; date: string }>): number
	{
		if (rankings.length < 3)
		{
			return 0.5; // Neutral consistency for insufficient data
		}

		let improvementCount = 0;
		let totalComparisons = 0;

		for (let i = 1; i < rankings.length; i += 1)
		{
			const current = rankings[i].rank;
			const previous = rankings[i - 1].rank;

			if (current < previous)
			{ // Lower rank number = improvement
				improvementCount += 1;
			}
			totalComparisons += 1;
		}

		return totalComparisons > 0 ? improvementCount / totalComparisons : 0;
	}

	/**
	 * Calculate confidence for temporal discovery
	 */
	private calculateTemporalConfidence(
		history: RankingHistory,
		analysis: TemporalAnalysis,
	): number
	{
		const factors = {
			baseConfidence: 0.5, // Lower base confidence for temporal analysis
			rank: analysis.currentRank,
			sourceReliability: this.getSourceReliability(history.source),
			hasMetadata: true, // Always has temporal metadata
			isRecent: true, // Always analyzing recent data
		};

		let confidence = this.calculateConfidence(factors);

		// Boost confidence based on improvement magnitude
		if (analysis.improvement >= 0.7)
		{ // 70%+ improvement
			confidence += 0.2;
		}
		else if (analysis.improvement >= 0.5)
		{ // 50%+ improvement
			confidence += 0.1;
		}

		// Boost confidence based on velocity
		if (analysis.velocity > 10000)
		{ // Very fast rise
			confidence += 0.15;
		}
		else if (analysis.velocity > 1000)
		{ // Fast rise
			confidence += 0.1;
		}
		// Adjust confidence based on consistency (penalize inconsistency more)
		if (analysis.consistency >= 0.8)
		{
			confidence += 0.1; // Reward high consistency
		}
		else if (analysis.consistency < 0.7)
		{
			confidence -= 0.15; // Penalize low consistency
		}

		// Boost confidence for domains in top rankings (more granular)
		if (analysis.currentRank && analysis.currentRank <= 10000)
		{
			confidence += 0.15; // Top 10k domains
		}
		else if (analysis.currentRank && analysis.currentRank <= 100000)
		{
			confidence += 0.1; // Top 100k domains
		}
		else if (analysis.currentRank && analysis.currentRank <= 1000000)
		{
			confidence += 0.05; // Top 1M domains
		}

		return Math.max(0, Math.min(1, confidence));
	}

	/**
	 * Build discovery reason for temporal analysis
	 */
	private buildTemporalDiscoveryReason(
		history: RankingHistory,
		analysis: TemporalAnalysis,
	): string
	{
		const improvementPercent = Math.round(analysis.improvement * 100);
		const velocityInfo = analysis.velocity > 0
			? ` (${Math.round(analysis.velocity)} positions/day)`
			: '';

		return `Rising domain: ${improvementPercent}% rank improvement from ${analysis.previousRank} to ${analysis.currentRank} in ${history.source}${velocityInfo}`;
	}

	/**
	 * Group candidates by source
	 */
	private groupCandidatesBySource(
		candidates: DomainCandidate[],
	): Map<string, DomainCandidate[]>
	{
		const grouped = new Map<string, DomainCandidate[]>();

		for (const candidate of candidates)
		{
			const source = candidate.source;
			if (!grouped.has(source))
			{
				grouped.set(source, []);
			}
			grouped.get(source)!.push(candidate);
		}

		return grouped;
	}

	/**
	 * Calculate average confidence of discovered domains
	 */
	private calculateAverageConfidence(discovered: DiscoveredDomain[]): number
	{
		if (discovered.length === 0)
		{
			return 0;
		}

		const totalConfidence = discovered.reduce((sum, domain) => sum + domain.confidence, 0);
		return Math.round((totalConfidence / discovered.length) * 100) / 100;
	}

	/**
	 * Calculate days difference between two dates
	 */
	private calculateDaysDifference(date1: string, date2: string): number
	{
		const d1 = new Date(date1);
		const d2 = new Date(date2);
		const diffTime = Math.abs(d2.getTime() - d1.getTime());
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	}

	/**
	 * Get source reliability score (0-1)
	 */
	private getSourceReliability(source: string): number
	{
		const reliabilityMap: Record<string, number> = {
			tranco: 0.95,
			radar: 0.90,
			umbrella: 0.85,
			czds: 0.80,
			'common-crawl': 0.70,
			sonar: 0.65,
		};

		return reliabilityMap[source.toLowerCase()] || 0.5;
	}
}

interface TemporalAnalysis
{
	isRising: boolean;
	improvement: number;
	velocity: number;
	consistency: number;
	currentRank?: number;
	previousRank?: number;
	daysDiff?: number;
}

export type { TemporalConfig, TemporalAnalysis };

export { TemporalAnalysisProcessor };

export default TemporalAnalysisProcessor;
