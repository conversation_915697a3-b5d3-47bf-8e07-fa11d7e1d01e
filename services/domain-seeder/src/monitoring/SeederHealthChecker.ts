import type { RedisClientWrapper } from '@shared';
import { <PERSON><PERSON>hecker, logger } from '@shared';
import type { SeederDatabaseManager } from '../seiceIntegration';


type HealthStatusType =
{
	healthy: boolean;
	status: 'healthy' | 'degraded' | 'unhealthy';
	checks: {
		database: boolean;
		redis: boolean;
		queue: boolean;
		sources: boolean;
		psl: boolean;
		memory: boolean;
		disk: boolean;
	};
	details: Record<string, any>;
	timestamp: string;
	uptime: number;
	version: string;
	environment: string;
};

type SourceHealthCheckType =
{
	name: string;
	healthy: boolean;
	lastUpdate: Date | null;
	staleness: number; // seconds
	error?: string;
	responseTime?: number;
	consecutiveFailures?: number;
};

type DatabaseHealthDetailsType =
{
	scylla: {
		healthy: boolean;
		responseTime?: number;
		error?: string;
		connectionPool?: {
			active: number;
			idle: number;
			total: number;
		};
	};
	maria: {
		healthy: boolean;
		responseTime?: number;
		error?: string;
		connectionPool?: {
			active: number;
			idle: number;
			total: number;
		};
	};
	manticore: {
		healthy: boolean;
		responseTime?: number;
		error?: string;
		indexStatus?: string;
	};
};

type QueueHealthDetailsType =
{
	'new:domains': {
		length: number;
		healthy: boolean;
		backlog: boolean;
	};
	'new:domains:high': {
		length: number;
		healthy: boolean;
		backlog: boolean;
	};
	streams: {
		[streamName: string]: {
			length: number;
			consumerGroups: number;
			pendingMessages: number;
		};
	};
};

type SystemResourceHealthType =
{
	memory: {
		used: number;
		total: number;
		percentage: number;
		healthy: boolean;
	};
	disk: {
		used: number;
		total: number;
		percentage: number;
		healthy: boolean;
	};
	cpu: {
		usage: number;
		loadAverage: number[];
		healthy: boolean;
	};
};

class SeederHealthChecker extends HealthChecker
{
	private readonly logger = logger.getLogger('SeederHealthChecker');

	private dbManager: SeederDatabaseManager;

	private redis: RedisClientWrapper;

	private sourceConnectors: Map<string, any> = new Map();

	private pslManager: any;

	private startTime: number;

	private version: string;

	private environment: string;

	private healthCheckCache: Map<string, { result: any; timestamp: number; ttl: number }> = new Map();

	private alertThresholds = {
		memory: 85, // percentage
		disk: 90, // percentage
		cpu: 80, // percentage
		queueDepth: 100000, // messages
		sourceStalenesss: 25 * 3600, // 25 hours in seconds
		pslStaleness: 7 * 24 * 3600, // 7 days in seconds
		responseTime: 5000, // 5 seconds
		consecutiveFailures: 3,
	};

	constructor(
		dbManager: SeederDatabaseManager,
		redis: RedisClientWrapper,
	)
	{
		super('domain-seeder', proc_package_ver | '1.0.0');
		this.dbManager = dbManager;
		this.redis = redis;
		this.startTime = Date.now();
		this.version = process.env.npm_package_version || '1.0.0';
		this.environment = process.env.NODE_ENV || 'development';
	}

	registerSourceConnector(name: string, connector: any): void
	{
		this.sourceConnectors.set(name, connector);
		this.logger.debug('Source connector registered for health checks', { name });
	}

	setPSLManager(pslManager: any): void
	{
		this.pslManager = pslManager;
	}

	async checkHealth(): Promise<HealthStatusType>
	{
		const startTime = Date.now();
		const checks = {
			database: false,
			redis: false,
			queue: false,
			sources: false,
			psl: false,
			memory: false,
			disk: false,
		};
		const details: Record<string, any> = {};

		try
		{
			// Run health checks in parallel for better performance
			const [
				databaseHealth,
				redisHealth,
				queueHealth,
				sourceHealth,
				pslHealth,
				systemHealth,
			] = await Promise.allSettled([
				this.checkDatabaseHealthWithDetails(),
				this.checkRedisHealthWithDetails(),
				this.checkQueueHealthWithDetails(),
				this.checkSourcesHealth(),
				this.checkPSLHealthWithDetails(),
				this.checkSystemResourceHealth(),
			]);

			// Process database health
			if (databaseHealth.status === 'fulfilled')
			{
				checks.database = databaseHealth.value.healthy;
				details.database = databaseHealth.value.details;
			}
			else
			{
				checks.database = false;
				details.database = { error: databaseHealth.reason?.message || 'Database check failed' };
			}

			// Process Redis health
			if (redisHealth.status === 'fulfilled')
			{
				checks.redis = redisHealth.value.healthy;
				details.redis = redisHealth.value.details;
			}
			else
			{
				checks.redis = false;
				details.redis = { error: redisHealth.reason?.message || 'Redis check failed' };
			}

			// Process queue health
			if (queueHealth.status === 'fulfilled')
			{
				checks.queue = queueHealth.value.healthy;
				details.queue = queueHealth.value.details;
			}
			else
			{
				checks.queue = false;
				details.queue = { error: queueHealth.reason?.message || 'Queue check failed' };
			}

			// Process source health
			if (sourceHealth.status === 'fulfilled')
			{
				checks.sources = sourceHealth.value.healthy;
				details.sources = sourceHealth.value.details;
			}
			else
			{
				checks.sources = false;
				details.sources = { error: sourceHealth.reason?.message || 'Source check failed' };
			}

			// Process PSL health
			if (pslHealth.status === 'fulfilled')
			{
				checks.psl = pslHealth.value.healthy;
				details.psl = pslHealth.value.details;
			}
			else
			{
				checks.psl = false;
				details.psl = { error: pslHealth.reason?.message || 'PSL check failed' };
			}

			// Process system resource health
			if (systemHealth.status === 'fulfilled')
			{
				checks.memory = systemHealth.value.memory.healthy;
				checks.disk = systemHealth.value.disk.healthy;
				details.system = systemHealth.value;
			}
			else
			{
				checks.memory = false;
				checks.disk = false;
				details.system = { error: systemHealth.reason?.message || 'System check failed' };
			}

			// Determine overall health status
			const criticalChecks = ['database', 'redis'];
			const hasCriticalFailures = criticalChecks.some(check => !checks[check]);
			const hasAnyFailures = Object.values(checks).some(check => !check);

			let status: 'healthy' | 'degraded' | 'unhealthy';
			if (hasCriticalFailures)
			{
				status = 'unhealthy';
			}
			else if (hasAnyFailures)
			{
				status = 'degraded';
			}
			else
			{
				status = 'healthy';
			}

			const healthy = status === 'healthy';
			const duration = Date.now() - startTime;
			const uptime = Math.floor((Date.now() - this.startTime) / 1000);

			this.logger.info('Health check completed', {
				healthy,
				status,
				duration,
				checks,
			});

			return ({
				healthy,
				status,
				checks,
				details,
				timestamp: new Date().toISOString(),
				uptime,
				version: this.version,
				environment: this.environment,
			});
		}
		catch (error)
		{
			this.logger.error('Health check failed', error);
			return ({
				healthy: false,
				status: 'unhealthy',
				checks,
				details: { error: error.message },
				timestamp: new Date().toISOString(),
				uptime: Math.floor((Date.now() - this.startTime) / 1000),
				version: this.version,
				environment: this.environment,
			});
		}
	}

	private async checkDatabaseHealth(): Promise<boolean>
	{
		try
		{
			return await this.dbManager.healthCheck();
		}
		catch (error)
		{
			this.logger.error('Database health check failed', error);
			return false;
		}
	}

	private async checkDatabaseHealthWithDetails(): Promise<{ healthy: boolean; details: DatabaseHealthDetailsType }>
	{
		const details: DatabaseHealthDetailsType =
		{
			scylla: { healthy: false },
			maria: { healthy: false },
			manticore: { healthy: false },
		};

		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();
			const mariaClient = this.dbManager.getMariaClient();
			const manticoreClient = this.dbManager.getManticoreClient();

			// Check ScyllaDB
			try
			{
				const startTime = Date.now();
				await scyllaClient.execute('SELECT now() FROM system.local LIMIT 1');
				details.scylla = {
					healthy: true,
					responseTime: Date.now() - startTime,
				};
			}
			catch (error)
			{
				details.scylla = {
					healthy: false,
					error: error.message,
				};
			}

			// Check MariaDB
			try
			{
				const startTime = Date.now();
				await mariaClient.execute('SELECT 1');
				details.maria = {
					healthy: true,
					responseTime: Date.now() - startTime,
				};
			}
			catch (error)
			{
				details.maria = {
					healthy: false,
					error: error.message,
				};
			}

			// Check Manticore
			try
			{
				const startTime = Date.now();
				await manticoreClient.healthCheck();
				details.manticore = {
					healthy: true,
					responseTime: Date.now() - startTime,
					indexStatus: 'active',
				};
			}
			catch (error)
			{
				details.manticore = {
					healthy: false,
					error: error.message,
				};
			}

			const healthy = details.scylla.healthy && details.maria.healthy && details.manticore.healthy;
			return { healthy, details };
		}
		catch (error)
		{
			this.logger.error('Database health check failed', error);
			return {
				healthy: false,
				details: {
					...details,
					error: error.message,
				} as any,
			};
		}
	}

	private async getDatabaseDetails(): Promise<any>
	{
		try
		{
			const scyllaClient = this.dbManager.getScyllaClient();
			const mariaClient = this.dbManager.getMariaClient();
			const manticoreClient = this.dbManager.getManticoreClient();

			const [scyllaHealth, mariaHealth, manticoreHealth] = await Promise.allSettled([
				scyllaClient.healthCheck(),
				mariaClient.healthCheck(),
				manticoreClient.healthCheck(),
			]);

			return {
				scylla: {
					healthy: scyllaHealth.status === 'fulfilled' && scyllaHealth.value,
					error: scyllaHealth.status === 'rejected' ? scyllaHealth.reason?.message : null,
				},
				maria: {
					healthy: mariaHealth.status === 'fulfilled' && mariaHealth.value,
					error: mariaHealth.status === 'rejected' ? mariaHealth.reason?.message : null,
				},
				manticore: {
					healthy: manticoreHealth.status === 'fulfilled' && manticoreHealth.value,
					error: manticoreHealth.status === 'rejected' ? manticoreHealth.reason?.message : null,
				},
			};
		}
		catch (error)
		{
			return ({ error: error.message });
		}
	}

	private async checkRedisHealth(): Promise<boolean>
	{
		try
		{
			const result = await this.redis.ping();
			return result === 'PONG';
		}
		catch (error)
		{
			this.logger.error('Redis health check failed', error);
			return false;
		}
	}

	private async checkRedisHealthWithDetails(): Promise<{ healthy: boolean; details: any }>
	{
		try
		{
			const startTime = Date.now();
			const result = await this.redis.ping();
			const responseTime = Date.now() - startTime;

			if (result !== 'PONG')
			{
				return {
					healthy: false,
					details: {
						error: 'Ping failed',
						responseTime,
					},
				};
			}

			// Get Redis info
			const [info, memory, stats] = await Promise.allSettled([
				this.redis.info(),
				this.redis.info('memory'),
				this.redis.info('stats'),
			]);

			const details: any = {
				connected: true,
				responseTime,
			};

			if (info.status === 'fulfilled')
			{
				const infoLines = info.value.split('\n');
				details.server = infoLines
					.filter(line => line.includes('redis_version') || line.includes('uptime_in_seconds'))
					.reduce((acc, line) =>
					{
						const [key, value] = line.split(':');
						if (key && value)
						{
							acc[key.trim()] = value.trim();
						}
						return acc;
					}, {} as Record<string, string>);
			}

			if (memory.status === 'fulfilled')
			{
				const memoryLines = memory.value.split('\n');
				details.memory = memoryLines
					.filter(line => line.includes('used_memory') || line.includes('maxmemory'))
					.reduce((acc, line) =>
					{
						const [key, value] = line.split(':');
						if (key && value)
						{
							acc[key.trim()] = value.trim();
						}
						return acc;
					}, {} as Record<string, string>);
			}

			if (stats.status === 'fulfilled')
			{
				const statsLines = stats.value.split('\n');
				details.stats = statsLines
					.filter(line => line.includes('total_connections_received') || line.includes('total_commands_processed'))
					.reduce((acc, line) =>
					{
						const [key, value] = line.split(':');
						if (key && value)
						{
							acc[key.trim()] = value.trim();
						}
						return acc;
					}, {} as Record<string, string>);
			}

			return {
				healthy: true,
				details,
			};
		}
		catch (error)
		{
			this.logger.error('Redis health check failed', error);
			return {
				healthy: false,
				details: {
					error: error.message,
				},
			};
		}
	}

	private async getRedisDetails(): Promise<any>
	{
		try
		{
			const info = await this.redis.info();
			const memory = await this.redis.info('memory');

			return {
				connected: true,
				info: info.split('\n').slice(0, 5), // First few lines of info
				memory: memory.split('\n').filter(line => line.includes('used_memory') || line.includes('maxmemory')),
			};
		}
		catch (error)
		{
			return ({ error: error.message });
		}
	}

	private async checkQueueHealth(): Promise<boolean>
	{
		try
		{
			// Check if we can get queue lengths
			const normalQueueLength = await this.redis.llen('new:domains');
			const highQueueLength = await this.redis.llen('new:domains:high');

			// Queue is healthy if we can get lengths (even if 0)
			return normalQueueLength >= 0 && highQueueLength >= 0;
		}
		catch (error)
		{
			this.logger.error('Queue health check failed', error);
			return false;
		}
	}

	private async checkQueueHealthWithDetails(): Promise<{ healthy: boolean; details: QueueHealthDetails }>
	{
		try
		{
			// Check queue lengths
			const [normalQueueLength, highQueueLength] = await Promise.all([
				this.redis.llen('new:domains'),
				this.redis.llen('new:domains:high'),
			]);

			// Check Redis Streams
			const streamNames = ['stream:new:domains', 'stream:discovery:log'];
			const streamDetails: { [streamName: string]: any } = {};

			for (const streamName of streamNames)
			{
				try
				{
					const streamInfo = await this.redis.xinfo('STREAM', streamName);
					const groupInfo = await this.redis.xinfo('GROUPS', streamName);

					streamDetails[streamName] = {
						length: streamInfo.length || 0,
						consumerGroups: Array.isArray(groupInfo) ? groupInfo.length : 0,
						pendingMessages: 0, // Will be calculated from consumer groups
					};

					// Get pending messages count
					if (Array.isArray(groupInfo))
					{
						let totalPending = 0;
						for (const group of groupInfo)
						{
							totalPending += group.pending || 0;
						}
						streamDetails[streamName].pendingMessages = totalPending;
					}
				}
				catch (streamError)
				{
					// Stream might not exist yet, which is okay
					streamDetails[streamName] = {
						length: 0,
						consumerGroups: 0,
						pendingMessages: 0,
						error: 'Stream not found or not accessible',
					};
				}
			}

			const details: QueueHealthDetailsType =
			{
				'new:domains':
				{
					length: normalQueueLength,
					healthy: normalQueueLength >= 0 && normalQueueLength < this.alertThresholds.queueDepth,
					backlog: normalQueueLength > this.alertThresholds.queueDepth,
				},
				'new:domains:high':
				{
					length: highQueueLength,
					healthy: highQueueLength >= 0 && highQueueLength < this.alertThresholds.queueDepth,
					backlog: highQueueLength > this.alertThresholds.queueDepth,
				},
				streams: streamDetails,
			};

			const healthy = details['new:domains'].healthy && details['new:domains:high'].healthy;

			return { healthy, details };
		}
		catch (error)
		{
			this.logger.error('Queue health check failed', error);
			return {
				healthy: false,
				details: {
					error: error.message,
				} as any,
			};
		}
	}

	private async getQueueDetails(): Promise<any>
	{
		try
		{
			const normalQueueLength = await this.redis.llen('new:domains');
			const highQueueLength = await this.redis.llen('new:domains:high');

			return ({
				'new:domains': normalQueueLength,
				'new:domains:high': highQueueLength,
				total: normalQueueLength + highQueueLength,
			});
		}
		catch (error)
		{
			return ({ error: error.message });
		}
	}

	private async checkSourcesHealth()
		: Promise<{
			healthy: boolean;
			details: SourceHealthCheckType[];
		}>
	{
		const sourceChecks: SourceHealthCheckType[] = [];
		let allHealthy = true;

		for (const [name, connector] of this.sourceConnectors)
		{
			try
			{
				const startTime = Date.now();
				const healthy = await Promise.race([
					connector.healthCheck(),
					new Promise((_, reject) => setTimeout(() => reject(new Error('Health check timeout')), this.alertThresholds.responseTime)),
				]) as boolean;

				const responseTime = Date.now() - startTime;
				const lastUpdate = await connector.getLastUpdate();
				const staleness = lastUpdate
					? Math.floor((Date.now() - lastUpdate.getTime()) / 1000)
					: Infinity;

				// Get consecutive failure count from cache
				const failureKey = `health:failures:${name}`;
				const consecutiveFailures = await this.getConsecutiveFailures(failureKey);

				const sourceCheck: SourceHealthCheckType =
				{
					name,
					healthy,
					lastUpdate,
					staleness,
					responseTime,
					consecutiveFailures,
				};

				// Consider source unhealthy if it's stale
				if (staleness > this.alertThresholds.sourceStalenesss)
				{
					sourceCheck.healthy = false;
					sourceCheck.error = `Source is stale (${Math.floor(staleness / 3600)} hours)`;
				}

				// Consider source unhealthy if response time is too high
				if (responseTime > this.alertThresholds.responseTime)
				{
					sourceCheck.healthy = false;
					sourceCheck.error = `Response time too high (${responseTime}ms)`;
				}

				// Consider source unhealthy if too many consecutive failures
				if (consecutiveFailures >= this.alertThresholds.consecutiveFailures)
				{
					sourceCheck.healthy = false;
					sourceCheck.error = `Too many consecutive failures (${consecutiveFailures})`;
				}

				// Update failure count
				if (sourceCheck.healthy)
				{
					await this.resetConsecutiveFailures(failureKey);
				}
				else
				{
					await this.incrementConsecutiveFailures(failureKey);
				}

				sourceChecks.push(sourceCheck);

				if (!sourceCheck.healthy)
				{
					allHealthy = false;
				}
			}
			catch (error)
			{
				const failureKey = `health:failures:${name}`;
				const consecutiveFailures = await this.incrementConsecutiveFailures(failureKey);

				sourceChecks.push({
					name,
					healthy: false,
					lastUpdate: null,
					staleness: Infinity,
					error: error.message,
					consecutiveFailures,
				});
				allHealthy = false;
			}
		}

		return ({ healthy: allHealthy, details: sourceChecks });
	}

	private async getConsecutiveFailures(key: string): Promise<number>
	{
		try
		{
			const value = await this.redis.get(key);
			return value ? parseInt(value, 10) : 0;
		}
		catch (error)
		{
			return 0;
		}
	}

	private async incrementConsecutiveFailures(key: string): Promise<number>
	{
		try
		{
			const result = await this.redis.incr(key);
			await this.redis.expire(key, 86400); // 24 hour TTL
			return result;
		}
		catch (error)
		{
			return 1;
		}
	}

	private async resetConsecutiveFailures(key: string): Promise<void>
	{
		try
		{
			await this.redis.del(key);
		}
		catch (error)
		{
			// Ignore errors when resetting
		}
	}

	// TODO-MAYBE-INCOMPLETE: This function has created but unused?
	// private async checkPSLHealth(): Promise<boolean>
	// {
	// 	if (!this.pslManager)
	// 	{
	// 		return true; // PSL not configured, consider healthy
	// 	}

	// 	try
	// 	{
	// 		const lastUpdate = this.pslManager.getLastUpdate();
	// 		if (!lastUpdate)
	// 		{
	// 			return false; // PSL never updated
	// 		}

	// 		// Check if PSL is stale (>7 days)
	// 		const staleness = Date.now() - lastUpdate.getTime();
	// 		const maxStaleness = 7 * 24 * 60 * 60 * 1000; // 7 days

	// 		return staleness < maxStaleness;
	// 	}
	// 	catch (error)
	// 	{
	// 		this.logger.error('PSL health check failed', error);
	// 		return false;
	// 	}
	// }

	private async checkPSLHealthWithDetails(): Promise<{ healthy: boolean; details: any }>
	{
		if (!this.pslManager)
		{
			return {
				healthy: true,
				details: { configured: false, status: 'not_configured' },
			};
		}

		try
		{
			const lastUpdate = this.pslManager.getLastUpdate();
			const staleness = lastUpdate
				? Math.floor((Date.now() - lastUpdate.getTime()) / 1000)
				: null;

			const isStale = staleness ? staleness > this.alertThresholds.pslStaleness : false;
			const healthy = !isStale && lastUpdate !== null;

			const details = {
				configured: true,
				lastUpdate: lastUpdate?.toISOString(),
				staleness,
				stale: isStale,
				status: healthy ? 'healthy' : (isStale ? 'stale' : 'never_updated'),
				nextUpdateDue: lastUpdate
					? new Date(lastUpdate.getTime() + (7 * 24 * 60 * 60 * 1000)).toISOString()
					: null,
			};

			return { healthy, details };
		}
		catch (error)
		{
			this.logger.error('PSL health check failed', error);
			return {
				healthy: false,
				details: {
					configured: true,
					error: error.message,
					status: 'error',
				},
			};
		}
	}

	private async checkSystemResourceHealth(): Promise<SystemResourceHealthType>
	{
		try
		{
			const process = await import('process');
			const os = await import('os');
			const fs = await import('fs');

			// Memory check
			const memoryUsage = process.memoryUsage();
			const totalMemory = os.totalmem();
			const freeMemory = os.freemem();
			const usedMemory = totalMemory - freeMemory;
			const memoryPercentage = Math.round((usedMemory / totalMemory) * 100);

			// CPU check
			const cpuUsage = await this.getCpuUsage();
			const loadAverage = os.loadavg();

			// Comprehensive disk check for multiple mount points
			let diskUsed = 0;
			let diskTotal = 0;
			let diskPercentage = 0;

			try
			{
				// Check multiple important directories
				const pathsToCheck =
				[
					process.cwd(), // Current working directory
					os.tmpdir(), // Temp directory
					os.homedir(), // Home directory
					'/var/log', // Log directory (if exists)
				];

				let maxUsagePercentage = 0;
				let totalUsed = 0;
				let totalSpace = 0;

				for (const path of pathsToCheck)
				{
					try
					{
						const stats = await fs.promises.statfs(path);
						const pathTotal = stats.blocks * stats.bsize;
						const pathUsed = (stats.blocks - stats.bavail) * stats.bsize;
						const pathPercentage = (pathUsed / pathTotal) * 100;

						totalUsed += pathUsed;
						totalSpace += pathTotal;
						maxUsagePercentage = Math.max(maxUsagePercentage, pathPercentage);
					}
					catch (pathError)
					{
						// Skip paths that don't exist or can't be accessed
						this.logger.debug('Could not check disk usage for path', { path, error: (pathError as Error).message });
					}
				}

				diskUsed = totalUsed;
				diskTotal = totalSpace;
				diskPercentage = Math.round(maxUsagePercentage); // Use the highest usage percentage
			}
			catch (diskError)
			{
				// Disk check failed, use defaults
				this.logger.warn('Disk usage check failed', { error: (diskError as Error).message });
			}

			return {
				memory: {
					used: Math.round(usedMemory / 1024 / 1024), // MB
					total: Math.round(totalMemory / 1024 / 1024), // MB
					percentage: memoryPercentage,
					healthy: memoryPercentage < this.alertThresholds.memory,
				},
				disk: {
					used: Math.round(diskUsed / 1024 / 1024), // MB
					total: Math.round(diskTotal / 1024 / 1024), // MB
					percentage: diskPercentage,
					healthy: diskPercentage < this.alertThresholds.disk,
				},
				cpu: {
					usage: cpuUsage,
					loadAverage,
					healthy: cpuUsage < this.alertThresholds.cpu,
				},
			};
		}
		catch (error)
		{
			this.logger.error('System resource health check failed', error);
			return {
				memory: {
					used: 0, total: 0, percentage: 0, healthy: false,
				},
				disk: {
					used: 0, total: 0, percentage: 0, healthy: false,
				},
				cpu: { usage: 0, loadAverage: [0, 0, 0], healthy: false },
			};
		}
	}

	private async getCpuUsage(): Promise<number>
	{
		const os = await import('os');

		return new Promise((resolve) =>
		{
			const startMeasure = this.cpuAverage();

			setTimeout(() =>
			{
				const endMeasure = this.cpuAverage();
				const idleDifference = endMeasure.idle - startMeasure.idle;
				const totalDifference = endMeasure.total - startMeasure.total;
				const percentageCPU = 100 - ~~(100 * idleDifference / totalDifference);
				resolve(percentageCPU);
			}, 100);
		});
	}

	private cpuAverage(): { idle: number; total: number }
	{
		const os = require('os');
		const cpus = os.cpus();
		let totalIdle = 0;
		let totalTick = 0;

		cpus.forEach((cpu: any) =>
		{
			for (const type in cpu.times)
			{
				totalTick += cpu.times[type];
			}
			totalIdle += cpu.times.idle;
		});

		return {
			idle: totalIdle / cpus.length,
			total: totalTick / cpus.length,
		};
	}

	// TODO-MAYBE-INCOMPLETE: This function has created but unused?
	// private async getPSLDetails(): Promise<any>
	// {
	// 	if (!this.pslManager)
	// 	{
	// 		return { configured: false };
	// 	}

	// 	try
	// 	{
	// 		const lastUpdate = this.pslManager.getLastUpdate();
	// 		const staleness = lastUpdate
	// 			? Math.floor((Date.now() - lastUpdate.getTime()) / 1000)
	// 			: null;

	// 		return {
	// 			configured: true,
	// 			lastUpdate: lastUpdate?.toISOString(),
	// 			staleness,
	// 			stale: staleness ? staleness > (7 * 24 * 3600) : true,
	// 		};
	// 	}
	// 	catch (error)
	// 	{
	// 		return ({ error: error.message });
	// 	}
	// }

	// Readiness probe - checks if service is ready to accept traffic
	async checkReadiness(): Promise<boolean>
	{
		try
		{
			// Service is ready if database and Redis are healthy
			const dbHealthy = await this.checkDatabaseHealth();
			const redisHealthy = await this.checkRedisHealth();

			return dbHealthy && redisHealthy;
		}
		catch (error)
		{
			this.logger.error('Readiness check failed', error);
			return false;
		}
	}

	// Liveness probe - checks if service is alive
	async checkLiveness(): Promise<boolean>
	{
		try
		{
			// Service is alive if we can perform basic operations
			const redisHealthy = await this.checkRedisHealth();
			return redisHealthy;
		}
		catch (error)
		{
			this.logger.error('Liveness check failed', error);
			return false;
		}
	}

	// Get detailed status for monitoring
	async getDetailedStatus(): Promise<any>
	{
		const health = await this.checkHealth();
		const readiness = await this.checkReadiness();
		const liveness = await this.checkLiveness();

		return ({
			...health,
			readiness,
			liveness,
			uptime: process.uptime(),
			memory: process.memoryUsage(),
			version: process.env.npm_package_version || 'unknown',
		});
	}

	// TODO-MAYBE-INCOMPLETE: This function has created but unused?
	// Cache health check results to avoid excessive checks
	// private async getCachedResult<T>(
	// 	key: string,
	// 	checkFunction: () => Promise<T>,
	// 	ttlSeconds: number = 30,
	// ): Promise<T>
	// {
	// 	const cached = this.healthCheckCache.get(key);
	// 	const now = Date.now();

	// 	if (cached && (now - cached.timestamp) < (cached.ttl * 1000))
	// 	{
	// 		return cached.result;
	// 	}

	// 	const result = await checkFunction();
	// 	this.healthCheckCache.set(key, {
	// 		result,
	// 		timestamp: now,
	// 		ttl: ttlSeconds,
	// 	});

	// 	return result;
	// }

	// Clear health check cache
	clearCache(): void
	{
		this.healthCheckCache.clear();
	}

	// Set custom alert thresholds
	setAlertThresholds(thresholds: Partial<typeof this.alertThresholds>): void
	{
		this.alertThresholds = { ...this.alertThresholds, ...thresholds };
		this.logger.info('Alert thresholds updated', { thresholds: this.alertThresholds });
	}

	// Get current alert thresholds
	getAlertThresholds(): typeof this.alertThresholds
	{
		return { ...this.alertThresholds };
	}

	// Check if any alerts should be triggered
	async checkAlerts(): Promise<{
		alerts: Array<{
			level: 'warning' | 'critical';
			component: string;
			message: string;
			timestamp: string;
			details?: any;
		}>;
		hasAlerts: boolean;
	}>
	{
		const alerts: Array<{
			level: 'warning' | 'critical';
			component: string;
			message: string;
			timestamp: string;
			details?: any;
		}> = [];

		try
		{
			const health = await this.checkHealth();
			const timestamp = new Date().toISOString();

			// Check critical database failures
			if (!health.checks.database)
			{
				alerts.push({
					level: 'critical',
					component: 'database',
					message: 'Database health check failed',
					timestamp,
					details: health.details.database,
				});
			}

			// Check critical Redis failures
			if (!health.checks.redis)
			{
				alerts.push({
					level: 'critical',
					component: 'redis',
					message: 'Redis health check failed',
					timestamp,
					details: health.details.redis,
				});
			}

			// Check queue depth
			if (health.details.queue && typeof health.details.queue === 'object')
			{
				const queueDetails = health.details.queue as QueueHealthDetailsType;
				if (queueDetails['new:domains']?.backlog)
				{
					alerts.push({
						level: 'warning',
						component: 'queue',
						message: `Queue backlog detected: ${queueDetails['new:domains'].length} messages`,
						timestamp,
						details: queueDetails,
					});
				}
			}

			// Check system resources
			if (health.details.system && typeof health.details.system === 'object')
			{
				const systemDetails = health.details.system as SystemResourceHealthType;

				if (!systemDetails.memory.healthy)
				{
					alerts.push({
						level: 'warning',
						component: 'memory',
						message: `High memory usage: ${systemDetails.memory.percentage}%`,
						timestamp,
						details: systemDetails.memory,
					});
				}

				if (!systemDetails.disk.healthy)
				{
					alerts.push({
						level: 'warning',
						component: 'disk',
						message: `High disk usage: ${systemDetails.disk.percentage}%`,
						timestamp,
						details: systemDetails.disk,
					});
				}

				if (!systemDetails.cpu.healthy)
				{
					alerts.push({
						level: 'warning',
						component: 'cpu',
						message: `High CPU usage: ${systemDetails.cpu.usage}%`,
						timestamp,
						details: systemDetails.cpu,
					});
				}
			}

			// Check source staleness
			if (health.details.sources && Array.isArray(health.details.sources))
			{
				const sourceDetails = health.details.sources as SourceHealthCheckType[];
				for (const source of sourceDetails)
				{
					if (!source.healthy && source.staleness > this.alertThresholds.sourceStalenesss)
					{
						alerts.push({
							level: 'warning',
							component: 'source',
							message: `Source ${source.name} is stale (${Math.floor(source.staleness / 3600)} hours)`,
							timestamp,
							details: source,
						});
					}

					if (source.consecutiveFailures && source.consecutiveFailures >= this.alertThresholds.consecutiveFailures)
					{
						alerts.push({
							level: 'critical',
							component: 'source',
							message: `Source ${source.name} has ${source.consecutiveFailures} consecutive failures`,
							timestamp,
							details: source,
						});
					}
				}
			}

			return {
				alerts,
				hasAlerts: alerts.length > 0,
			};
		}
		catch (error)
		{
			this.logger.error('Alert check failed', error);
			return {
				alerts: [{
					level: 'critical',
					component: 'health-checker',
					message: 'Health check system failure',
					timestamp: new Date().toISOString(),
					details: { error: error.message },
				}],
				hasAlerts: true,
			};
		}
	}
}

export type {
	HealthStatusType,
	SourceHealthCheckType,
	DatabaseHealthDetailsType,
	QueueHealthDetailsType,
	SystemResourceHealthType,
};

export default SeederHealthChecker;
