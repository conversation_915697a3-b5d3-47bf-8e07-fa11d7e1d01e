import type { Request, Response, NextFunction, Application } from 'ultimate-express';
import express from 'ultimate-express';
import { Server } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import { DatabaseManager, Logger, Constants } from '@shared';
import Config from '@shared/utils/Config';
import { ReactRenderer, PageComponent } from './utils/ReactRenderer';
import DomainSearchService from './services/DomainSearchService';
import DomainAnalysisService from './services/DomainAnalysisService';
import TopDomainsService from './services/TopDomainsService';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const logger = Logger.getLogger('WebApp');
const config = Config;

/**
 * Web Application Service
 * Provides domain search, analysis, and ranking interfaces
 */
class WebAppService {
	private app: Application;
	private server: Server | null;
	private dbManager: DatabaseManager;
	private domainSearchService: DomainSearchService | null = null;
	private domainAnalysisService: DomainAnalysisService | null = null;
	private topDomainsService: TopDomainsService | null = null;

	constructor() {
		this.app = express();
		this.server = null;
		this.dbManager = new DatabaseManager();
	}

	/**
	 * Initialize the web application service
	 */
	async initialize(): Promise<void> {
		try {
			logger.info('Initializing Web Application Service...');

			// Validate configuration
			config.validate();

			// Initialize database connections
			try
			{
				await this.dbManager.initialize();
				this.domainSearchService = new DomainSearchService(this.dbManager);
				this.domainAnalysisService = new DomainAnalysisService(this.dbManager);
				this.topDomainsService = new TopDomainsService(this.dbManager);
				logger.info('Database connections initialized successfully');
			}
			catch (error)
			{
				logger.warn('Database initialization failed, running in development mode:', error);
				// Continue without database connections for development
			}

			// Setup middleware
			this.setupMiddleware();

			// Setup routes
			this.setupRoutes();

			// Setup error handling
			this.setupErrorHandling();

			logger.info('Web Application Service initialized successfully');
		}
		catch (error) {
			logger.error('Failed to initialize Web Application Service:', error);
			throw error;
		}
	}

	/**
	 * Setup ultimate-express middleware
	 */
	setupMiddleware(): void {
		// Set up EJS template engine
		this.app.set('view engine', 'ejs');
		this.app.set('views', path.join(__dirname, 'views'));

		// Request logging
		this.app.use(Logger.createRequestLogger('WebApp'));

		// Basic middleware
		this.app.use(express.json({ limit: '10mb' }));
		this.app.use(express.urlencoded({ extended: true }));

		// Static files (for CSS, JS, images)
		this.app.use('/static', express.static(path.join(__dirname, '../public')));

		// CORS
		this.app.use((req: Request, res: Response, next: NextFunction) => {
			res.header('Access-Control-Allow-Origin', config.get('CORS_ORIGIN', '*'));
			res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
			res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
			next();
		});
	}

	/**
	 * Setup application routes
	 */
	setupRoutes(): void {
		// Health check endpoint
		this.app.get('/health', async (req: Request, res: Response) => {
			try {
				let databases = { scylla: false, maria: false, redis: false, manticore: false };
				let message = 'Web server running';

				if (this.dbManager) {
					try {
						databases = await this.dbManager.healthCheck();
						message = 'Web server running with database connections';
					} catch (error) {
						message = 'Web server running, database health check failed';
					}
				} else {
					message = 'Web server running, databases not connected';
				}

				res.json({
					status: 'ok',
					service: 'web-app',
					timestamp: new Date().toISOString(),
					databases,
					mode: config.isDevelopment() ? 'development' : 'production',
					message
				});
			}
			catch (error) {
				res.status(500).json({
					status: 'error',
					service: 'web-app',
					error: (error as Error).message
				});
			}
		});

		// Web page routes with React SSR
		this.setupWebRoutes();

		// API routes
		this.setupApiRoutes();

		// Description API
		this.app.get('/api/domains/:domain/description', async (req: Request, res: Response) => {
			try {
				const { default: descriptionRouter } = await import('./routes/description');
				return descriptionRouter.handle(req, res);
			} catch (error) {
				logger.error('Description route failed to load:', error);
				return res.status(500).json({ error: 'Route load failed' });
			}
		});
	}

	/**
	 * Setup web page routes with React SSR
	 */
	setupWebRoutes(): void {
		// Homepage
		this.app.get('/', async (req: Request, res: Response) => {
			try {
				const pageData = ReactRenderer.renderPage({
					component: 'HomePage',
					props: {
						initialData: {}
					}
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('Homepage render error:', error);
				res.status(500).send('Error rendering homepage');
			}
		});

		// Search page
		this.app.get('/search', async (req: Request, res: Response) => {
			try {
				const { q, category, country, technology, page = 1, limit = 20 } = req.query;

				// Get search results if query is provided
				let searchResults = null;
				if (q && this.domainSearchService) {
					try {
						searchResults = await this.domainSearchService.searchDomains({
							query: q as string,
							category: category as string,
							country: country as string,
							technology: technology as string,
							page: parseInt(page as string),
							limit: parseInt(limit as string)
						});
					} catch (error) {
						logger.error('Search failed:', error);
						// Continue with null results
					}
				}

				const pageData = ReactRenderer.renderPage({
					component: 'SearchPage',
					props: {
						initialData: {
							query: q as string || '',
							results: searchResults?.domains || [],
							facets: searchResults?.facets || {},
							pagination: searchResults?.pagination || {},
							filters: searchResults?.filters || {},
							took: searchResults?.took || 0
						}
					}
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('Search page render error:', error);
				res.status(500).send('Error rendering search page');
			}
		});

		// Domain analysis page
		this.app.get('/domain/:domain', async (req: Request, res: Response) => {
			try {
				const { domain } = req.params;

				let analysis = null;
				if (this.domainAnalysisService) {
					try {
						analysis = await this.domainAnalysisService.getDomainAnalysis(domain);
					} catch (error) {
						logger.error('Domain analysis failed:', error);
						// Continue with null analysis
					}
				}

				if (!analysis) {
					return res.status(404).send('Domain not found or analysis not available');
				}

				const pageData = ReactRenderer.renderPage({
					component: 'DomainAnalysisPage',
					props: {
						initialData: { analysis }
					}
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('Domain analysis page render error:', error);
				res.status(500).send('Error rendering domain analysis page');
			}
		});

		// Top domains page
		this.app.get('/top-domains', async (req: Request, res: Response) => {
			try {
				const { category, country, limit = 50, timeframe } = req.query;

				let topDomains = null;
				if (this.topDomainsService) {
					try {
						topDomains = await this.topDomainsService.getTopDomains({
							category: category as string,
							country: country as string,
							limit: parseInt(limit as string),
							timeframe: timeframe as string
						});
					} catch (error) {
						logger.error('Top domains retrieval failed:', error);
						// Continue with null data
					}
				}

				// Get available categories
				let categories = [];
				if (this.topDomainsService) {
					try {
						categories = await this.topDomainsService.getAvailableCategories();
					} catch (error) {
						logger.error('Failed to get categories:', error);
					}
				}

				const pageData = ReactRenderer.renderPage({
					component: 'TopDomainsPage',
					props: {
						initialData: {
							topDomains,
							categories,
							selectedCategory: category as string,
							selectedCountry: country as string,
							selectedTimeframe: timeframe as string
						}
					}
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('Top domains page render error:', error);
				res.status(500).send('Error rendering top domains page');
			}
		});

		// Domain comparison page
		this.app.get('/compare', async (req: Request, res: Response) => {
			try {
				const domains = Array.isArray(req.query.domain)
					? req.query.domain as string[]
					: req.query.domain
						? [req.query.domain as string]
						: [];

				let comparison = null;
				if (domains.length >= 2 && this.domainAnalysisService) {
					try {
						comparison = await this.domainAnalysisService.compareDomains(domains);
					} catch (error) {
						logger.error('Domain comparison failed:', error);
						// Continue with null comparison
					}
				}

				const pageData = ReactRenderer.renderPage({
					component: 'DomainComparisonPage',
					props: {
						initialData: {
							comparison,
							domains: domains.length >= 2 ? domains : ['', '']
						}
					}
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('Comparison page render error:', error);
				res.status(500).send('Error rendering comparison page');
			}
		});

		// About page
		this.app.get('/about', async (req: Request, res: Response) => {
			try {
				const pageData = ReactRenderer.renderPage({
					component: 'HomePage', // We'll create an AboutPage component later
					props: {
						initialData: {}
					},
					title: 'About',
					description: 'Learn about our comprehensive domain analysis and ranking platform.'
				});

				res.render('layout', pageData);
			}
			catch (error) {
				logger.error('About page render error:', error);
				res.status(500).send('Error rendering about page');
			}
		});
	}

	/**
	 * Setup API routes
	 */
	setupApiRoutes(): void {

		// Domain search API with Manticore integration
		this.app.get('/api/domains/search', async (req: Request, res: Response) => {
			try {
				const {
					q,
					category,
					country,
					technology,
					ssl_grade,
					min_rank,
					max_rank,
					min_score,
					max_score,
					sort,
					page = 1,
					limit = 20
				} = req.query;

				if (!this.domainSearchService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain search service not initialized'
					});
				}

				const searchResults = await this.domainSearchService.searchDomains({
					query: q as string,
					category: category as string,
					country: country as string,
					technology: technology as string,
					sslGrade: ssl_grade as string,
					minRank: min_rank ? parseInt(min_rank as string) : undefined,
					maxRank: max_rank ? parseInt(max_rank as string) : undefined,
					minScore: min_score ? parseFloat(min_score as string) : undefined,
					maxScore: max_score ? parseFloat(max_score as string) : undefined,
					sort: sort as string,
					page: parseInt(page as string),
					limit: parseInt(limit as string)
				});

				res.json(searchResults);
			}
			catch (error) {
				logger.error('Domain search error:', error);
				res.status(500).json({
					error: 'Search failed',
					message: (error as Error).message
				});
			}
		});

		// Domain analysis API
		this.app.get('/api/domains/:domain/analysis', async (req: Request, res: Response) => {
			try {
				const { domain } = req.params;

				if (!this.domainAnalysisService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain analysis service not initialized'
					});
				}

				const analysis = await this.domainAnalysisService.getDomainAnalysis(domain);

				if (!analysis) {
					return res.status(404).json({
						error: 'Domain not found',
						message: `Analysis for domain ${domain} not found`
					});
				}

				res.json(analysis);
			}
			catch (error) {
				logger.error('Domain analysis error:', error);
				res.status(500).json({
					error: 'Analysis failed',
					message: (error as Error).message
				});
			}
		});

		// Top domains API
		this.app.get('/api/domains/top', async (req: Request, res: Response) => {
			try {
				const { category, country, limit = 50, offset = 0, timeframe } = req.query;

				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				const topDomains = await this.topDomainsService.getTopDomains({
					category: category as string,
					country: country as string,
					limit: parseInt(limit as string),
					offset: parseInt(offset as string),
					timeframe: timeframe as string
				});

				res.json(topDomains);
			}
			catch (error) {
				logger.error('Top domains error:', error);
				res.status(500).json({
					error: 'Failed to get top domains',
					message: (error as Error).message
				});
			}
		});

		// Domain comparison API
		this.app.post('/api/domains/compare', async (req: Request, res: Response) => {
			try {
				const { domains } = req.body;

				if (!domains || !Array.isArray(domains) || domains.length < 2) {
					return res.status(400).json({
						error: 'Invalid request',
						message: 'At least 2 domains required for comparison'
					});
				}

				if (!this.domainAnalysisService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain analysis service not initialized'
					});
				}

				const comparison = await this.domainAnalysisService.compareDomains(domains);
				res.json(comparison);
			}
			catch (error) {
				logger.error('Domain comparison error:', error);
				res.status(500).json({
					error: 'Comparison failed',
					message: (error as Error).message
				});
			}
		});

		// Search suggestions API
		this.app.get('/api/domains/search/suggestions', async (req: Request, res: Response) => {
			try {
				const { q, limit = 10 } = req.query;

				if (!q || typeof q !== 'string') {
					return res.status(400).json({
						error: 'Invalid request',
						message: 'Query parameter "q" is required'
					});
				}

				if (!this.domainSearchService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain search service not initialized'
					});
				}

				const suggestions = await this.domainSearchService.getSearchSuggestions(
					q,
					parseInt(limit as string)
				);

				res.json({ suggestions });
			}
			catch (error) {
				logger.error('Search suggestions error:', error);
				res.status(500).json({
					error: 'Suggestions failed',
					message: (error as Error).message
				});
			}
		});

		// Popular searches API
		this.app.get('/api/domains/search/popular', async (req: Request, res: Response) => {
			try {
				if (!this.domainSearchService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain search service not initialized'
					});
				}

				const popularSearches = await this.domainSearchService.getPopularSearches();
				res.json(popularSearches);
			}
			catch (error) {
				logger.error('Popular searches error:', error);
				res.status(500).json({
					error: 'Popular searches failed',
					message: (error as Error).message
				});
			}
		});

		// Search statistics API
		this.app.get('/api/domains/search/stats', async (req: Request, res: Response) => {
			try {
				if (!this.domainSearchService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain search service not initialized'
					});
				}

				const stats = await this.domainSearchService.getSearchStats();
				res.json(stats);
			}
			catch (error) {
				logger.error('Search stats error:', error);
				res.status(500).json({
					error: 'Search stats failed',
					message: (error as Error).message
				});
			}
		});

		// Domain ranking explanation API
		this.app.get('/api/domains/:domain/ranking', async (req: Request, res: Response) => {
			try {
				const { domain } = req.params;

				if (!this.domainAnalysisService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Domain analysis service not initialized'
					});
				}

				const explanation = await this.domainAnalysisService.getDomainRankingExplanation(domain);

				if (!explanation) {
					return res.status(404).json({
						error: 'Domain not found',
						message: `Ranking explanation for domain ${domain} not found`
					});
				}

				res.json(explanation);
			}
			catch (error) {
				logger.error('Ranking explanation error:', error);
				res.status(500).json({
					error: 'Ranking explanation failed',
					message: (error as Error).message
				});
			}
		});

		// Global rankings API
		this.app.get('/api/rankings/global', async (req: Request, res: Response) => {
			try {
				const { page = 1, limit = 50, sort } = req.query;

				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				const rankings = await this.topDomainsService.getGlobalRankings({
					page: parseInt(page as string),
					limit: parseInt(limit as string),
					sort: sort as string
				});

				res.json(rankings);
			}
			catch (error) {
				logger.error('Global rankings error:', error);
				res.status(500).json({
					error: 'Failed to get global rankings',
					message: (error as Error).message
				});
			}
		});

		// Category rankings API
		this.app.get('/api/rankings/category/:category', async (req: Request, res: Response) => {
			try {
				const { category } = req.params;
				const { page = 1, limit = 50 } = req.query;

				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				const rankings = await this.topDomainsService.getCategoryRankings({
					category,
					page: parseInt(page as string),
					limit: parseInt(limit as string)
				});

				res.json(rankings);
			}
			catch (error) {
				logger.error('Category rankings error:', error);
				res.status(500).json({
					error: 'Failed to get category rankings',
					message: (error as Error).message
				});
			}
		});

		// Available categories API
		this.app.get('/api/categories', async (req: Request, res: Response) => {
			try {
				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				const categories = await this.topDomainsService.getAvailableCategories();
				res.json(categories);
			}
			catch (error) {
				logger.error('Categories error:', error);
				res.status(500).json({
					error: 'Failed to get categories',
					message: (error as Error).message
				});
			}
		});

		// Trending domains API
		this.app.get('/api/domains/trending', async (req: Request, res: Response) => {
			try {
				const { timeframe = '7d', limit = 20 } = req.query;

				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				const trending = await this.topDomainsService.getTrendingDomains({
					timeframe: timeframe as string,
					limit: parseInt(limit as string)
				});

				res.json(trending);
			}
			catch (error) {
				logger.error('Trending domains error:', error);
				res.status(500).json({
					error: 'Failed to get trending domains',
					message: (error as Error).message
				});
			}
		});

		// Cache invalidation API (for admin use)
		this.app.post('/api/cache/invalidate', async (req: Request, res: Response) => {
			try {
				const { category } = req.body;

				if (!this.topDomainsService) {
					return res.status(503).json({
						error: 'Service unavailable',
						message: 'Top domains service not initialized'
					});
				}

				await this.topDomainsService.invalidateCache(category);

				res.json({
					success: true,
					message: category ? `Cache invalidated for category: ${category}` : 'All cache invalidated'
				});
			}
			catch (error) {
				logger.error('Cache invalidation error:', error);
				res.status(500).json({
					error: 'Cache invalidation failed',
					message: (error as Error).message
				});
			}
		});
	}









	/**
	 * Setup error handling middleware
	 */
	setupErrorHandling(): void {
		// 404 handler
		this.app.use((req: Request, res: Response) => {
			// Check if it's an API request
			if (req.path.startsWith('/api/')) {
				res.status(404).json({
					error: 'Not Found',
					message: `API endpoint ${req.method} ${req.path} not found`
				});
			} else {
				// Render 404 page for web requests
				try {
					const pageData = ReactRenderer.renderPage({
						component: 'HomePage',
						props: {
							initialData: { error: '404', message: 'Page not found' }
						},
						title: '404 - Page Not Found',
						description: 'The page you are looking for could not be found.'
					});
					res.status(404).render('layout', pageData);
				} catch (error) {
					res.status(404).send('Page not found');
				}
			}
		});

		// Global error handler
		this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
			logger.error('Unhandled error:', error);

			// Check if it's an API request
			if (req.path.startsWith('/api/')) {
				res.status(500).json({
					error: 'Internal Server Error',
					message: config.isDevelopment() ? error.message : 'Something went wrong'
				});
			} else {
				// Render error page for web requests
				try {
					const pageData = ReactRenderer.renderPage({
						component: 'HomePage',
						props: {
							initialData: { error: '500', message: 'Internal server error' }
						},
						title: '500 - Server Error',
						description: 'An internal server error occurred.'
					});
					res.status(500).render('layout', pageData);
				} catch (renderError) {
					res.status(500).send('Internal server error');
				}
			}
		});
	}

	/**
	 * Start the web application server
	 */
	async start(): Promise<void> {
		const port = config.get('SERVICE_PORT', Constants.SERVICES.WEB_APP);

		return new Promise((resolve, reject) => {
			try {
				this.server = this.app.listen(port, () => {
					logger.info(`Web Application Service started on port ${port}`);
					resolve();
				});

				// Handle server errors if the server object supports it
				if (this.server && typeof this.server.on === 'function') {
					this.server.on('error', (error: Error) => {
						logger.error('Server error:', error);
						reject(error);
					});
				}
			} catch (error) {
				logger.error('Failed to start server:', error);
				reject(error);
			}
		});
	}

	/**
	 * Stop the web application server
	 */
	async stop(): Promise<void> {
		if (this.server) {
			return new Promise((resolve) => {
				this.server!.close(() => {
					logger.info('Web Application Service stopped');
					resolve();
				});
			});
		}
	}

	/**
	 * Graceful shutdown
	 */
	async shutdown(): Promise<void> {
		try {
			logger.info('Shutting down Web Application Service...');
			await this.stop();
			await this.dbManager.close();
			logger.info('Web Application Service shut down successfully');
		}
		catch (error) {
			logger.error('Error during shutdown:', error);
			throw error;
		}
	}
}

// Initialize and start the service
async function main(): Promise<void> {
	const webApp = new WebAppService();

	try {
		await webApp.initialize();
		await webApp.start();

		// Graceful shutdown handling
		process.on('SIGTERM', async () => {
			await webApp.shutdown();
			process.exit(0);
		});

		process.on('SIGINT', async () => {
			await webApp.shutdown();
			process.exit(0);
		});
	}
	catch (error) {
		logger.error('Failed to start Web Application Service:', error);
		process.exit(1);
	}
}

// Start the service if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main();
}

export default WebAppService;
