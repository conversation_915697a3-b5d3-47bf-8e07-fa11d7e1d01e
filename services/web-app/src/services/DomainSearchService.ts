import { DatabaseManager, logger, ManticoreClient } from '@shared';

/**
 * Domain Search Service
 * Handles domain search operations with Manticore integration
 */
class DomainSearchService
{
	private dbManager: DatabaseManager;

	private manticoreClient: ManticoreClient;

	private logger = logger.getLogger('DomainSearchService');

	constructor(dbManager: DatabaseManager)
	{
		this.dbManager = dbManager;
		this.manticoreClient = dbManager.getManticoreClient();
	}

	/**
	 * Search domains with faceted filtering and pagination
	 */
	async searchDomains(params: {
		query?: string;
		category?: string;
		country?: string;
		technology?: string;
		sslGrade?: string;
		minRank?: number;
		maxRank?: number;
		minScore?: number;
		maxScore?: number;
		sort?: string;
		page?: number;
		limit?: number;
	}): Promise<{
		domains: any[];
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
			hasNext: boolean;
			hasPrev: boolean;
		};
		facets: Record<string, any>;
		filters: Record<string, any>;
		took: number;
	}>
	{
		try
		{
			const page = Math.max(1, params.page || 1);
			const limit = Math.min(100, Math.max(1, params.limit || 20));
			const offset = (page - 1) * limit;

			// Build filters
			const filters: Record<string, any> = {};
			if (params.category) filters.category = params.category;
			if (params.country) filters.country = params.country;
			if (params.technology) filters.technologies = params.technology;
			if (params.sslGrade) filters.ssl_grade = params.sslGrade;
			if (params.minRank) filters.min_rank = params.minRank;
			if (params.maxRank) filters.max_rank = params.maxRank;
			if (params.minScore) filters.min_score = params.minScore;
			if (params.maxScore) filters.max_score = params.maxScore;

			// Execute search
			const searchResult = await this.manticoreClient.searchDomains({
				query: params.query,
				filters,
				sort: params.sort || 'score',
				limit,
				offset,
				facets: ['category', 'country', 'technologies', 'ssl_grade'],
			});

			// Calculate pagination
			const totalPages = Math.ceil(searchResult.total / limit);
			const pagination = {
				page,
				limit,
				total: searchResult.total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			};

			this.logger.info(`Domain search completed: ${searchResult.results.length} results in ${searchResult.took}ms`);

			return {
				domains: searchResult.results,
				pagination,
				facets: searchResult.facets,
				filters,
				took: searchResult.took,
			};
		}
		catch (error)
		{
			this.logger.error('Domain search failed:', error);
			throw error;
		}
	}

	/**
	 * Get search suggestions based on partial query
	 */
	async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]>
	{
		try
		{
			if (!query || query.length < 2)
			{
				return [];
			}

			// Search for domains that start with the query
			const searchResult = await this.manticoreClient.searchDomains({
				query: `${query}*`,
				limit,
				offset: 0,
			});

			// Extract unique domain suggestions
			const suggestions = searchResult.results
				.map(result => result.domain)
				.filter((domain, index, array) => array.indexOf(domain) === index)
				.slice(0, limit);

			return suggestions;
		}
		catch (error)
		{
			this.logger.error('Search suggestions failed:', error);
			return [];
		}
	}

	/**
	 * Get popular search terms and categories
	 */
	async getPopularSearches(): Promise<{
		categories: Array<{ name: string; count: number }>;
		technologies: Array<{ name: string; count: number }>;
		countries: Array<{ name: string; count: number }>;
	}>
	{
		try
		{
			// Get facet data for popular terms
			const searchResult = await this.manticoreClient.searchDomains({
				query: '',
				limit: 1,
				offset: 0,
				facets: ['category', 'technologies', 'country'],
			});

			return {
				categories: searchResult.facets.category || [],
				technologies: searchResult.facets.technologies || [],
				countries: searchResult.facets.country || [],
			};
		}
		catch (error)
		{
			this.logger.error('Popular searches failed:', error);
			return {
				categories: [],
				technologies: [],
				countries: [],
			};
		}
	}

	/**
	 * Get search statistics
	 */
	async getSearchStats(): Promise<{
		totalDomains: number;
		totalCategories: number;
		totalCountries: number;
		totalTechnologies: number;
		lastUpdated: string;
	}>
	{
		try
		{
			// Get total count and facet statistics
			const searchResult = await this.manticoreClient.searchDomains({
				query: '',
				limit: 0,
				offset: 0,
				facets: ['category', 'technologies', 'country'],
			});

			return {
				totalDomains: searchResult.total,
				totalCategories: searchResult.facets.category?.length || 0,
				totalCountries: searchResult.facets.country?.length || 0,
				totalTechnologies: searchResult.facets.technologies?.length || 0,
				lastUpdated: new Date().toISOString(),
			};
		}
		catch (error)
		{
			this.logger.error('Search stats failed:', error);
			return {
				totalDomains: 0,
				totalCategories: 0,
				totalCountries: 0,
				totalTechnologies: 0,
				lastUpdated: new Date().toISOString(),
			};
		}
	}
}

export { DomainSearchService };

export default DomainSearchService;
