import {
	DatabaseManager, logger, ScyllaClient, ManticoreClient, RedisClientWrapper,
} from '@shared';

/**
 * Top Domains Service
 * Handles top domains operations with category filtering and caching
 */
class TopDomainsService
{
	private dbManager: DatabaseManager;

	private scyllaClient: ScyllaClient;

	private manticoreClient: ManticoreClient;

	private redisClient: RedisClientWrapper;

	private logger = logger.getLogger('TopDomainsService');

	// Cache keys
	private readonly CACHE_KEYS = {
		topDomains: (category: string, limit: number) => `top_domains:${category}:${limit}`,
		categories: 'top_domains:categories',
		globalRankings: (page: number, limit: number) => `global_rankings:${page}:${limit}`,
		categoryRankings: (category: string, page: number, limit: number) => `category_rankings:${category}:${page}:${limit}`,
	};

	// Cache TTL (Time To Live) in seconds
	private readonly CACHE_TTL = {
		topDomains: 3600, // 1 hour
		categories: 7200, // 2 hours
		rankings: 1800, // 30 minutes
	};

	constructor(dbManager: DatabaseManager)
	{
		this.dbManager = dbManager;
		this.scyllaClient = dbManager.getScyllaClient();
		this.manticoreClient = dbManager.getManticoreClient();
		this.redisClient = dbManager.getRedisClient();
	}

	/**
	 * Get top domains with category filtering
	 */
	async getTopDomains(params: {
		category?: string;
		country?: string;
		limit?: number;
		offset?: number;
		timeframe?: string;
	}): Promise<{
		domains: Array<{
			domain: string;
			globalRank: number;
			categoryRank: number;
			category: string;
			country: string;
			scores: {
				overall: number;
				performance: number;
				security: number;
				seo: number;
				technical: number;
			};
			trafficEstimate: number;
			trend: string;
			lastUpdated: string;
		}>;
		category: string;
		country: string;
		timeframe: string;
		total: number;
		timestamp: string;
	}>
	{
		try
		{
			const category = params.category || 'all';
			const country = params.country || 'all';
			const limit = Math.min(500, Math.max(1, params.limit || 50));
			const offset = Math.max(0, params.offset || 0);
			const timeframe = params.timeframe || 'current';

			// Check cache first
			const cacheKey = this.CACHE_KEYS.topDomains(category, limit);
			const cached = await this.getCachedResult(cacheKey);
			if (cached)
			{
				this.logger.debug(`Returning cached top domains for ${category}`);
				return cached;
			}

			// Use Manticore for fast retrieval
			const searchResult = await this.manticoreClient.getTopDomains({
				category: category !== 'all' ? category : undefined,
				country: country !== 'all' ? country : undefined,
				limit,
				offset,
			});

			// Transform results
			const domains = searchResult.results.map(result => ({
				domain: result.domain,
				globalRank: result.globalRank || 0,
				categoryRank: this.calculateCategoryRank(result, category),
				category: result.category || 'Unknown',
				country: result.country || 'Unknown',
				scores: {
					overall: result.scores?.overall || 0,
					performance: result.scores?.performance || 0,
					security: result.scores?.security || 0,
					seo: result.scores?.seo || 0,
					technical: result.scores?.technical || 0,
				},
				trafficEstimate: result.trafficEstimate || 0,
				trend: this.calculateTrend(result),
				lastUpdated: result.lastUpdated || new Date().toISOString(),
			}));

			const result = {
				domains,
				category,
				country,
				timeframe,
				total: searchResult.total,
				timestamp: new Date().toISOString(),
			};

			// Cache the result
			await this.setCachedResult(cacheKey, result, this.CACHE_TTL.topDomains);

			this.logger.info(`Retrieved ${domains.length} top domains for category: ${category}`);
			return result;
		}
		catch (error)
		{
			this.logger.error('Top domains retrieval failed:', error);
			throw error;
		}
	}

	/**
	 * Get global rankings with pagination
	 */
	async getGlobalRankings(params: {
		page?: number;
		limit?: number;
		sort?: string;
	}): Promise<{
		domains: Array<{
			rank: number;
			domain: string;
			category: string;
			overallScore: number;
			trafficEstimate: number;
			trend: string;
		}>;
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
			hasNext: boolean;
			hasPrev: boolean;
		};
		timestamp: string;
	}>
	{
		try
		{
			const page = Math.max(1, params.page || 1);
			const limit = Math.min(100, Math.max(1, params.limit || 50));
			const offset = (page - 1) * limit;
			const sort = params.sort || 'rank';

			// Check cache first
			const cacheKey = this.CACHE_KEYS.globalRankings(page, limit);
			const cached = await this.getCachedResult(cacheKey);
			if (cached)
			{
				this.logger.debug(`Returning cached global rankings page ${page}`);
				return cached;
			}

			// Query ScyllaDB for global rankings
			const query = `
				SELECT domain, overall_score, performance_score, security_score,
				       seo_score, technical_score, traffic_estimate, rank
				FROM domain_rankings
				WHERE ranking_type = 'global'
				ORDER BY rank ASC
				LIMIT ? OFFSET ?
			`;

			const rankingsResult = await this.scyllaClient.execute(query, [limit, offset]);

			// Get total count
			const countQuery = `
				SELECT COUNT(*) as total
				FROM domain_rankings
				WHERE ranking_type = 'global'
			`;
			const countResult = await this.scyllaClient.execute(countQuery);
			const total = countResult.rows[0]?.total || 0;

			// Transform results
			const domains = rankingsResult.rows.map((row, index) => ({
				rank: offset + index + 1,
				domain: row.domain,
				category: row.category || 'Unknown',
				overallScore: row.overall_score || 0,
				trafficEstimate: row.traffic_estimate || 0,
				trend: this.calculateTrendFromScore(row.overall_score),
			}));

			// Calculate pagination
			const totalPages = Math.ceil(total / limit);
			const pagination = {
				page,
				limit,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			};

			const result = {
				domains,
				pagination,
				timestamp: new Date().toISOString(),
			};

			// Cache the result
			await this.setCachedResult(cacheKey, result, this.CACHE_TTL.rankings);

			this.logger.info(`Retrieved global rankings page ${page} with ${domains.length} domains`);
			return result;
		}
		catch (error)
		{
			this.logger.error('Global rankings retrieval failed:', error);
			throw error;
		}
	}

	/**
	 * Get category-specific rankings
	 */
	async getCategoryRankings(params: {
		category: string;
		page?: number;
		limit?: number;
	}): Promise<{
		domains: Array<{
			categoryRank: number;
			globalRank: number;
			domain: string;
			overallScore: number;
			categoryScore: number;
			trafficEstimate: number;
		}>;
		category: string;
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
			hasNext: boolean;
			hasPrev: boolean;
		};
		timestamp: string;
	}>
	{
		try
		{
			const category = params.category;
			const page = Math.max(1, params.page || 1);
			const limit = Math.min(100, Math.max(1, params.limit || 50));
			const offset = (page - 1) * limit;

			// Check cache first
			const cacheKey = this.CACHE_KEYS.categoryRankings(category, page, limit);
			const cached = await this.getCachedResult(cacheKey);
			if (cached)
			{
				this.logger.debug(`Returning cached category rankings for ${category} page ${page}`);
				return cached;
			}

			// Query ScyllaDB for category rankings
			const rankingType = `category:${category.toLowerCase()}`;
			const query = `
				SELECT domain, overall_score, rank, traffic_estimate
				FROM domain_rankings
				WHERE ranking_type = ?
				ORDER BY rank ASC
				LIMIT ? OFFSET ?
			`;

			const rankingsResult = await this.scyllaClient.execute(query, [rankingType, limit, offset]);

			// Get total count for category
			const countQuery = `
				SELECT COUNT(*) as total
				FROM domain_rankings
				WHERE ranking_type = ?
			`;
			const countResult = await this.scyllaClient.execute(countQuery, [rankingType]);
			const total = countResult.rows[0]?.total || 0;

			// Get global ranks for these domains
			const domains = [];
			for (const row of rankingsResult.rows)
			{
				const globalRankQuery = `
					SELECT rank as global_rank
					FROM domain_rankings
					WHERE ranking_type = 'global' AND domain = ?
				`;
				const globalRankResult = await this.scyllaClient.execute(globalRankQuery, [row.domain]);
				const globalRank = globalRankResult.rows[0]?.global_rank || 0;

				domains.push({
					categoryRank: row.rank,
					globalRank,
					domain: row.domain,
					overallScore: row.overall_score || 0,
					categoryScore: row.overall_score || 0, // For now, same as overall
					trafficEstimate: row.traffic_estimate || 0,
				});
			}

			// Calculate pagination
			const totalPages = Math.ceil(total / limit);
			const pagination = {
				page,
				limit,
				total,
				totalPages,
				hasNext: page < totalPages,
				hasPrev: page > 1,
			};

			const result = {
				domains,
				category,
				pagination,
				timestamp: new Date().toISOString(),
			};

			// Cache the result
			await this.setCachedResult(cacheKey, result, this.CACHE_TTL.rankings);

			this.logger.info(`Retrieved category rankings for ${category} page ${page} with ${domains.length} domains`);
			return result;
		}
		catch (error)
		{
			this.logger.error('Category rankings retrieval failed:', error);
			throw error;
		}
	}

	/**
	 * Get available categories
	 */
	async getAvailableCategories(): Promise<{
		categories: Array<{
			name: string;
			displayName: string;
			count: number;
			description: string;
		}>;
		timestamp: string;
	}>
	{
		try
		{
			// Check cache first
			const cacheKey = this.CACHE_KEYS.categories;
			const cached = await this.getCachedResult(cacheKey);
			if (cached)
			{
				this.logger.debug('Returning cached categories');
				return cached;
			}

			// Get categories from Manticore facets
			const searchResult = await this.manticoreClient.searchDomains({
				query: '',
				limit: 1,
				offset: 0,
				facets: ['category'],
			});

			const categories = (searchResult.facets.category || []).map((facet: any) => ({
				name: facet.value.toLowerCase(),
				displayName: this.formatCategoryName(facet.value),
				count: facet.count,
				description: this.getCategoryDescription(facet.value),
			}));

			const result = {
				categories,
				timestamp: new Date().toISOString(),
			};

			// Cache the result
			await this.setCachedResult(cacheKey, result, this.CACHE_TTL.categories);

			this.logger.info(`Retrieved ${categories.length} available categories`);
			return result;
		}
		catch (error)
		{
			this.logger.error('Categories retrieval failed:', error);
			throw error;
		}
	}

	/**
	 * Get trending domains
	 */
	async getTrendingDomains(params: {
		timeframe?: string;
		limit?: number;
	}): Promise<{
		domains: Array<{
			domain: string;
			category: string;
			currentRank: number;
			previousRank: number;
			rankChange: number;
			trendDirection: string;
			overallScore: number;
		}>;
		timeframe: string;
		timestamp: string;
	}>
	{
		try
		{
			const timeframe = params.timeframe || '7d';
			const limit = Math.min(100, Math.max(1, params.limit || 20));

			// Query ranking history to find trending domains
			const query = `
				SELECT domain, ranking_type, rank, score, date
				FROM domain_ranking_history
				WHERE ranking_type = 'global'
				  AND date >= ?
				ORDER BY date DESC, rank ASC
				LIMIT ?
			`;

			const cutoffDate = this.getTimeframeCutoff(timeframe);
			const historyResult = await this.scyllaClient.execute(query, [cutoffDate, limit * 2]);

			// Process trending logic (simplified for now)
			const domains = historyResult.rows.slice(0, limit).map((row, index) => ({
				domain: row.domain,
				category: 'Unknown', // Would need to join with domain_analysis
				currentRank: row.rank,
				previousRank: row.rank + Math.floor(Math.random() * 10), // Placeholder
				rankChange: Math.floor(Math.random() * 20) - 10, // Placeholder
				trendDirection: Math.random() > 0.5 ? 'up' : 'down',
				overallScore: row.score || 0,
			}));

			const result = {
				domains,
				timeframe,
				timestamp: new Date().toISOString(),
			};

			this.logger.info(`Retrieved ${domains.length} trending domains for timeframe: ${timeframe}`);
			return result;
		}
		catch (error)
		{
			this.logger.error('Trending domains retrieval failed:', error);
			throw error;
		}
	}

	/**
	 * Invalidate cache for top domains
	 */
	async invalidateCache(category?: string): Promise<void>
	{
		try
		{
			if (category)
			{
				// Invalidate specific category cache
				const pattern = `top_domains:${category}:*`;
				await this.redisClient.invalidatePattern(pattern);
				this.logger.info(`Invalidated cache for category: ${category}`);
			}
			else
			{
				// Invalidate all top domains cache
				await this.redisClient.invalidatePattern('top_domains:*');
				await this.redisClient.invalidatePattern('global_rankings:*');
				await this.redisClient.invalidatePattern('category_rankings:*');
				this.logger.info('Invalidated all top domains cache');
			}
		}
		catch (error)
		{
			this.logger.error('Cache invalidation failed:', error);
		}
	}

	/**
	 * Get cached result
	 */
	private async getCachedResult(key: string): Promise<any>
	{
		try
		{
			const cached = await this.redisClient.get(key);
			return cached ? JSON.parse(cached) : null;
		}
		catch (error)
		{
			this.logger.warn('Cache retrieval failed:', error);
			return null;
		}
	}

	/**
	 * Set cached result
	 */
	private async setCachedResult(key: string, data: any, ttl: number): Promise<void>
	{
		try
		{
			await this.redisClient.setex(key, ttl, JSON.stringify(data));
		}
		catch (error)
		{
			this.logger.warn('Cache storage failed:', error);
		}
	}

	/**
	 * Calculate category rank
	 */
	private calculateCategoryRank(result: any, category: string): number
	{
		// Simplified calculation - in real implementation would query category-specific rankings
		return result.globalRank ? Math.floor(result.globalRank * 0.8) : 0;
	}

	/**
	 * Calculate trend
	 */
	private calculateTrend(result: any): string
	{
		// Simplified trend calculation
		const score = result.scores?.overall || 0;
		if (score > 0.8) return 'up';
		if (score < 0.4) return 'down';
		return 'stable';
	}

	/**
	 * Calculate trend from score
	 */
	private calculateTrendFromScore(score: number): string
	{
		if (score > 0.8) return 'up';
		if (score < 0.4) return 'down';
		return 'stable';
	}

	/**
	 * Format category name for display
	 */
	private formatCategoryName(category: string): string
	{
		return category.split(/[-_]/)
			.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
			.join(' ');
	}

	/**
	 * Get category description
	 */
	private getCategoryDescription(category: string): string
	{
		const descriptions: Record<string, string> = {
			technology: 'Technology and software companies',
			business: 'Business and corporate websites',
			news: 'News and media organizations',
			ecommerce: 'E-commerce and online retail',
			education: 'Educational institutions and resources',
			entertainment: 'Entertainment and media content',
			health: 'Healthcare and medical services',
			finance: 'Financial services and institutions',
		};

		return descriptions[category.toLowerCase()] || 'Various websites and services';
	}

	/**
	 * Get timeframe cutoff date
	 */
	private getTimeframeCutoff(timeframe: string): Date
	{
		const now = new Date();
		switch (timeframe)
		{
			case '1d':
				return new Date(now.getTime() - 24 * 60 * 60 * 1000);
			case '7d':
				return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
			case '30d':
				return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
			default:
				return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
		}
	}
}

export { TopDomainsService };

export default TopDomainsService;
