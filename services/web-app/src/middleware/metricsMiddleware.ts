import { Request, Response, NextFunction } from 'ultimate-express';
import { MetricsCollector, CommonMetrics } from '@shared/monitoring/MetricsCollector';
import Logger from '@shared/utils/Logger';

const logger = Logger.getLogger('MetricsMiddleware');
const metricsCollector = new MetricsCollector();
const commonMetrics = new CommonMetrics(metricsCollector);

// Extend Request interface to include metrics timing
declare global {
	namespace UltimateExpress {
		interface Request {
			startTime?: number;
		}
	}
}

/**
 * Middleware to collect HTTP request metrics
 */
function metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
	// Record request start time
	req.startTime = Date.now();

	// Increment active requests counter
	metricsCollector.gauge('http_active_requests', 1);

	// Override res.end to capture response metrics
	const originalEnd = res.end;
	res.end = function(chunk?: any, encoding?: any, cb?: any) {
		try {
			const duration = Date.now() - (req.startTime || Date.now());
			const method = req.method;
			const path = getRoutePath(req);
			const statusCode = res.statusCode;

			// Record HTTP metrics
			commonMetrics.httpRequest(method, path, statusCode, duration);

			// Record response size if available
			const contentLength = res.get('content-length');
			if (contentLength) {
				metricsCollector.histogram('http_response_size_bytes', parseInt(contentLength), {
					method,
					path,
					status_code: statusCode.toString()
				});
			}

			// Decrement active requests counter
			metricsCollector.gauge('http_active_requests', -1);

			// Log slow requests
			if (duration > 5000) { // 5 seconds
				logger.warn(`Slow request detected: ${method} ${path} took ${duration}ms`);
			}

		} catch (error) {
			logger.error('Error recording HTTP metrics:', error);
		}

		// Call original end method
		return originalEnd.call(this, chunk, encoding, cb);
	};

	next();
}

/**
 * Get normalized route path for metrics
 */
function getRoutePath(req: Request): string {
	// Use route path if available (from ultimate-express router)
	if (req.route && req.route.path) {
		return req.route.path;
	}

	// Normalize common patterns
	let path = req.path;

	// Replace domain names with placeholder
	path = path.replace(/\/api\/domains\/[a-zA-Z0-9.-]+/, '/api/domains/:domain');

	// Replace UUIDs with placeholder
	path = path.replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id');

	// Replace numeric IDs with placeholder
	path = path.replace(/\/\d+/g, '/:id');

	return path;
}

/**
 * Middleware to collect database operation metrics
 */
function databaseMetricsWrapper<T>(
	database: string,
	operation: string,
	fn: () => Promise<T>
): Promise<T> {
	return metricsCollector.time(
		'database_operation_duration',
		async () => {
			try {
				const result = await fn();
				commonMetrics.databaseQuery(database, operation, 0, true);
				return result;
			} catch (error) {
				commonMetrics.databaseQuery(database, operation, 0, false);
				throw error;
			}
		},
		{ database, operation }
	);
}

/**
 * Middleware to collect cache operation metrics
 */
function cacheMetricsWrapper<T>(
	operation: string,
	fn: () => Promise<T | null>
): Promise<T | null> {
	return metricsCollector.time(
		'cache_operation_duration',
		async () => {
			const result = await fn();
			const hit = result !== null && result !== undefined;
			commonMetrics.cacheOperation(operation, hit);
			return result;
		},
		{ operation }
	);
}

/**
 * Error tracking middleware
 */
function errorMetricsMiddleware(
	error: Error,
	req: Request,
	res: Response,
	next: NextFunction
): void {
	// Record error metrics
	metricsCollector.counter('http_errors_total', 1, {
		method: req.method,
		path: getRoutePath(req),
		error_type: error.name,
		status_code: res.statusCode.toString()
	});

	// Log error details
	logger.error('HTTP Error:', {
		method: req.method,
		path: req.path,
		error: error.message,
		stack: error.stack,
		statusCode: res.statusCode
	});

	next(error);
}

/**
 * Rate limiting metrics middleware
 */
function rateLimitMetricsMiddleware(req: Request, res: Response, next: NextFunction): void {
	// Check if request was rate limited
	const rateLimited = res.statusCode === 429;

	if (rateLimited) {
		metricsCollector.counter('http_rate_limited_total', 1, {
			method: req.method,
			path: getRoutePath(req),
			ip: req.ip
		});
	}

	next();
}

/**
 * Custom business metrics helpers
 */
class BusinessMetrics {
	private metrics: MetricsCollector;

	constructor() {
		this.metrics = metricsCollector;
	}

	/**
	 * Record domain search metrics
	 */
	domainSearch(query: string, resultsCount: number, duration: number): void {
		this.metrics.counter('domain_searches_total', 1, {
			has_results: (resultsCount > 0).toString()
		});

		this.metrics.timer('domain_search_duration', duration);
		this.metrics.histogram('domain_search_results_count', resultsCount);
	}

	/**
	 * Record domain analysis metrics
	 */
	domainAnalysis(domain: string, success: boolean, duration: number): void {
		this.metrics.counter('domain_analysis_total', 1, {
			success: success.toString()
		});

		this.metrics.timer('domain_analysis_duration', duration);
	}

	/**
	 * Record ranking calculation metrics
	 */
	rankingCalculation(category: string, domainsCount: number, duration: number): void {
		this.metrics.counter('ranking_calculations_total', 1, {
			category
		});

		this.metrics.timer('ranking_calculation_duration', duration, {
			category
		});

		this.metrics.gauge('ranking_domains_processed', domainsCount, {
			category
		});
	}

	/**
	 * Record crawl job metrics
	 */
	crawlJob(jobType: string, success: boolean, duration: number): void {
		this.metrics.counter('crawl_jobs_total', 1, {
			job_type: jobType,
			success: success.toString()
		});

		this.metrics.timer('crawl_job_duration', duration, {
			job_type: jobType
		});
	}
}

const businessMetrics = new BusinessMetrics();

export {
	metricsMiddleware,
	errorMetricsMiddleware,
	rateLimitMetricsMiddleware,
	databaseMetricsWrapper,
	cacheMetricsWrapper,
	BusinessMetrics,
	businessMetrics,
};

export default {
	metricsMiddleware,
	errorMetricsMiddleware,
	rateLimitMetricsMiddleware,
	databaseMetricsWrapper,
	cacheMetricsWrapper,
	businessMetrics,
};
