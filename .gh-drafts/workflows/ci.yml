name: <PERSON><PERSON> (draft)

on:
  workflow_dispatch:
  pull_request:
    branches: [ main ]

jobs:
  build-test:
    name: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>check, Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false
      - name: Install deps
        run: pnpm install --frozen-lockfile
      - name: Lint
        run: pnpm lint
        continue-on-error: true
      - name: Typecheck
        run: pnpm typecheck
        continue-on-error: true
      - name: Test (workspace)
        run: pnpm test
        env:
          VALIDATE_DESCRIPTIONS: '1'
      - name: Upload coverage
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            coverage
            **/coverage
    # Draft: allow failures while stabilizing
    continue-on-error: true
