# Manticore Search Sync Plan

## Overview
We will add a full-text search indexing pipeline for domains and their descriptions using Manticore Search. The pipeline consists of:
- Index schemas (RT mode) for `domains` and `descriptions`
- A shared Manticore client in @shared
- A Sync Worker service that consumes sync jobs and upserts documents into Manticore
- Publishers in crawler and ranking-engine to enqueue sync jobs on relevant events
- Backfill script to bootstrap indices from ScyllaDB
- Health/metrics and basic retries

This plan does not require a running Manticore instance to review code. For local/dev, docker-compose will provide a default instance; production config goes via env vars.

## Index Design
Two real-time indices:

1) `domains` (by domain)
- id: auto or hash(domain)
- domain (string, attr)
- category_primary (string, attr)
- status (string, attr)
- registrar (string, attr)
- age_days (int, attr)
- technologies (text)
- title (text)
- meta_description (text)
- tags (multi, attr)
- updated_at (timestamp, attr)

2) `descriptions` (rich text for search)
- id: auto or hash(domain)
- domain (string, attr)
- overview (text)
- key_features (text)
- technologies (text)
- seo_text (text)
- category (string, attr)
- language (string, attr)
- updated_at (timestamp, attr)

Notes:
- We will prefer RT indexes with schema-less text fields (Manticore supports dynamic schema via JSON) OR explicit columns; we’ll start explicit for clarity.
- We’ll normalize/marshal nested DomainDescription fields into flat text + attrs.

## Configuration
Shared env via @shared Config:
- MANTICORE_HOST (default: manticore)
- MANTICORE_PORT (default: 9308, HTTP JSON)
- MANTICORE_INDEX_DOMAINS (default: domains)
- MANTICORE_INDEX_DESCRIPTIONS (default: descriptions)
- MANTICORE_SYNC_CONCURRENCY (default: 5)

docker-compose: add a manticore service if not already present (optional for this PR if you have one).

## Shared Client (@shared)
- ManticoreClient.ts: lightweight HTTP client using node fetch/axios replacement (we’ll use native fetch in Node 20) for /insert, /replace, /bulk, /search endpoints.
- Methods: ensureIndex, upsert(index, doc, id?), bulkUpsert(index, docs), delete(index, id), search(query)
- Handle retries (3x) and errors with Logger.

## Job Contract
Queue: `queue:manticore:sync`
Payloads:
- type: "domain" | "description"
- domain: string
- doc?: partial document; if absent, worker fetches from Scylla
- upsert: boolean (default true)

Producers:
- Crawler after crawl stored -> publish for domain baseline + description (if VALIDATE_DESCRIPTIONS or when built)
- Ranking-engine after ranking update -> publish domain with updated ranking text fields (title/meta from seo, computed summary)
- Web-app write paths (if any mutating routes later) -> publish

## Sync Worker (services/manticore-sync)
- Reads jobs from JobQueue
- Resolves doc (either from job.doc or fetches from Scylla via DatabaseManager)
- Maps to index schema (see Mapping below)
- Upserts into the correct index using ManticoreClient
- Metrics: successes, failures, latency; logs w/ Logger; health endpoint

## Mapping from Scylla to Index
Domains index:
- domain: domain_analysis.domain
- category_primary: domain_analysis.category (string)
- status: domain_analysis.crawl_status
- registrar: domain_analysis.registrar
- age_days: domain_analysis.domain_age_days
- technologies: join array domain_analysis.technologies or from homepage_analysis.libraries
- title/meta_description: from seo_metrics.title/description
- tags: from categories or derived
- updated_at: now()

Descriptions index:
- domain: as above
- overview: description.overview.summary
- key_features: description.overview.keyFeatures join
- technologies: description.technical.technologies join
- seo_text: concat of seo.title + seo.metaDescription
- category: description.metadata.category.primary
- language: description.metadata.language or seo.languages[0]
- updated_at: now()

## Backfill Script
- scripts/manticore-backfill.ts: paginates over domain_analysis table, builds docs, bulkUpsert into domains; if description available (later), bulkUpsert into descriptions.

## Health and Metrics
- Health endpoint in worker: /health returns Manticore ping and queue lag
- Metrics via shared MetricsCollector; counters: sync_success, sync_failure, upsert_latency_ms

## Failure Handling
- Retries: client-level (3x) with backoff; job requeue on failure with limited attempts
- Dead-letter: log to a DLQ channel or file via Logger

## Rollout Plan
1. Implement shared ManticoreClient
2. Implement sync worker service skeleton and mapping utils
3. Add job publish hooks in crawler and ranking-engine
4. Optional: docker-compose service and init SQL for RT indexes
5. Backfill script
6. CI jobs (optional) to lint/typecheck

## Do you need a running Manticore?
- For development: local docker compose recommended
- For initial code review: not required; unit tests will mock ManticoreClient
- Integration tests can be gated behind env flag (RUN_MANTICORE_IT=1)

## Open Questions
- Confirm exact fields for indices (add/remove)
- Confirm whether to store ranking scores for filtering/sorting
- Decide on tokenizer/collation for text fields
- Index names/namespaces per environment

## Next Steps
- Approve schema/fields and queue contract
- I’ll scaffold @shared ManticoreClient, a new service `services/manticore-sync`, and publisher hooks.
