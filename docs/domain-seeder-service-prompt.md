# Domain Seeder Service — Prompt/Blueprint

Create a new service at services/domain-seeder that continuously discovers NEW domains we don’t have yet, starting from highest-popularity sources and backfilling downward. It must NOT compute/modify our popularity ranking; only find and enqueue domains absent from our DB. Additionally, generate user-facing content (summary, categories, tags, seoSummary) in two modes: preGenerated (self) and live (internet-checked) with the preGenerated pass done by the seeder and live regeneration later by the scheduler.

Objectives

- Aggregate candidate domains from external sources prioritized by popularity: Tranco, Cloudflare Radar, Cisco Umbrella 1M (if allowed), CZDS/zone files (.com/.net + others), PIR .org, Common Crawl host frequency, Rapid7 FDNS/Project Sonar.
- Canonicalize to eTLD+1 (PSL), punycode, lowercase, collapse www, dedupe.
- Check against our database and only keep domains that don’t exist yet.
- For new domains, generate preGenerated content (summary, primary/secondary category, ≥5 tags, seoSummary) using heuristics.
- Throttle additions with strict backpressure to avoid overloading infra.
- Expose CLI/API to run periodic fetch, report coverage gaps, and backfill long tail.

Deliverables

1. Package scaffold

- services/domain-seeder/{package.json, tsconfig.json, vitest.config.ts, Dockerfile, README.md}
- src/index.ts (entry), src/config.ts (env), src/metrics.ts (Prometheus), src/http.ts (/health, /trigger)
- src/sources/\* connectors: tranco.ts, radar.ts, umbrella.ts, czds.ts, pirOrg.ts, commonCrawl.ts, sonarFdns.ts
- src/normalize/psl.ts, src/filters/rules.ts
- src/db/domainRepository.ts: hasDomain(eTLD1) -> boolean | batchHas(set)
- src/pipeline/{fetchCandidates.ts, normalizeAndDedupe.ts, filterKnown.ts, enqueueNew.ts, generateContent.ts}
- src/queue/newDomainQueue.ts: enqueueNew(domains: string[]) with rate limits

2. Data flow

- Fetch candidates per source in priority order (top lists first), streaming pages/chunks.
- Normalize to eTLD+1, lowercase, punycode; drop IPs/special TLDs; dedupe using a Bloom filter to limit memory.
- Batch existence check against our DB/index (Scylla/Maria/Manticore) using shared database clients; keep only domains not present.
- Generate preGenerated content for each new domain based on Domain Content Generation Plan; set generation.preGenerated=true and method=self.
- Enqueue ONLY missing domains into Redis list/stream new:domains with payload {domain, firstSource, seenAt, contentSnippet}.
- Persist provenance: {domain, firstSeenAt, sources[], preGeneratedContent} in domain_discovery.

3. Prioritization policy (no ranking changes)

- Source order: Tranco → Radar → Umbrella → CZDS core TLDs → Common Crawl → Sonar/FDNS.
- Within each source, process by native rank or magnitude descending.
- Enqueue in tiers: top-10k, 100k, 1M, then long tail.
- Maintain per-TLD quotas per cycle to ensure breadth.

4. Backpressure and quotas

- Env budgets: ENQUEUE_BATCH, ENQUEUE_INTERVAL_MS, MAX_NEW_PER_DAY, MAX_INFLIGHT_DISCOVERY.
- Redis token-bucket for rate limiting; jitter sleeps to avoid bursts.
- Skip enqueue if queue depth > NEW_QUEUE_MAX_DEPTH.
- Idempotent: use Redis SETNX or ZADD NX for discovered:{domain}.

5. DB presence checks

- Batch check API: batchHas(domains: string[]): Promise<Map<domain, boolean>>.
- Query Scylla/Maria/Manticore for existing domains; fallback Redis Bloom filter with acceptable FP rate.
- Cache recent positives in Redis for 24h.

6. Content generation (SEO)
   Parameters: audienceIntent, tone, summaryChars, language, regionHint.
   preGenerated (self): TLD priors, brand lists, basic tags; output overview.summary, metadata.category.primary/secondary, metadata.tags[≥5], seo.seoSummary.
   live (scheduler): after crawl data ready, regenerate using HTML/meta/sitemap/socials and ML classifier; set generation.preGenerated=false.

7. Scheduling & cadence

- Tranco/Radar/Umbrella: daily pulls.
- CZDS/zone diffs: as provided (daily/weekly); ingest diffs for new regs.
- Common Crawl/Sonar: weekly/monthly long-tail sweeps.
- Reconciler: re-check “discovered but not yet in DB” to avoid re-adding.
- Scheduler job: regenerate content in live mode once crawl signals available.

8. Integration points

- Output: Redis new:domains (LIST or STREAM). Message: {domain, firstSource, seenAt, preGenerated: true}.
- Optional high-priority stream: new:domains:high for top-tier seeds.
- Scheduler consumes and assigns crawl jobs; later triggers live content regeneration.

9. Config/Env

- SOURCES_ENABLED=tranco,radar,czds,cc,sonar
- ENQUEUE_BATCH=1000; ENQUEUE_INTERVAL_MS=1000; MAX_NEW_PER_DAY=500000
- NEW_QUEUE_MAX_DEPTH=200000; REDIS_URL=...
- DB_CHECK_BATCH=5000; BLOOM_FP_RATE=0.01
- PSL_UPDATE_INTERVAL_DAYS=7
- CONTENT_LANG_DEFAULT=en; SUMMARY_CHARS=220

10. Metrics & alerts

- candidates_fetched_total{source}; candidates_after_normalize; known_in_db_total; new_discovered_total
- content_generated_total{mode=self|live}; seo_summary_length_hist
- enqueue_attempts_total; enqueue_success_total; rate_limited_total
- queue_depth; db_check_latency_ms; source_staleness_seconds
- Alerts: zero new discovered for 24h; content generation failures; queue depth > threshold for 1h

11. Testing

- Unit: PSL mapping, normalization, dedupe, batchHas, rate limiter, preGenerated heuristics.
- Integration: mock sources → only unknown domains enqueued with content snippet; quotas respected.
- Golden: fixed inputs and DB state → fixed enqueued set and content fields.
- Load: simulate 10M candidates; verify throughput and limits.

12. Security/compliance

- Respect ToS/licensing; CZDS creds via env; no secrets in logs.
- No PII processing; provenance data minimal.

13. CLI commands

- pnpm seeder:run — one full cycle respecting quotas
- pnpm seeder:top — ingest top sources only
- pnpm seeder:status — print discovery counts, queue depth, source freshness
- pnpm seeder:backfill — process long-tail sources within budget

14. Milestones

- M1: Scaffold, Tranco connector, DB presence check, enqueue with quotas, preGenerated content, metrics
- M2: Radar + CZDS connectors, Bloom cache, API/health, alerts
- M3: Common Crawl/Sonar connectors, zone diffs, reconciler, docs; scheduler live regeneration wiring

Acceptance criteria

- Redis new:domains populated only with domains not in our DB; top-tier prioritized; preGenerated content present
- Throughput within ENQUEUE\_\* budgets; bounded queue depth
- Idempotent re-runs; metrics/health green; tests pass

Implementation notes

- Follow repo conventions (TS, ESM, vitest, shared Config/Logger)
- Reuse shared database clients in shared/database; add domainRepository adapter
- Prefer streaming parsers and backpressure-aware pipelines
