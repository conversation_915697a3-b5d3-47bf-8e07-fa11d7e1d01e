# Domain Existence Check Optimization Architecture

## Overview

This document outlines the architecture for optimizing domain existence checks in the domain-seeder service by leveraging Manticore Search as the primary lookup mechanism and eliminating the inefficient Redis cache layer.

## Current Architecture Problems

### Redis Cache Layer Issues

- **Memory Inefficiency**: Storing 100M+ domain existence flags in Redis requires 5-10GB RAM
- **Scalability Bottleneck**: Current implementation has artificial limits that don't scale
- **Redundant Storage**: Domains stored in both ScyllaDB and Redis cache
- **Complex Cache Management**: TTL management, invalidation, and warming add unnecessary complexity
- **Cache Miss Penalty**: Every miss requires expensive database roundtrips

### Current Data Flow

```
Discovery Engine → Redis Cache Check → Cache Miss → ScyllaDB Query → Cache Update
```

## Proposed Architecture

### Core Principle

Use Manticore Search as the single source of truth for domain existence checks, leveraging the existing search index that's already maintained for user searches and internal lookups.

### Data Flow Architecture

```
Discovery Engine → Manticore Search (Batch Query) → ScyllaDB Update (if new domains found)
```

### Key Components

#### 1. Manticore Search as Primary Index

- **Purpose**: Single source of truth for domain existence
- **Advantages**:
  - Already indexed for fast lookups
  - Supports efficient batch operations (10K+ domains per query)
  - Designed for large datasets (100M+ documents)
  - Shared with user search functionality
  - No memory constraints like Redis

#### 2. ScyllaDB as Persistent Storage

- **Purpose**: Authoritative data store for domain records
- **Role**: Receives updates when new domains are discovered
- **Relationship**: Manticore index reflects ScyllaDB content

#### 3. Redis for Operational Efficiency

- **Purpose**: Limited to operational concerns only
  - Deduplication (prevent concurrent checks of same domain)
  - Rate limiting and backpressure control
  - Recent negative results (small cache, short TTL)
  - Processing state tracking
- **Memory Usage**: <100MB (vs 5-10GB in current approach)

## Detailed Architecture

### Domain Existence Check Flow

1. **Batch Preparation**

   - Collect domains from discovery sources
   - Remove duplicates using Redis deduplication
   - Group into optimal batch sizes (10K-50K domains)

2. **Manticore Search Query**

   - Execute batch existence query using Manticore's IN operator
   - Leverage existing domain index for fast lookups
   - Return existence map for all queried domains

3. **Result Processing**

   - Identify new domains not in Manticore index
   - Queue new domains for ScyllaDB insertion
   - Update Redis with recent negative results (short TTL)

4. **ScyllaDB Synchronization**
   - Insert new domain records into ScyllaDB
   - Trigger Manticore index update
   - Maintain data consistency between stores

### Scalability Design

#### Horizontal Scaling

- **Service Nodes**: Multiple domain-seeder instances can run concurrently
- **Database Scaling**: ScyllaDB and Manticore both support horizontal scaling
- **Load Distribution**: Redis-based coordination prevents duplicate work across nodes

#### Performance Optimization

- **Batch Processing**: Process 10K-100K domains per query
- **Connection Pooling**: Maintain persistent connections to databases
- **Async Processing**: Non-blocking operations with proper backpressure
- **Query Optimization**: Leverage Manticore's optimized indexing

### Data Consistency Model

#### Primary Data Flow

```
External Sources → Domain Discovery → Manticore Check → ScyllaDB Insert → Index Update
```

#### Consistency Guarantees

- **Eventually Consistent**: Manticore index reflects ScyllaDB within seconds
- **Conflict Resolution**: ScyllaDB is authoritative for conflicts
- **Recovery**: Automatic index rebuilding from ScyllaDB if needed

## Implementation Components

### 1. OptimizedDomainExistenceService

- Coordinates between Manticore and ScyllaDB
- Implements batch processing logic
- Handles error recovery and fallback scenarios

### 2. ManticoreDomainIndex

- Optimized queries for existence checking
- Batch operations with proper error handling
- Connection management and query optimization

### 3. ScyllaDomainStore

- Efficient domain insertion and updates
- Triggers for Manticore index synchronization
- Consistency monitoring and repair

### 4. RedisOperationalLayer

- Deduplication and rate limiting
- Processing state management
- Minimal memory footprint operations

## Performance Characteristics

### Expected Improvements

- **Memory Usage**: Reduce from 5-10GB to <100MB
- **Query Performance**: 100K+ domain checks in <1 second
- **Scalability**: Support 1B+ domains without architectural changes
- **Startup Time**: Seconds (no cache warming required)
- **Cost Efficiency**: Eliminate large Redis instances

### Benchmark Targets

- **Batch Size**: 50K domains per query
- **Throughput**: 1M+ domain checks per minute per node
- **Latency**: <100ms for batch queries
- **Memory**: <100MB Redis operational cache
- **Availability**: 99.9% uptime with automatic failover

## Migration Strategy

### Phase 1: Parallel Implementation

- Implement new architecture alongside existing Redis cache
- A/B test performance and reliability
- Validate data consistency

### Phase 2: Gradual Migration

- Migrate discovery engines one by one
- Monitor performance and error rates
- Maintain rollback capability

### Phase 3: Cache Elimination

- Remove Redis cache layer
- Simplify codebase and configuration
- Optimize based on production metrics

### Phase 4: Performance Tuning

- Fine-tune batch sizes and connection pools
- Implement advanced caching strategies if needed
- Scale horizontally based on load

## Configuration

### Manticore Configuration

```typescript
manticore: {
  batchSize: 50000,           // Domains per batch query
  queryTimeout: 5000,         // Query timeout in ms
  connectionPool: 20,         // Connection pool size
  indexName: 'domains_index', // Primary domain index
  retryAttempts: 3,          // Query retry attempts
}
```

### ScyllaDB Configuration

```typescript
scylla: {
  batchSize: 10000,          // Insert batch size
  consistencyLevel: 'QUORUM', // Write consistency
  retryPolicy: {             // Retry configuration
    maxAttempts: 3,
    backoffMs: 1000,
  },
}
```

### Redis Configuration

```typescript
redis: {
  deduplicationTtl: 300,     // 5 minutes
  negativeResultTtl: 3600,   // 1 hour
  maxConcurrentChecks: 10000, // Concurrent operations
  keyPrefix: 'domain:op:',   // Operational key prefix
}
```

## Monitoring and Observability

### Key Metrics

- Domain existence check latency and throughput
- Manticore query performance and error rates
- ScyllaDB insertion rates and consistency lag
- Redis operational cache hit rates
- Cross-node coordination efficiency

### Alerting

- High error rates in domain existence checks
- Manticore-ScyllaDB consistency lag
- Memory usage exceeding thresholds
- Query performance degradation

## Security Considerations

### Access Control

- Separate credentials for read (Manticore) and write (ScyllaDB) operations
- Redis access limited to operational functions
- Network segmentation between components

### Data Protection

- No sensitive data in Redis operational cache
- Audit logging for domain discovery operations
- Encryption in transit for all database connections

## Future Enhancements

### Advanced Optimizations

- Bloom filters for negative result caching
- Predictive prefetching based on discovery patterns
- Machine learning for batch size optimization
- Geographic distribution of domain checks

### Integration Opportunities

- Real-time domain discovery streaming
- Integration with domain validation services
- Advanced analytics on domain discovery patterns
- API endpoints for external domain existence checks

This architecture provides a scalable, efficient foundation for domain existence checking that can grow with the service requirements while maintaining high performance and reliability.
