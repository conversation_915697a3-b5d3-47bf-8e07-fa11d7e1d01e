# Domain Description Validation & Integration Plan

## Goals
- Guarantee DomainDescription objects conform to docs/domain-description.schema.json
- Fail fast on bad data; auto-recover with retries; surface issues in logs/metrics
- Make searchable facets consistent across Scylla and Manticore

## Scope
- Shared: Validator utility, types, and schema loading
- Crawler: Build + validate DomainDescription before persistence/indexing
- Ranking Engine: Validate inputs, compute scores, validate outputs
- Web App: New endpoint for description; response validation in dev/test
- Tooling: CLI validator + PNPM scripts + CI check

## Deliverables
1. docs/domain-description.schema.json (done)
2. shared/src/utils/DomainDescriptionValidator.ts
3. shared/src/models/DomainDescription.ts (TS types aligned with schema)
4. Crawler integration: assembly + validation + error handling
5. Ranking Engine integration: validation gates pre/post scoring
6. Web App endpoint: GET /api/domains/:domain/description
7. Scripts: pnpm validate:descriptions, pnpm validate:file <path>
8. Tests: unit for validator; integration in crawler/ranking/web-app

## Design
### Validation Library
- Ajv v8 with 2020-12 draft support
- Pre-compile schema at startup; cache validator
- Strict mode enabled; allErrors=false (fast fail); formats enabled

### Shared Utilities
- DomainDescriptionValidator
  - loadSchema(file=docs/domain-description.schema.json)
  - validate(obj): { ok: boolean; errors?: AjvError[] }
  - assert(obj): void throws ValidationError
- DomainDescription types
  - Generate from schema via typebox/zod-to-ts or hand-maintain initial TS interfaces
  - Export DomainDescription type for services

### Data Flow
1) Crawler
   - Collect raw metrics (DNS, SEO, performance, security)
   - Assemble DomainDescription
   - Validate (assert)
   - Persist to Scylla (domain_analysis, domain_content)
   - Index to Manticore (domains_index, domain_content_index)
   - Enqueue ranking update
   - On validation failure: log structured error, increment error_count, schedule retry with backoff

2) Ranking Engine
   - Fetch DomainDescription from Scylla
   - Validate input (assert)
   - Compute scores/ranks, update `ranking` node
   - Validate output (assert)
   - Persist + reindex; publish update event

3) Web App
   - Endpoint: GET /api/domains/:domain/description
   - Read from Scylla (cached via Redis)
   - Validate in dev/test only (guarded by NODE_ENV)
   - Return 200 with JSON; 502 if missing; 500 on unexpected

### Error Handling & Observability
- Use shared Logger; log ajv errors with path, message, dataPath
- Metrics: counter `validation_failures_total{service}`; gauge last failure timestamp
- Tracing tags `validation.ok`, `validation.errors`

### CLI & Scripts
- shared/bin/validate-description.ts: validate file(s) or stdin
- PNPM
  - root: "validate:descriptions": "node shared/bin/validate-description.ts docs/samples/**/*.json"
  - "validate:file": "node shared/bin/validate-description.ts"
- CI
  - Add to root test workflow: pnpm validate:descriptions

## File Changes
- docs/
  - domain-description-validation-integration-plan.md (this)
  - samples/ (add few example JSONs later)
- shared/
  - src/models/DomainDescription.ts
  - src/utils/DomainDescriptionValidator.ts
  - bin/validate-description.ts
- services/crawler/
  - src/pipelines/DescriptionAssembler.ts
  - integrate in DataCollectionOrchestrator
- services/ranking-engine/
  - src/services/DescriptionScoringService.ts
- services/web-app/
  - src/routes/description.ts (GET /api/domains/:domain/description)

## Phased Rollout
- Phase 1: Shared validator + CLI + samples + tests
- Phase 2: Crawler integration (validate pre-persist); behind feature flag VALIDATE_DESCRIPTIONS
- Phase 3: Ranking engine integration; add post-validate
- Phase 4: Web API; cache + metrics; docs update

## Risks & Mitigations
- Schema drift: add version field later; pin with $id; add schema tests
- Perf: Ajv compile once; reuse; skip runtime validation in prod hot paths if needed
- Data gaps: allow partial objects initially by relaxing required fields behind flag

## Acceptance Criteria
- Validator returns non-ok for malformed docs and ok for provided samples
- Crawler refuses to persist invalid descriptions and schedules retry
- Ranking engine only publishes validated scores
- Web endpoint returns validated JSON in dev/test
- CI job fails if any sample is invalid
