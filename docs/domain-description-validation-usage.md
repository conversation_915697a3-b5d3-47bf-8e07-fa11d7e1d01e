# DomainDescription validation usage

Env flag
- VALIDATE_DESCRIPTIONS=1 enables runtime JSON Schema validation gates

Commands
- Validate samples: pnpm validate:descriptions
- Run crawler with validation: VALIDATE_DESCRIPTIONS=1 pnpm --filter @domainr/crawler dev
- Run ranking-engine with validation: VALIDATE_DESCRIPTIONS=1 pnpm --filter @domainr/ranking-engine dev

Notes
- <PERSON>raw<PERSON> validates a built DomainDescription prior to persisting
- Ranking engine validates fetched input and post-score output
- Failures raise warnings and throw in critical paths
