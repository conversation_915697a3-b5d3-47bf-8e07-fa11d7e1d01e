# Top Domain Ranking System (Tranco-like)

Comprehensive design, algorithm, and implementation blueprint for building a robust, reproducible, manipulation-resistant domain popularity ranking, integrated end-to-end with our existing crawler, scheduler, ranking engine, and web application.

Version: 0.2 (living document)
Owners: ranking-engine, scheduler, crawler teams
Status: Proposed

---

Table of Contents

1. Problem Statement & Goals
2. Product Requirements
3. Non-Goals
4. Key Concepts & Definitions
5. System Overview & Architecture
6. Data Sources (Survey, Licensing, Access)
7. Canonicalization & Normalization
8. Filtering & Safety Policies
9. Scoring Algorithms (Transforms, Aggregation, Alternatives)
10. Temporal Smoothing & Stability Controls
11. Manipulation Resistance & Abuse Detection
12. Confidence, Uncertainty, and Explainability
13. Snapshotting, Reproducibility, and Versioning
14. Outputs, Formats, and APIs
15. Data Model & Schemas
16. Pipeline Orchestration & Scheduling Strategy
17. Storage, Indexing, and Retention
18. Integration with Our System (Scheduler, Crawler, Web App)
19. Evaluation & Validation Framework
20. Monitoring, Alerting, and SLOs
21. Performance, Scalability, and Cost Model
22. Security, Compliance, and Ethics
23. Failure Modes & Recovery Playbooks
24. Implementation Plan & Milestones
25. Detailed Module Specs (Interfaces, Pseudocode)
26. Testing Strategy (Unit, Integration, Golden, Property)
27. Local Dev, CI, and Deployment
28. Roadmap & Open Questions
29. Appendices (Math notes, Examples, Tuning guides)

---

1. Problem Statement & Goals
   We need a high-quality popularity ranking of eTLD+1 domains to prioritize crawling, surface top domains in the app, and enable longitudinal analyses. The ranking must be reproducible, robust to manipulation, transparent, and cost-effective to compute on a daily cadence.

Goals

- Accurate: correlate well with trusted benchmarks (Tranco/Umbrella/CrUX)
- Stable: limited daily churn without lagging real changes
- Robust: resistant to single-source bias and gaming
- Transparent: publish metadata, sources used, confidence
- Scalable: support top-1M to top-100M with daily/weekly recomputes
- Reproducible: snapshot inputs, parameters, and outputs deterministically

2. Product Requirements

- Generate daily snapshot for top 1M (stretch: 10M/100M with stratified storage)
- Provide search/browse API and downloadable CSV/JSON artifacts
- Provide movers report (added/removed/up/down by buckets)
- Provide per-domain details: score, present sources, component utilities, confidence, last-change
- Regional lists (opt-in): per country/continent using geolocated sources or weighting

3. Non-Goals

- Predict absolute traffic; we rank relative popularity
- Comprehensive site classification; only minimal filtering
- Full OSINT threat determination; only basic abuse filters

4. Key Concepts & Definitions

- Domain: eTLD+1 using PSL (Public Suffix List)
- Source: external dataset providing rank or magnitude for domains or origins
- Utility: normalized score per source in [0,1]
- Score: aggregate popularity metric per domain
- Snapshot: time-indexed immutable set of ranked domains with metadata

5. System Overview & Architecture

- Ingestion: pull multiple sources, verify, store raw artifacts
- Processing: normalize, canonicalize, filter
- Scoring: per-source transform -> utilities -> aggregation -> smoothing
- Publication: outputs, diffs, metrics, metadata
- Serving: API for top lists and per-domain introspection
- Integration: scheduler consumes ranks to set crawl priority; web app displays lists

Logical Components

- SourceConnectors, Normalizer, FilterEngine, ScoringEngine, TemporalSmoother, ConfidenceEngine, Publisher, SnapshotStore, API Service

6. Data Sources (Survey, Licensing, Access)
   Candidate sources (pick 2–4 for MVP; expand later):

- DNS Resolver Popularity: Cisco Umbrella Top 1M (free, non-commercial clauses); Cloudflare Radar aggregates; Quad9 (policy-based); ISP anonymized (partnership)
- Web Usage: Chrome UX Report (CrUX) Origins (monthly, public BigQuery; map to eTLD+1); Similarweb (licensed)
- Crawl Frequency: Common Crawl host frequency (public S3); Internet Archive capture counts
- Link Graph: Majestic/Ahrefs referring domains to root (licensed)
- Search Presence: SERP host coverage over broad queries (must respect ToS; likely not for MVP)
- Registry/Zone Dynamics: CZDS for .com/.net/.name; registrar counts (feed freshness signals, not direct popularity)

Source Evaluation Criteria

- Coverage breadth and bias (region, device)
- Freshness and cadence
- Stability and noise characteristics
- Legal/licensing constraints
- Cost and access complexity

7. Canonicalization & Normalization

- Normalize host/origin -> eTLD+1 via PSL; maintain PSL version with checksum
- Lowercase, trim trailing dot, normalize unicode to punycode (UTS 46)
- Collapse <www>. to root; choose representative by highest utility if conflicting
- Remove IPs, localhost, special-use TLDs
- Deduplicate across sources; keep provenance for explainability

8. Filtering & Safety Policies

- Parked/placeholder detection via heuristics (keywords: parked, for-sale, sedo, holding), known parking ASNs, HTTP title patterns; soft flag not hard exclude in baseline
- Malware/abuse: maintain denylist from public feeds; exclude from public top lists; keep internal scores for analytics
- Adult category optional filter via curated lists and keyword heuristics; produce filtered and unfiltered views
- TLD policies: exclude invalid or test TLDs
- Always retain an unfiltered baseline snapshot for reproducibility (internal use)

9. Scoring Algorithms (Transforms, Aggregation, Alternatives)
   Per-Source Transforms

- Ranks -> utility: u = 1/log(1+r) or u = 1/(1+r)^β with β∈[0.5,1.0]; choose to compress tail
- Magnitudes -> utility: u = robust_scale(log1p(m)) using per-source median/IQR
- Winsorization: clamp utilities at [p1, p99] per source
- Missing data: u = 0 and mark missing=true to feed confidence model

Aggregation (default)

- Weighted sum with caps: S_raw = Σ w_i \* u_i; enforce Σ w_i = 1; cap per-source contribution ≤ cap_i
- Weight selection: equal weights initially; later data-driven via maximizing Kendall tau vs benchmark on validation months

Alternatives (implement behind flags)

- Borda/Kemeny across partial rankings
- Robust mean of z-scores with trimming
- Reciprocal Rank Fusion (RRF): S = Σ 1/(k + r_i)
- Learning-to-Rank using pseudo-labels (teacher-student with Tranco) for future iteration

10. Temporal Smoothing & Stability Controls

- EMA: S*t = α S_raw_t + (1-α) S*{t-1}; α default 0.6, tuned by minimizing daily churn subject to correlation floor
- Jump Guard: limit ΔS_t per-day to γ-percentile (e.g., 99th) of global ΔS_t distribution unless corroborated by ≥2 sources increasing
- Freshness Boost: slight boost for domains with recent corroborated increases to avoid lag

11. Manipulation Resistance & Abuse Detection

- Source independence: never allow single-source dominance; cap_i ≤ 0.5
- Cross-source corroboration: require ≥2 sources for large promotions into top-N
- ASN/hosting cluster anomaly: detect synchronized multi-domain jumps from same ASN/prefix
- Pattern rules: disposable TLD bursts, lookalike/typosquats (Levenshtein to top brands)
- Manual review queue: suspicious movers with low confidence and high ΔS
- Blacklist/graylist with audit logs and expiry

12. Confidence, Uncertainty, and Explainability
    Confidence C ∈ [0,1] from:

- Coverage: fraction of sources present
- Agreement: Kendall’s W or average pairwise Kendall tau among present ranks
- Stability: 1 - normalized variance of S over last N days
- Recency: time since last corroborated update
  Explainability: include per-source utilities, normalized contributions, and reasons for filters/flags

13. Snapshotting, Reproducibility, and Versioning

- Snapshot ID: YYYY-MM-DD.sha256(inputs + params + code_version)
- Store: raw inputs checksums, per-source params, weights, α, PSL version, filters version
- Tag snapshots with semantic version of algorithm; bump minor for param change, major for algorithm change
- Ability to recompute past snapshots from pinned inputs

14. Outputs, Formats, and APIs
    Artifacts

- top-N.jsonl: {rank, domain, score, confidence, sources, snapshot_id}
- metadata.json: params, weights, caps, α, PSL checksum, inputs list
- movers.json: {added, removed, up: [{domain, Δrank}], down: [...]}
- per-domain explain JSON on demand via API
  APIs
- GET /popularity/snapshots/latest?limit=N
- GET /popularity/snapshots/:id
- GET /popularity/domain/:name
- GET /popularity/movers?window=1d
  Pagination with rank ranges; ETag = snapshot_id

15. Data Model & Schemas
    Types (TypeScript)

- SourceSample { sourceId: string; fetchedAt: string; checksum: string; rows: Array<{domain: string; rank?: number; magnitude?: number}> }
- DomainUtilities { domain: string; bySource: Record<string, number>; missing: Record<string, boolean> }
- DomainScore { domain: string; scoreRaw: number; scoreEma: number; confidence: number }
- SnapshotMeta { id: string; date: string; params: {...}; inputs: Array<{sourceId: string; checksum: string}> }
- Movers { added: string[]; removed: string[]; up: Array<{domain: string; delta: number}>; down: Array<{domain: string; delta: number}> }

16. Pipeline Orchestration & Scheduling Strategy
    Cadence

- Daily job at 00:00 UTC; source fetch windows respect upstream cadences (CrUX monthly → treat as static until new month)
  Parallelism
- Shard compute by TLD hash buckets (e.g., 256 buckets). Map-reduce style: per-bucket aggregation then global rank
  Idempotency
- Content-addressable storage by checksum; retries safe
  Backfills
- On schema/param change or input arrival, trigger recompute with backpressure controls

17. Storage, Indexing, and Retention

- Raw inputs: S3-compatible, versioned, 90-day retention (longer for compliance if permitted)
- Snapshots: keep daily for 1 year; monthly for 5 years
- Serving index: ScyllaDB table current_top with clustering on rank; Manticore index for domain lookup/autocomplete
- Redis cache: hot top-100k for UI and scheduler

18. Integration with Our System
    Already present

- Scheduler (services/scheduler) can consume priority queues
- Crawler (services/crawler) selective data collection modules
- Ranking engine (services/ranking-engine) and shared utils
- Web app surfaces Top Domains
  Additions
- New service: services/domain-popularity with CLI and API
- Scheduler: seed queue by deciles of popularity; recrawl cadence tiers (e.g., top-10k daily, next 100k weekly, tail monthly)
- Web app: new endpoints/views for popularity explainers and movers

19. Evaluation & Validation Framework
    Metrics

- Correlation: Kendall tau/Spearman with Tranco/Umbrella
- Stability: daily churn rate in top-10k/100k/1M
- Coverage: domains with ≥2 sources
- Precision of movers: manual audit pass rates on sampled movers
  Datasets
- Hold-out historical months for offline evaluation
  Reports
- Daily evaluation dashboard; drift detectors on correlation and churn

20. Monitoring, Alerting, and SLOs

- SLO: Daily snapshot by 01:00 UTC, p99 API latency < 200ms for top-1k window
- Alerts: missing source, checksum mismatch, low coverage, high churn spike, API error rate
- Tracing: per-source fetch spans; aggregation time; snapshot publish time

21. Performance, Scalability, and Cost Model
    Estimates

- Top-100M rows: ~100M × ~40 bytes utility map compressed ~4–6 GB raw; use streaming & on-disk sort
- Compute: map stage O(Nsources + Nrows); reduce stage O(Ndomains log N) for ranking; external merge sort
  Optimizations
- Sparse representation for bySource maps
- Use streaming JSONL/Parquet; avoid materializing full cross-product
- Parallel shard processing and external sort by score

22. Security, Compliance, and Ethics

- Respect upstream licenses and ToS; store attributions in metadata
- PII: none processed; ensure no resolver logs with PII are ingested without agreements
- Opt-out: maintain and honor removals from public lists
- Transparency page: algorithm summary and contact for challenges

23. Failure Modes & Recovery Playbooks

- Source outage: proceed with subset; lower confidence; alert
- Bad data spike: quarantine source version; revert to previous snapshot; hotfix weights=renormalized
- Compute failure: retry per shard; resume from last successful stage
- Snapshot publish failure: atomically stage then swap pointers

24. Implementation Plan & Milestones
    M1 (MVP, 2–3 weeks)

- Implement connectors: CrUX (BigQuery export), Common Crawl host freq, Umbrella (if permissible) or proxy synthetic
- Normalization + PSL module
- ScoringEngine with weighted sum + EMA
- Publisher with JSONL and metadata
- CLI job and scheduler integration (priority tiers)
  M2 (Hardening, 2–3 weeks)
- Winsorization, anomaly flags, confidence computation
- Movers report and API endpoints
- Evaluation dashboard and regression checks
  M3 (Scale & API, 3–4 weeks)
- Sharded processing to 100M
- Public/partner API with ETags and snapshot browsing
- Regional variants and filters

25. Detailed Module Specs (Interfaces, Pseudocode)
    Interfaces

- interface SourceConnector { id: string; kind: 'rank'|'magnitude'; fetch: (since?: string) => AsyncIterable<{domain: string; rank?: number; magnitude?: number}> }
- interface Normalizer { toETLD1(host: string): string; normalize(domain: string): string }
- interface FilterEngine { isAllowed(domain: string): boolean; flags(domain: string): string[] }
- interface ScoringEngine { toUtility(input: {rank?: number; magnitude?: number}, sourceId: string): number; aggregate(utils: Record<string, number>): number }
- interface TemporalSmoother { smooth(domain: string, raw: number): number }
- interface ConfidenceEngine { compute(domain: string, utils: Record<string, number>, history: number[]): number }
- interface Publisher { writeSnapshot(...): Promise<void> }

Pseudocode

```
runSnapshot(date):
  inputs = fetchAllSources(date)
  rows = streamMergeByDomain(inputs)
  for domain in rows:
    e = normalizer.toETLD1(domain)
    if !filter.isAllowed(e): continue // keep internal record if needed
    utils = {}
    for (src in sources): utils[src] = scoring.toUtility(rows[src], src)
    utils = winsorize(utils)
    raw = scoring.aggregate(utils)
    score = smoother.smooth(e, raw)
    conf = confidence.compute(e, utils, history[e])
    emit {domain: e, score, conf, utils}
  rank = externalSortByScore(desc)
  publish(rank, metadata)
```

26. Testing Strategy (Unit, Integration, Golden, Property)

- Unit: PSL mapping, punycode normalization, utility transforms, winsorization, caps, EMA
- Property: monotonicity (higher utility -> higher score), stability (EMA bounds), idempotency (same inputs -> same snapshot)
- Golden: fixed mini inputs -> expected snapshot (IDs and ranks)
- Integration: end-to-end pipeline with synthetic noisy sources; API correctness; diff generation
- Regression: daily Kendall tau vs. baseline within thresholds; churn bounds

27. Local Dev, CI, and Deployment

- Local: seed with synthetic sources; run CLI to produce small top-10k
- CI: vitest for units; golden tests; lint/typecheck
- Deployment: containerized service with cron; S3 creds for storage; integrate with existing k8s if available

28. Roadmap & Open Questions

- Source licensing: confirm Umbrella usage terms; evaluate Cloudflare Radar APIs
- Teacher-student LTR with Tranco labels
- Regionalization: reliable per-country signals
- Better abuse features: brand protection and typosquat clustering
- Public transparency report and governance

29. Appendices
    A) Math Notes

- Utility from rank: u=1/log(1+r) yields diminishing returns; tune via β for tail compression
- EMA: α chosen by minimizing f = λ·(1 - corr) + (1-λ)·churn; λ≈0.6
- Confidence: Kendall’s W in [0,1]; combine via C = a·coverage + b·agreement + c·stability

B) Example Artifacts
metadata.json
{
"snapshot_id": "2025-08-05:sha256:...",
"date": "2025-08-05",
"params": {"alpha": 0.6, "weights": {"cc": 0.34, "crux": 0.33, "umbrella": 0.33}, "caps": {"cc": 0.5, "crux": 0.5, "umbrella": 0.5}},
"psl": {"version": "2025-07-01", "checksum": "..."},
"inputs": [{"sourceId":"cc","checksum":"..."},{"sourceId":"crux","checksum":"..."}],
"code_version": "domain-popularity@1.0.0"
}

row.json
{"rank":1,"domain":"google.com","score":0.998,"confidence":0.98,"sources":["cc","crux","umbrella"]}

C) MVP Connectors Plan

- CrUX export via BigQuery to CSV/Parquet; origin -> eTLD+1
- Common Crawl: host-level frequency from URL index; monthly rollup
- Optional Umbrella: if terms allow; otherwise simulate via sample frequencies

D) Integration Contract to Scheduler

- Redis key: popularity:top:latest -> sorted set domain->score
- Scheduler reads ranks to assign crawl tiers and enqueue seeds

E) What We Already Do (Current State)

- Scheduler, crawler, ranking engine, shared utils, monitoring foundations
- Manticore search sync plan exists and will ingest popularity as a field
- Web app Top Domains page ready to display ranked lists

F) Next Steps

- Implement services/domain-popularity with connectors, scoring, and publisher
- Wire Redis/Manticore sync and scheduler prioritization
- Stand-up daily job and dashboards
