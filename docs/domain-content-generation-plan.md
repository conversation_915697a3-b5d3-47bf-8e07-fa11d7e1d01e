# Domain Content Generation — Unified Plan (v1)

Status: Draft
Owner: TBD
Scope: Generate and maintain user/SEO‑facing domain content (description, category, tags) and foundational fields for broader domain profiles. Supports two modes: preGenerated (no live fetch) and live analyzed (crawler/scheduler). Reflects updated schema decisions.

## Goals

- High‑quality, reader‑friendly and SEO‑ready description (80–160 words)
- Clear taxonomy: Primary and Secondary category
- At least 5 tags (up to 12), normalized and deduped
- Deterministic baseline without external calls; enhanced results from live crawl
- Validate against docs/domain-description.schema.json; persist and cache

## Schema Updates (applied)

- Registration: { firstRegisteredAt, lastUpdatedAt, expiresAt } (date‑time)
- Remove ageDays and launchedYear
- ISO standards: country ISO‑3166‑1 alpha‑2, language ISO‑639‑1; keep future BCP‑47 extension in roadmap
- IDN/UTF‑8: metadata.idn { isIdn, unicode, ascii }
- Socials plural: reputation.social platform arrays (twitter[], facebook[], linkedin[], instagram[])
- Category: secondary now single string (Primary > Secondary)
- Flags: { preGenerated }

## Content Surfaces (initial)

- overview.summary: user/SEO description (320 words)
- metadata.category: { primary, secondary }
- metadata.tags: 5–12
- seo.title/metaDescription when known
- reputation.social arrays when known

## Two Phases

Phase 1 — First Content Creation (Domain Seeder)

- Agent: generative/heuristic agent runs without live fetch (preGenerated=true)
- Required outputs: overview.summary (at least 320 words), metadata.category {primary, secondary}, metadata.tags (>=5)
- Inputs: domain string, optional prior knowledge (language, country, known themes)
- Steps: tokenize domain -> infer intent via TLD/name heuristics -> classify primary/secondary -> generate description -> extract tags -> validate -> store/index
- Guarantees: no fabricated claims; targeted but generic; include brand/domain; weave 2–4 relevant phrases

Phase 2 — Completion & Updates (Scheduler)

- Agent: enhancement pipeline after crawl signals are ready (preGenerated=false)
- Purpose: fill missing fields and improve Phase 1 outputs
- Inputs: fetched homepage HTML, title/meta/og, main content, headings; robots/sitemap; optional DNS/WHOIS/SSL; language; discovered social links
- Steps: fetch -> extract -> analyze (keywords/NER/offering/audience/geo cues) -> re‑classify -> retag (TF‑IDF + rules) -> regenerate/refine description -> validate -> store/index
- Update policy: only overwrite Phase 1 fields when confidence improves; preserve history/versioning
- Flags: set flags.preGenerated=false after successful Phase 2

## SEO/Readability Rules

- Length: at least 320 words; first paragraph style; active voice
- Include: brand/domain, offering, audience, benefits, geo if relevant
- Keywords: weave 2–4 key phrases naturally; avoid over‑optimization
- Clarity: sentence length 12–20 words; one concise feature list allowed
- Safety: no unverifiable claims in preGenerated mode; respect detected language

## Parameters Needed

Common

- domain (normalized), IDN info
- language (ISO‑639‑1) and country (ISO‑3166‑1) if known or inferred

Live

- title, metaDescription, og:title/description
- main content excerpt, headings, key features
- discovered social links, sitemap presence, robots policy

## Pipeline Components

- shared/services/CategoryTagger: classifyCategory(), extractTags(), normalizeTags()
- shared/services/DomainSummaryService: generateBaseline(), generateFromContent()
- Validator: shared/utils/DomainDescriptionValidator + schema
- CLI: services/crawler/src/commands/seed-domain.ts (preGenerated) and describe.ts (live)
- Scheduler: services/scheduler job to re‑generate when signals are ready

## Data Flow

- Seeder: domain -> heuristics -> categories -> tags -> summary -> validate -> store/index (preGenerated=true)
- Scheduler: domain -> fetch/extract -> analyze -> categories -> tags -> summary -> validate -> store/index (preGenerated=false)

## Operational Policies

- Fetch: robots‑aware; 5–8s timeout; 1MB HTML cap; retry/backoff
- Logging/metrics: shared Logger and monitoring
- Cache: reuse fetch/extract results

## Future Draft (kept, not required now)

- Rich presentation fields: features, pros/cons, audience, use cases, pricing, trust signals, tech stack, accessibility and content quality scores, screenshots, JSON‑LD/OG, social links expansion, compliance badges, popularity metrics, related domains, FAQs, reviews, changelog/news
- Ranking integration signals: performance/SEO/security scores

## Implementation Steps

1. Finalize schema changes and update DomainDescriptionValidator
2. Implement CategoryTagger and DomainSummaryService
3. Add seed-domain and describe CLIs; expose pnpm scripts
4. Wire scheduler job (one‑time post‑crawl regeneration)
5. Tests: unit (tagger, summary), integration (both modes), schema validation
