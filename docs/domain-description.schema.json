{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://domainr.local/schemas/domain-description.schema.json", "title": "DomainDescription", "type": "object", "additionalProperties": false, "required": ["metadata", "ranking", "crawl"], "properties": {"metadata": {"type": "object", "additionalProperties": false, "required": ["domain", "tld", "status", "category", "tags"], "properties": {"domain": {"type": "string", "pattern": "^[a-z0-9-]+(\\.[a-z0-9-]+)+$"}, "tld": {"type": "string", "minLength": 2}, "status": {"type": "string", "enum": ["active", "parked", "redirect", "inactive"]}, "country": {"type": "string", "pattern": "^[A-Z]{2}$", "description": "ISO 3166-1 alpha-2 country code"}, "language": {"type": "string", "pattern": "^[a-z]{2}$", "description": "ISO 639-1 language code"}, "registration": {"type": "object", "additionalProperties": false, "properties": {"firstRegisteredAt": {"type": "string", "format": "date-time"}, "lastUpdatedAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time"}}}, "idn": {"type": "object", "additionalProperties": false, "required": ["isIdn"], "properties": {"isIdn": {"type": "boolean"}, "unicode": {"type": "string"}, "ascii": {"type": "string"}}}, "owner": {"type": "object", "additionalProperties": false, "properties": {"organization": {"type": "string"}, "registrar": {"type": "string"}}}, "category": {"type": "object", "additionalProperties": false, "required": ["primary"], "properties": {"primary": {"type": "string"}, "secondary": {"type": "string"}}}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "minItems": 5, "maxItems": 12}, "preGenerated": {"type": "boolean", "description": "Indicates if content was generated without live analysis"}}}, "overview": {"type": "object", "additionalProperties": false, "properties": {"summary": {"type": "string", "minLength": 320, "description": "Domain description with minimum 320 words for SEO optimization"}, "keyFeatures": {"type": "array", "items": {"type": "string", "maxLength": 120}, "maxItems": 5}, "cta": {"type": "object", "additionalProperties": false, "properties": {"label": {"type": "string", "maxLength": 40}, "url": {"type": "string", "format": "uri"}}}}}, "technical": {"type": "object", "additionalProperties": false, "properties": {"technologies": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "performance": {"type": "object", "additionalProperties": false, "properties": {"loadTimeMs": {"type": "number", "minimum": 0}, "coreWebVitals": {"type": "object", "additionalProperties": false, "properties": {"fcp": {"type": "number", "minimum": 0}, "lcp": {"type": "number", "minimum": 0}, "cls": {"type": "number", "minimum": 0}, "fid": {"type": "number", "minimum": 0}, "speedIndex": {"type": "number", "minimum": 0}}}}}, "security": {"type": "object", "additionalProperties": false, "properties": {"sslGrade": {"type": "string"}, "securityHeaders": {"type": "object", "additionalProperties": false, "properties": {"hsts": {"type": "boolean"}, "csp": {"type": "boolean"}, "xFrameOptions": {"type": "boolean"}}}, "vulnerabilities": {"type": "array", "items": {"type": "object", "additionalProperties": false, "required": ["id", "severity"], "properties": {"id": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "summary": {"type": "string"}}}}, "certificate": {"type": "object", "additionalProperties": false, "properties": {"issuer": {"type": "string"}, "expiration": {"type": "string", "format": "date-time"}}}}}, "mobileFriendlyScore": {"type": "number", "minimum": 0, "maximum": 1}, "accessibilityScore": {"type": "number", "minimum": 0, "maximum": 1}, "dns": {"type": "object", "additionalProperties": false, "properties": {"a": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "aaaa": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "mx": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "cname": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "txt": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "ns": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "hosting": {"type": "object", "additionalProperties": false, "properties": {"provider": {"type": "string"}, "region": {"type": "string"}}}}}, "seo": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "maxLength": 70}, "metaDescription": {"type": "string", "maxLength": 160}, "structuredData": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sitemap": {"type": "object", "additionalProperties": false, "properties": {"present": {"type": "boolean"}, "url": {"type": "string", "format": "uri"}}}, "robots": {"type": "object", "additionalProperties": false, "properties": {"present": {"type": "boolean"}, "policy": {"type": "string", "enum": ["allow", "disallow", "mixed"]}}}, "languages": {"type": "array", "items": {"type": "string", "pattern": "^[a-z]{2}$"}, "uniqueItems": true}}}, "contentProfile": {"type": "object", "additionalProperties": false, "properties": {"topics": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "formats": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "updateFrequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "sporadic"]}, "contentLength": {"type": "object", "additionalProperties": false, "properties": {"avgWords": {"type": "integer", "minimum": 0}}}, "media": {"type": "object", "additionalProperties": false, "properties": {"images": {"type": "integer", "minimum": 0}, "videos": {"type": "integer", "minimum": 0}}}}}, "reputation": {"type": "object", "additionalProperties": false, "properties": {"backlinks": {"type": "object", "additionalProperties": false, "properties": {"estimate": {"type": "integer", "minimum": 0}, "qualityScore": {"type": "number", "minimum": 0, "maximum": 1}}}, "mentions": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"source": {"type": "string"}, "url": {"type": "string", "format": "uri"}}}}, "social": {"type": "object", "additionalProperties": false, "properties": {"twitter": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "facebook": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "linkedin": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "instagram": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}, "reviews": {"type": "object", "additionalProperties": false, "properties": {"averageRating": {"type": "number", "minimum": 0, "maximum": 5}, "sources": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "sentimentScore": {"type": "number", "minimum": 0, "maximum": 1}}}}}, "ranking": {"type": "object", "additionalProperties": false, "properties": {"globalRank": {"type": "integer", "minimum": 1}, "categoryRank": {"type": "integer", "minimum": 1}, "scores": {"type": "object", "additionalProperties": false, "properties": {"performance": {"type": "number", "minimum": 0, "maximum": 1}, "security": {"type": "number", "minimum": 0, "maximum": 1}, "seo": {"type": "number", "minimum": 0, "maximum": 1}, "technical": {"type": "number", "minimum": 0, "maximum": 1}, "backlink": {"type": "number", "minimum": 0, "maximum": 1}, "overall": {"type": "number", "minimum": 0, "maximum": 1}}}, "trend": {"type": "string", "enum": ["improving", "stable", "declining"]}, "trafficEstimateMonthly": {"type": "integer", "minimum": 0}}}, "compliance": {"type": "object", "additionalProperties": false, "properties": {"privacyPolicyUrl": {"type": "string", "format": "uri"}, "termsUrl": {"type": "string", "format": "uri"}, "cookiesPresent": {"type": "boolean"}, "accessibilityNotes": {"type": "string"}}}, "crawl": {"type": "object", "additionalProperties": false, "required": ["lastCrawled", "crawlType"], "properties": {"lastCrawled": {"type": "string", "format": "date-time"}, "crawlType": {"type": "string", "enum": ["quick", "full", "security", "performance", "content"]}, "errors": {"type": "integer", "minimum": 0}, "screenshotUrls": {"type": "array", "items": {"type": "string", "format": "uri"}, "uniqueItems": true}, "subdomains": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}}}}