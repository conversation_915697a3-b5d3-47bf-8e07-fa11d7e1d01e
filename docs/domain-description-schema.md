# Domain Description Schema (v0.2 Draft)

This document defines a structured JSON schema for domain descriptions used by the Domain Ranking System. It’s designed to map cleanly to ScyllaDB, MariaDB, and Manticore attributes while remaining human-readable for toplists and detail pages.

## Top-level Object: DomainDescription

```json
{
  "metadata": { ... },
  "overview": { ... },
  "technical": { ... },
  "seo": { ... },
  "contentProfile": { ... },
  "reputation": { ... },
  "ranking": { ... },
  "compliance": { ... },
  "crawl": { ... },
  "generation": { ... }
}
```

### 1. metadata
Authoritative identity and classification. Aligns with Scylla domain_analysis fields and Manticore attributes.

```json
{
  "domain": "string",
  "tld": "string",
  "isIDN": true,
  "unicodeDomain": "string",
  "punycodeDomain": "string",
  "status": "active|parked|redirect|inactive",
  "firstRegisteredAt": "ISO8601",
  "lastUpdatedAt": "ISO8601",
  "expiresAt": "ISO8601",
  "country": "ISO3166-1 alpha-2",
  "languages": ["ISO639-1"],
  "owner": {
    "organization": "string",
    "registrar": "string",
    "registrantCountry": "ISO3166-1 alpha-2"
  },
  "category": {
    "primary": "string",
    "secondary": "string"
  },
  "tags": ["string"]
}
```

### 2. overview
Human summary for UI and search snippets.

```json
{
  "summary": "string",
  "keyFeatures": ["string"],
  "cta": {
    "label": "string",
    "url": "https://..."
  }
}
```

### 3. technical
Maps to performance/security/technical metrics in Scylla and Manticore attributes.

```json
{
  "technologies": ["string"],
  "performance": {
    "loadTimeMs": 0,
    "coreWebVitals": {
      "fcp": 0,
      "lcp": 0,
      "cls": 0,
      "fid": 0,
      "speedIndex": 0
    }
  },
  "security": {
    "sslGrade": "A+|A|B|C|...",
    "securityHeaders": {
      "hsts": true,
      "csp": true,
      "xFrameOptions": true
    },
    "vulnerabilities": [
      { "id": "string", "severity": "low|medium|high|critical", "summary": "string" }
    ],
    "certificate": {
      "issuer": "string",
      "expiration": "ISO8601"
    }
  },
  "mobileFriendlyScore": 0,
  "accessibilityScore": 0,
  "dns": {
    "a": ["ipv4"],
    "aaaa": ["ipv6"],
    "mx": ["string"],
    "cname": ["string"],
    "txt": ["string"],
    "ns": ["string"]
  },
  "hosting": {
    "provider": "string",
    "region": "string"
  }
}
```

### 4. seo
SEO and indexability signals. Mirrors Manticore index text fields and attributes.

```json
{
  "title": "string",
  "metaDescription": "string",
  "structuredData": ["Organization|Product|Article|BreadcrumbList|..."],
  "sitemap": { "present": true, "url": "https://.../sitemap.xml" },
  "robots": {
    "present": true,
    "policy": "allow|disallow|mixed"
  },
  "languages": ["ISO639-1"],
  "seoSummary": "string"
}
```

### 5. contentProfile
Editorial footprint and cadence.

```json
{
  "topics": ["string"],
  "formats": ["blog|docs|video|podcast|product|landing"],
  "updateFrequency": "daily|weekly|monthly|quarterly|sporadic",
  "contentLength": {
    "avgWords": 0
  },
  "media": {
    "images": 0,
    "videos": 0
  }
}
```

### 6. reputation
Backlinks, social presence, and external reviews. Maps to Maria backlinks, Scylla reviews, Manticore reviews index.

```json
{
  "backlinks": {
    "estimate": 0,
    "qualityScore": 0
  },
  "mentions": [
    { "source": "string", "url": "https://..." }
  ],
  "socials": [
    { "platform": "twitter|facebook|linkedin|instagram|youtube|tiktok|github", "handles": ["string"] }
  ],
  "reviews": {
    "averageRating": 0,
    "sources": ["trustpilot|sitejabber|google|manual"],
    "sentimentScore": 0
  }
}
```

### 7. ranking
Holds computed ranks and scores consistent with Ranking Engine weights and Manticore attributes.

```json
{
  "globalRank": 0,
  "categoryRank": 0,
  "scores": {
    "performance": 0,
    "security": 0,
    "seo": 0,
    "technical": 0,
    "backlink": 0,
    "overall": 0
  },
  "trend": "improving|stable|declining",
  "trafficEstimateMonthly": 0
}
```

### 8. compliance
Legal and policy disclosures.

```json
{
  "privacyPolicyUrl": "https://...",
  "termsUrl": "https://...",
  "cookiesPresent": true,
  "accessibilityNotes": "string"
}
```

### 9. crawl
Crawl metadata for provenance and retries.

```json
{
  "lastCrawled": "ISO8601",
  "crawlType": "quick|full|security|performance|content",
  "errors": 0,
  "screenshotUrls": ["https://..."],
  "subdomains": ["string"],
  "preGenerated": true
}
```

## Storage Mapping Notes
- Scylla: domain_analysis maps to metadata, technical, seo, ranking, compliance, crawl, generation; domain_content to contentProfile; domain_reviews to reputation.reviews; domain_ranking_history to ranking trend.
- Maria: domain_categories/domain_category_mapping mirror metadata.category; backlinks table maps to reputation.backlinks; domain_whois to metadata.owner/registrar/firstRegisteredAt/expiresAt.
- Manticore: domains_index holds searchable text (domain, title, seoSummary, description) and attributes (category, languages, isIDN, scores, sslGrade, timestamps).

## Example Instance

```json
{
  "metadata": {
    "domain": "example.com",
    "tld": "com",
    "isIDN": false,
    "unicodeDomain": "example.com",
    "punycodeDomain": "example.com",
    "status": "active",
    "firstRegisteredAt": "2005-01-15T00:00:00Z",
    "lastUpdatedAt": "2024-03-01T00:00:00Z",
    "expiresAt": "2026-01-15T00:00:00Z",
    "country": "US",
    "languages": ["en"],
    "owner": { "organization": "Example Inc.", "registrar": "Namecheap", "registrantCountry": "US" },
    "category": { "primary": "technology", "secondary": "developer-tools" },
    "tags": ["api", "open-source", "docs"]
  },
  "overview": {
    "summary": "API platform for developers with reliable infrastructure and comprehensive documentation.",
    "keyFeatures": ["Generous free tier", "99.9% uptime", "SDKs for JS/Python/Go"],
    "cta": { "label": "Sign up free", "url": "https://example.com/signup" }
  },
  "technical": {
    "technologies": ["Next.js", "Node.js", "Nginx", "Cloudflare"],
    "performance": { "loadTimeMs": 1200, "coreWebVitals": { "fcp": 0.9, "lcp": 1.2, "cls": 0.02, "fid": 0.03, "speedIndex": 1.5 } },
    "security": { "sslGrade": "A", "securityHeaders": { "hsts": true, "csp": true, "xFrameOptions": true }, "vulnerabilities": [], "certificate": { "issuer": "Let’s Encrypt", "expiration": "2026-01-01T00:00:00Z" } },
    "mobileFriendlyScore": 0.92,
    "accessibilityScore": 0.88,
    "dns": { "a": ["*************"], "aaaa": [], "mx": ["mx1.example.com"], "cname": [], "txt": ["v=spf1 ..."], "ns": ["ns1.example.com"] },
    "hosting": { "provider": "Cloudflare", "region": "global" }
  },
  "seo": {
    "title": "Example — Developer API Platform",
    "metaDescription": "Build faster with Example APIs and SDKs.",
    "structuredData": ["Organization", "Product"],
    "sitemap": { "present": true, "url": "https://example.com/sitemap.xml" },
    "robots": { "present": true, "policy": "allow" },
    "languages": ["en"],
    "seoSummary": "Example is a developer-first API platform offering reliable infrastructure, SDKs, and docs to build and scale faster."
  },
  "contentProfile": {
    "topics": ["APIs", "SDKs", "Guides"],
    "formats": ["docs", "blog"],
    "updateFrequency": "weekly",
    "contentLength": { "avgWords": 1600 },
    "media": { "images": 12, "videos": 1 }
  },
  "reputation": {
    "backlinks": { "estimate": 3200, "qualityScore": 0.68 },
    "mentions": [{ "source": "GitHub", "url": "https://github.com/..." }],
    "social": { "twitter": "@example", "facebook": "example", "linkedin": "example", "instagram": "example" },
    "reviews": { "averageRating": 4.6, "sources": ["trustpilot"], "sentimentScore": 0.71 }
  },
  "ranking": {
    "globalRank": 12345,
    "categoryRank": 120,
    "scores": { "performance": 0.82, "security": 0.95, "seo": 0.78, "technical": 0.8, "backlink": 0.65, "overall": 0.80 },
    "trend": "improving",
    "trafficEstimateMonthly": 450000
  },
  "compliance": {
    "privacyPolicyUrl": "https://example.com/privacy",
    "termsUrl": "https://example.com/terms",
    "cookiesPresent": true,
    "accessibilityNotes": "Keyboard navigation supported"
  },
  "crawl": {
    "lastCrawled": "2025-08-04T12:00:00Z",
    "crawlType": "quick",
    "errors": 0,
    "screenshotUrls": ["https://cdn.example.com/screenshots/home.png"],
    "subdomains": ["status.example.com", "docs.example.com"],
    "preGenerated": true
  },
  "generation": {
    "preGenerated": true,
    "method": "self|live",
    "inputs": ["whois","homepage","sitemap","socials"],
    "version": "gen-1.0.0"
  }
}
```

## Authoring Guidance
- Keep summary under 2 sentences; features under 5 bullets.
- Use ISO dates (ISO8601), ISO3166-1 alpha-2 for countries, ISO639-1 for languages.
- Prefer firstRegisteredAt/lastUpdatedAt/expiresAt over derived ageDays.
- Omit empty arrays/fields when serializing for storage.
- Map directly to existing services: crawler populates technical/seo/crawl; ranking-engine populates ranking; web-app reads all for UI; seeder sets generation.preGenerated initially.
