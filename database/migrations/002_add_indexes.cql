-- Migration: 002_add_indexes.cql
-- Description: Add performance indexes for better query performance
-- Created: 2024-01-02
-- Version: 1.0.1

USE domain_ranking;

-- Add materialized views for better query performance
CREATE MATERIALIZED VIEW IF NOT EXISTS domains_by_category AS
    SELECT domain, category, global_rank, overall_score, last_crawled
    FROM domain_analysis
    WHERE category IS NOT NULL AND domain IS NOT NULL
    PRIMARY KEY (category, global_rank, domain);

CREATE MATERIALIZED VIEW IF NOT EXISTS domains_by_rank AS
    SELECT domain, global_rank, overall_score, category, last_crawled
    FROM domain_analysis
    WHERE global_rank IS NOT NULL AND domain IS NOT NULL
    PRIMARY KEY (global_rank, domain);

CREATE MATERIALIZED VIEW IF NOT EXISTS recent_crawls AS
    SELECT domain, last_crawled, crawl_status, global_rank
    FROM domain_analysis
    WHERE last_crawled IS NOT NULL AND domain IS NOT NULL
    PRIMARY KEY (last_crawled, domain)
    WITH CLUSTERING ORDER BY (last_crawled DESC);

-- Record this migration
INSERT INTO schema_migrations (migration_id, migration_name, applied_at, checksum, success)
VALUES ('002', 'add_indexes', toTimestamp(now()), 'sha256:002_indexes', true);
