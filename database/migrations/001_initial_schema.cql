-- Migration: 001_initial_schema.cql
-- Description: Initial ScyllaDB schema creation
-- Created: 2024-01-01
-- Version: 1.0.0

-- Create keyspace with proper replication strategy
CREATE KEYSPACE IF NOT EXISTS domain_ranking
WITH replication = {
    'class': 'NetworkTopologyStrategy',
    'datacenter1': 3
} AND durable_writes = true;

USE domain_ranking;

-- Migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations (
    migration_id text PRIMARY KEY,
    migration_name text,
    applied_at timestamp,
    checksum text,
    success boolean
);

-- Record this migration
INSERT INTO schema_migrations (migration_id, migration_name, applied_at, checksum, success)
VALUES ('001', 'initial_schema', toTimestamp(now()), 'sha256:001_initial', true);
