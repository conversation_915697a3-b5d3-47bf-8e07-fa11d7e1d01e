-- Migration 003: Add domain discovery provenance tracking table
-- This migration adds the domain_discovery table for provenance tracking

USE domain_ranking;

-- Domain Discovery Provenance Table
-- Stores lightweight metadata for discovery audit trails
CREATE TABLE IF NOT EXISTS domain_discovery (
    domain text PRIMARY KEY,
    first_seen_at timestamp,
    sources list<text>,                     -- List of sources that discovered this domain
    discovery_strategy text,                -- 'differential', 'zone-new', 'long-tail', 'temporal'
    confidence decimal,                     -- Confidence score (0.0 - 1.0)
    discovery_reason text,                  -- Human-readable reason for discovery
    pre_generated_content boolean,          -- Whether content was pre-generated
    metadata text,                          -- JSON string of additional metadata
    last_updated timestamp
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS ON domain_discovery (discovery_strategy);
CREATE INDEX IF NOT EXISTS ON domain_discovery (first_seen_at);
CREATE INDEX IF NOT EXISTS ON domain_discovery (confidence);

-- Create a table for discovery statistics aggregation
CREATE TABLE IF NOT EXISTS discovery_statistics (
    date_bucket text,                       -- 'daily:2025-08-06', 'weekly:2025-W32', 'monthly:2025-08'
    source text,
    strategy text,
    discovered_count counter,
    total_confidence decimal,
    PRIMARY KEY ((date_bucket), source, strategy)
);

-- Create index for statistics queries
CREATE INDEX IF NOT EXISTS ON discovery_statistics (date_bucket);
