-- Manticore Search Index Initialization
-- This file contains the index definitions for Manticore Search

-- Main domain search index
CREATE TABLE domains_index (
    id bigint,
    domain text indexed,
    title text indexed,
    description text indexed,
    content text indexed,
    category string attribute,
    country string attribute,
    language string attribute,
    technologies multi attribute,
    registrar string attribute,
    domain_age_days int attribute,
    global_rank int attribute,
    overall_score float attribute,
    performance_score float attribute,
    security_score float attribute,
    seo_score float attribute,
    technical_score float attribute,
    backlink_score float attribute,
    traffic_estimate bigint attribute,
    ssl_grade string attribute,
    mobile_friendly_score float attribute,
    accessibility_score float attribute,
    last_updated timestamp attribute
) engine='columnar';

-- Domain content search index (for full-text search across all pages)
CREATE TABLE domain_content_index (
    id bigint,
    domain text indexed,
    page_type string attribute,
    title text indexed,
    content text indexed,
    headings text indexed,
    meta_keywords multi attribute,
    language string attribute,
    last_updated timestamp attribute
) engine='columnar';

-- Domain reviews search index
CREATE TABLE domain_reviews_index (
    id bigint,
    domain text indexed,
    review_text text indexed,
    source string attribute,
    rating float attribute,
    sentiment_score float attribute,
    review_date timestamp attribute,
    verified int attribute
) engine='columnar';

-- Show created indexes
SHOW TABLES;
