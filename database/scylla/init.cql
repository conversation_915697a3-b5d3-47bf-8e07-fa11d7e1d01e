-- ScyllaDB Keyspace and Table Initialization
-- This file is executed when ScyllaDB container starts

-- Create keyspace
CREATE KEYSPACE IF NOT EXISTS domain_ranking
WITH replication = {
    'class': 'SimpleStrategy',
    'replication_factor': 1
};

USE domain_ranking;

-- Domain Rankings Table (Global and category-based rankings)
CREATE TABLE IF NOT EXISTS domain_rankings (
    ranking_type text,            -- 'global', 'category:tech', 'country:us', etc.
    rank int,
    domain text,
    overall_score decimal,
    performance_score decimal,
    security_score decimal,
    seo_score decimal,
    technical_score decimal,
    backlink_score decimal,
    traffic_estimate bigint,
    last_updated timestamp,
    PRIMARY KEY (ranking_type, rank, domain)
) WITH CLUSTERING ORDER BY (rank ASC);

-- Comprehensive Domain Analysis Data
CREATE TABLE IF NOT EXISTS domain_analysis (
    domain text PRIMARY KEY,
    global_rank int,
    category text,
    category_rank int,

    -- Performance Metrics
    performance_metrics map<text, decimal>,  -- load_time, fcp, lcp, cls, fid, speed_index

    -- Security Metrics
    security_metrics map<text, text>,        -- ssl_grade, security_headers, vulnerabilities
    ssl_certificate map<text, text>,         -- issuer, expiration, chain_info

    -- SEO Metrics
    seo_metrics map<text, text>,            -- meta_tags, structured_data, sitemap_status

    -- Technical Metrics
    technical_metrics map<text, text>,       -- page_size, resource_count, compression
    technologies set<text>,                  -- cms, frameworks, analytics, cdn
    server_info map<text, text>,            -- ip, location, hosting_provider, cdn_provider

    -- Domain Information
    domain_age_days int,
    registration_date date,
    expiration_date date,
    registrar text,

    -- DNS Records
    dns_records map<text, list<text>>,      -- a, aaaa, mx, cname, txt, ns records

    -- Content Analysis
    content_metrics map<text, decimal>,      -- readability, content_length, media_count
    language_detected text,
    mobile_friendly_score decimal,
    accessibility_score decimal,

    -- Social Presence
    social_links map<text, text>,           -- facebook, twitter, linkedin, instagram

    -- Visual Data
    screenshot_urls list<text>,
    subdomains set<text>,

    -- Crawl Metadata
    last_crawled timestamp,
    crawl_status text,
    error_count int,
    crawl_duration_ms int
);

-- Domain Content Storage (for full-text search indexing)
CREATE TABLE IF NOT EXISTS domain_content (
    domain text,
    page_type text,                         -- 'homepage', 'about', 'contact', 'privacy'
    content_hash text,
    title text,
    description text,
    content_text text,                      -- Full page text content
    meta_keywords set<text>,
    headings list<text>,                    -- h1, h2, h3 content
    last_updated timestamp,
    PRIMARY KEY (domain, page_type)
);

-- Domain Crawl Jobs
CREATE TABLE IF NOT EXISTS domain_crawl_jobs (
    job_id uuid PRIMARY KEY,
    domain text,
    crawl_type text,                        -- 'full', 'quick', 'security', 'performance', 'content'
    priority text,
    status text,
    retry_count int,
    scheduled_at timestamp,
    started_at timestamp,
    completed_at timestamp,
    error_message text,
    user_agent text,
    pages_to_crawl list<text>               -- specific pages to analyze
);

-- Domain Traffic History
CREATE TABLE IF NOT EXISTS domain_traffic_history (
    domain text,
    date date,
    estimated_visits bigint,
    bounce_rate decimal,
    avg_session_duration int,
    page_views_per_session decimal,
    traffic_sources map<text, decimal>,     -- organic, direct, referral, social percentages
    top_keywords list<text>,
    PRIMARY KEY (domain, date)
) WITH CLUSTERING ORDER BY (date DESC);

-- Domain Ranking History (for trend analysis)
CREATE TABLE IF NOT EXISTS domain_ranking_history (
    domain text,
    date date,
    ranking_type text,
    rank int,
    score decimal,
    PRIMARY KEY (domain, ranking_type, date)
) WITH CLUSTERING ORDER BY (date DESC);

-- Robots.txt Analysis Results
CREATE TABLE IF NOT EXISTS robots_analysis (
    domain text PRIMARY KEY,
    robots_txt_url text,
    exists boolean,
    accessible boolean,
    content text,
    rules text,                             -- JSON string of parsed rules
    sitemaps list<text>,
    crawl_delay int,
    is_compliant boolean,
    violations list<text>,
    warnings list<text>,
    last_analyzed timestamp,
    error_message text
);

-- DNS Analysis Results
CREATE TABLE IF NOT EXISTS dns_analysis (
    domain text PRIMARY KEY,
    a_records list<text>,                   -- IPv4 addresses
    aaaa_records list<text>,               -- IPv6 addresses
    mx_records list<text>,                 -- Mail exchange records
    cname_records list<text>,              -- CNAME records
    txt_records list<text>,                -- TXT records
    ns_records list<text>,                 -- Name server records
    soa_record text,                       -- SOA record (JSON)
    primary_ip text,                       -- Primary IPv4 address
    supports_ipv6 boolean,                 -- IPv6 support
    has_email_config boolean,              -- Has MX records
    has_cloudflare boolean,                -- Uses Cloudflare
    has_cdn boolean,                       -- Uses CDN
    dns_provider text,                     -- DNS provider
    analysis_time_ms int,                  -- Analysis duration
    errors list<text>,                     -- DNS resolution errors
    last_analyzed timestamp
);

-- SSL Analysis Results
CREATE TABLE IF NOT EXISTS ssl_analysis (
    domain text PRIMARY KEY,
    has_ssl boolean,
    ssl_grade text,
    certificate_issuer text,
    certificate_subject text,
    certificate_valid_from timestamp,
    certificate_valid_to timestamp,
    days_until_expiry int,
    certificate_key_size int,
    signature_algorithm text,
    protocols list<text>,
    cipher_suites list<text>,
    vulnerabilities list<text>,
    has_hsts boolean,
    hsts_max_age int,
    hsts_include_subdomains boolean,
    last_analyzed timestamp,
    error_message text
);

-- Homepage Analysis Results
CREATE TABLE IF NOT EXISTS homepage_analysis (
    domain text PRIMARY KEY,
    url text,
    accessible boolean,
    status_code int,
    response_time int,
    redirects int,
    final_url text,
    content_type text,
    content_length int,
    meta_title text,
    meta_description text,
    meta_keywords text,
    meta_author text,
    meta_viewport text,
    meta_charset text,
    meta_robots text,
    canonical_url text,
    og_title text,
    og_description text,
    og_image text,
    twitter_card text,
    detected_server text,
    detected_framework text,
    detected_cms text,
    analytics_tools list<text>,
    libraries list<text>,
    load_time int,
    ttfb int,
    has_h1 boolean,
    has_h2 boolean,
    image_count int,
    link_count int,
    word_count int,
    content_language text,
    last_analyzed timestamp,
    error_message text
);

-- Favicon Collection Results
CREATE TABLE IF NOT EXISTS favicon_collection (
    domain text PRIMARY KEY,
    favicon_url text,
    favicon_found boolean,
    favicon_size int,
    favicon_type text,
    source_duckduckgo boolean,
    source_direct boolean,
    source_html boolean,
    fallback_used text,
    last_collected timestamp,
    error_message text
);

-- Domain Info Analysis Results
CREATE TABLE IF NOT EXISTS domain_info (
    domain text PRIMARY KEY,
    is_valid boolean,
    is_tld boolean,
    is_subdomain boolean,
    normalized_domain text,
    registrar text,
    registration_date date,
    expiration_date date,
    last_updated timestamp,
    name_servers list<text>,
    domain_status list<text>,
    registrant_country text,
    registrant_organization text,
    admin_email text,
    tech_email text,
    days_until_expiry int,
    domain_age int,
    last_analyzed timestamp,
    error_message text
);

-- Domain Screenshots Storage (detailed screenshot metadata)
CREATE TABLE IF NOT EXISTS domain_screenshots (
    domain text,
    capture_date timestamp,
    desktop_url text,
    desktop_optimized_url text,
    desktop_width int,
    desktop_height int,
    desktop_format text,
    desktop_size int,
    desktop_error text,
    mobile_url text,
    mobile_optimized_url text,
    mobile_width int,
    mobile_height int,
    mobile_format text,
    mobile_size int,
    mobile_error text,
    capture_time_ms int,
    last_captured timestamp,
    PRIMARY KEY (domain, capture_date)
) WITH CLUSTERING ORDER BY (capture_date DESC);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS ON domain_analysis (category);
CREATE INDEX IF NOT EXISTS ON domain_analysis (global_rank);
CREATE INDEX IF NOT EXISTS ON domain_analysis (last_crawled);
CREATE INDEX IF NOT EXISTS ON domain_crawl_jobs (domain);
CREATE INDEX IF NOT EXISTS ON domain_crawl_jobs (status);
CREATE INDEX IF NOT EXISTS ON domain_crawl_jobs (scheduled_at);
CREATE INDEX IF NOT EXISTS ON robots_analysis (last_analyzed);
CREATE INDEX IF NOT EXISTS ON robots_analysis (is_compliant);
CREATE INDEX IF NOT EXISTS ON dns_analysis (last_analyzed);
CREATE INDEX IF NOT EXISTS ON dns_analysis (has_cloudflare);
CREATE INDEX IF NOT EXISTS ON dns_analysis (has_cdn);
CREATE INDEX IF NOT EXISTS ON dns_analysis (dns_provider);
CREATE INDEX IF NOT EXISTS ON ssl_analysis (last_analyzed);
CREATE INDEX IF NOT EXISTS ON ssl_analysis (ssl_grade);
CREATE INDEX IF NOT EXISTS ON ssl_analysis (has_ssl);
CREATE INDEX IF NOT EXISTS ON homepage_analysis (last_analyzed);
CREATE INDEX IF NOT EXISTS ON homepage_analysis (accessible);
CREATE INDEX IF NOT EXISTS ON favicon_collection (last_collected);
CREATE INDEX IF NOT EXISTS ON favicon_collection (favicon_found);
CREATE INDEX IF NOT EXISTS ON domain_info (last_analyzed);
CREATE INDEX IF NOT EXISTS ON domain_info (registrar);
CREATE INDEX IF NOT EXISTS ON domain_screenshots (capture_date);
CREATE INDEX IF NOT EXISTS ON domain_screenshots (last_updated);

-- Insert sample data for testing
INSERT INTO domain_rankings (ranking_type, rank, domain, overall_score, performance_score, security_score, seo_score, technical_score, backlink_score, traffic_estimate, last_updated)
VALUES ('global', 1, 'example.com', 0.95, 0.90, 0.98, 0.92, 0.88, 0.85, 1000000, toTimestamp(now()));

INSERT INTO domain_analysis (domain, global_rank, category, category_rank, performance_metrics, security_metrics, seo_metrics, technical_metrics, technologies, domain_age_days, registrar, last_crawled, crawl_status)
VALUES ('example.com', 1, 'technology', 1, {'load_time': 1.2, 'fcp': 0.8, 'lcp': 1.5}, {'ssl_grade': 'A+', 'score': '0.98'}, {'title': 'Example Domain', 'score': '0.92'}, {'page_size': '50000', 'score': '0.88'}, {'nginx', 'cloudflare'}, 8000, 'Example Registrar', toTimestamp(now()), 'completed');

DESCRIBE TABLES;
