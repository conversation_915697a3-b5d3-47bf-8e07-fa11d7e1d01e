-- MariaDB Database Initialization
-- This file is executed when MariaDB container starts

USE domain_ranking;

-- Domain Categories and Classifications
CREATE TABLE IF NOT EXISTS domain_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_category_id) REFERENCES domain_categories(id),
    INDEX idx_parent (parent_category_id),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Domain to Category Mapping
CREATE TABLE IF NOT EXISTS domain_category_mapping (
    domain VARCHAR(255),
    category_id INT,
    confidence_score DECIMAL(3,2),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (domain, category_id),
    FOREIGN KEY (category_id) REFERENCES domain_categories(id),
    INDEX idx_domain (domain),
    INDEX idx_category (category_id),
    INDEX idx_confidence (confidence_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Backlink Analysis
CREATE TABLE IF NOT EXISTS backlinks (
    id CHAR(36) PRIMARY KEY,
    source_domain VARCHAR(255),
    target_domain VARCHAR(255),
    link_quality_score DECIMAL(3,2),
    anchor_text VARCHAR(500),
    link_type ENUM('follow', 'nofollow', 'sponsored', 'ugc'),
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_verified TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_target_domain (target_domain),
    INDEX idx_source_domain (source_domain),
    INDEX idx_quality_score (link_quality_score),
    INDEX idx_discovered (discovered_at),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Domain Whois Information
CREATE TABLE IF NOT EXISTS domain_whois (
    domain VARCHAR(255) PRIMARY KEY,
    registrar VARCHAR(255),
    registration_date DATE,
    expiration_date DATE,
    name_servers TEXT,
    registrant_country VARCHAR(2),
    registrant_organization VARCHAR(255),
    privacy_protected BOOLEAN DEFAULT FALSE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_registrar (registrar),
    INDEX idx_registration_date (registration_date),
    INDEX idx_country (registrant_country),
    INDEX idx_expiration (expiration_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Domain Reviews and Ratings
CREATE TABLE IF NOT EXISTS domain_reviews (
    id CHAR(36) PRIMARY KEY,
    domain VARCHAR(255),
    source VARCHAR(100),                    -- 'trustpilot', 'sitejabber', 'google', 'manual'
    rating DECIMAL(2,1),
    review_text TEXT,
    review_date DATE,
    sentiment_score DECIMAL(3,2),
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_domain (domain),
    INDEX idx_source (source),
    INDEX idx_rating (rating),
    INDEX idx_review_date (review_date),
    INDEX idx_verified (verified)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Configuration
CREATE TABLE IF NOT EXISTS system_config (
    config_key VARCHAR(100) PRIMARY KEY,
    config_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default categories
INSERT IGNORE INTO domain_categories (name, description) VALUES
('Technology', 'Technology and software companies'),
('E-commerce', 'Online retail and shopping platforms'),
('News & Media', 'News websites and media organizations'),
('Education', 'Educational institutions and learning platforms'),
('Healthcare', 'Healthcare and medical services'),
('Finance', 'Financial services and banking'),
('Entertainment', 'Entertainment and gaming platforms'),
('Business Services', 'B2B services and consulting'),
('Government', 'Government and public sector websites'),
('Non-profit', 'Non-profit organizations and charities');

-- Insert subcategories
INSERT IGNORE INTO domain_categories (name, description, parent_category_id) VALUES
('Software Development', 'Software development companies', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1)),
('Cloud Services', 'Cloud computing and hosting services', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1)),
('Online Retail', 'E-commerce stores and marketplaces', (SELECT id FROM domain_categories WHERE name = 'E-commerce' LIMIT 1)),
('Digital Marketing', 'Marketing and advertising services', (SELECT id FROM domain_categories WHERE name = 'Business Services' LIMIT 1));

-- Insert sample data
INSERT IGNORE INTO domain_whois (domain, registrar, registration_date, expiration_date, registrant_country) VALUES
('example.com', 'Example Registrar Inc.', '2000-01-01', '2025-01-01', 'US'),
('test.com', 'Test Registrar LLC', '2010-05-15', '2024-05-15', 'US'),
('demo.org', 'Demo Registry', '2015-03-20', '2024-03-20', 'UK');

-- Insert sample category mappings
INSERT IGNORE INTO domain_category_mapping (domain, category_id, confidence_score) VALUES
('example.com', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1), 0.95),
('test.com', (SELECT id FROM domain_categories WHERE name = 'Technology' LIMIT 1), 0.85),
('demo.org', (SELECT id FROM domain_categories WHERE name = 'Education' LIMIT 1), 0.90);

-- Insert system configuration
INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
('crawl_rate_limit', '60', 'Maximum crawl requests per minute'),
('ranking_update_interval', '3600', 'Ranking update interval in seconds'),
('cache_ttl', '1800', 'Default cache TTL in seconds'),
('max_concurrent_crawls', '10', 'Maximum concurrent crawl operations');

-- Show table structure
SHOW TABLES;
DESCRIBE domain_categories;
DESCRIBE backlinks;
DESCRIBE domain_whois;
