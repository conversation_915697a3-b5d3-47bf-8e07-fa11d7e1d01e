# Database Initialization

This directory contains database initialization scripts and utilities for the Domain Ranking System.

## Structure

- `scylla/` - ScyllaDB initialization scripts
- `mariadb/` - MariaDB initialization scripts  
- `manticore/` - Manticore Search initialization scripts
- `migrations/` - Database migration scripts
- `sample-data/` - Sample data for testing and development
- `scripts/` - Utility scripts for database management

## Usage

### Docker Compose
The initialization scripts are automatically executed when containers start for the first time.

### Manual Initialization
```bash
# Initialize ScyllaDB
docker exec -it scylla cqlsh -f /docker-entrypoint-initdb.d/init.cql

# Initialize MariaDB
docker exec -it mariadb mysql -u root -p < /docker-entrypoint-initdb.d/init.sql

# Initialize Manticore
docker exec -it manticore mysql -h127.0.0.1 -P9306 < /docker-entrypoint-initdb.d/init.sql
```

### Migration Scripts
```bash
# Run migrations
node database/scripts/migrate.js

# Rollback migrations
node database/scripts/migrate.js --rollback

# Check migration status
node database/scripts/migrate.js --status
```
