{"alphabet": {"name": "Alphabet", "parent": null, "category_primary": "technology", "tags": ["search", "android", "maps", "email"]}, "google": {"name": "Google", "parent": "alphabet", "category_primary": "technology", "tags": ["search", "android", "maps"]}, "youtube": {"name": "YouTube", "parent": "alphabet", "category_primary": "entertainment", "tags": ["video"]}, "android": {"name": "Android", "parent": "alphabet", "category_primary": "technology", "tags": ["mobile"]}, "gmail": {"name": "Gmail", "parent": "alphabet", "category_primary": "technology", "tags": ["email", "productivity"]}, "meta": {"name": "Meta", "parent": null, "category_primary": "technology", "tags": ["messaging"]}, "facebook": {"name": "Facebook", "parent": "meta", "category_primary": "social", "tags": ["messaging"]}, "instagram": {"name": "Instagram", "parent": "meta", "category_primary": "social", "tags": []}, "whatsapp": {"name": "WhatsApp", "parent": "meta", "category_primary": "communications", "tags": ["messaging", "encryption"]}, "apple": {"name": "Apple", "parent": null, "category_primary": "technology", "tags": []}, "microsoft": {"name": "Microsoft", "parent": null, "category_primary": "technology", "tags": []}, "amazon": {"name": "Amazon", "parent": null, "category_primary": "e-commerce", "tags": []}, "aws": {"name": "AWS", "parent": "amazon", "category_primary": "technology", "tags": ["databases"]}, "netflix": {"name": "Netflix", "parent": null, "category_primary": "entertainment", "tags": ["video"]}, "tiktok": {"name": "TikTok", "parent": "bytedance", "category_primary": "entertainment", "tags": []}, "bytedance": {"name": "ByteDance", "parent": null, "category_primary": "technology", "tags": ["content"]}, "tesla": {"name": "Tesla", "parent": null, "category_primary": "automotive", "tags": []}, "samsung": {"name": "Samsung", "parent": null, "category_primary": "technology", "tags": []}, "intel": {"name": "Intel", "parent": null, "category_primary": "hardware-industry", "tags": []}, "nvidia": {"name": "NVIDIA", "parent": null, "category_primary": "hardware-industry", "tags": []}, "adobe": {"name": "Adobe", "parent": null, "category_primary": "technology", "tags": ["analytics"]}, "oracle": {"name": "Oracle", "parent": null, "category_primary": "technology", "tags": ["databases"]}, "salesforce": {"name": "Salesforce", "parent": null, "category_primary": "technology", "tags": ["crm"]}, "tencent": {"name": "Tencent", "parent": null, "category_primary": "technology", "tags": ["payments"]}, "alibaba": {"name": "Alibaba", "parent": null, "category_primary": "e-commerce", "tags": ["payments"]}, "paypal": {"name": "PayPal", "parent": null, "category_primary": "finance", "tags": ["payments", "checkout"]}, "visa": {"name": "Visa", "parent": null, "category_primary": "finance", "tags": ["payments"]}, "mastercard": {"name": "Mastercard", "parent": null, "category_primary": "finance", "tags": ["payments"]}, "uber": {"name": "Uber", "parent": null, "category_primary": "transport", "tags": []}, "airbnb": {"name": "Airbnb", "parent": null, "category_primary": "travel", "tags": []}, "spotify": {"name": "Spotify", "parent": null, "category_primary": "entertainment", "tags": []}, "coca-cola": {"name": "Coca-Cola", "parent": null, "category_primary": "food-drink", "tags": []}, "pepsi": {"name": "Pepsi", "parent": "pepsico", "category_primary": "food-drink", "tags": []}, "pepsico": {"name": "PepsiCo", "parent": null, "category_primary": "food-drink", "tags": []}, "nike": {"name": "Nike", "parent": null, "category_primary": "fashion", "tags": []}, "adidas": {"name": "Adidas", "parent": null, "category_primary": "fashion", "tags": []}, "puma": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "fashion", "tags": []}, "ibm": {"name": "IBM", "parent": null, "category_primary": "technology", "tags": []}, "dell": {"name": "Dell", "parent": null, "category_primary": "hardware-industry", "tags": []}, "lenovo": {"name": "Lenovo", "parent": null, "category_primary": "hardware-industry", "tags": []}, "hp": {"name": "HP", "parent": null, "category_primary": "hardware-industry", "tags": []}, "hpe": {"name": "HPE", "parent": null, "category_primary": "technology", "tags": []}, "siemens": {"name": "Siemens", "parent": null, "category_primary": "manufacturing", "tags": ["automation"]}, "sap": {"name": "SAP", "parent": null, "category_primary": "technology", "tags": []}, "accenture": {"name": "Accenture", "parent": null, "category_primary": "business", "tags": []}, "capgemini": {"name": "Capgemini", "parent": null, "category_primary": "business", "tags": []}, "infosys": {"name": "Infosys", "parent": null, "category_primary": "business", "tags": []}, "tcs": {"name": "TCS", "parent": null, "category_primary": "business", "tags": []}, "wipro": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "business", "tags": []}, "cognizant": {"name": "Cognizant", "parent": null, "category_primary": "business", "tags": []}, "sony": {"name": "Sony", "parent": null, "category_primary": "entertainment", "tags": []}, "lg": {"name": "LG", "parent": null, "category_primary": "technology", "tags": []}, "panasonic": {"name": "Panasonic", "parent": null, "category_primary": "technology", "tags": []}, "philipsp": {"name": "Philips", "parent": null, "category_primary": "health", "tags": []}, "unilever": {"name": "Unilever", "parent": null, "category_primary": "retail", "tags": []}, "p-g": {"name": "P&G", "parent": null, "category_primary": "retail", "tags": []}, "nestle": {"name": "Nestlé", "parent": null, "category_primary": "food-drink", "tags": []}, "danone": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "food-drink", "tags": []}, "general-mills": {"name": "General Mills", "parent": null, "category_primary": "food-drink", "tags": []}, "kelloggs": {"name": "<PERSON><PERSON><PERSON>'s", "parent": null, "category_primary": "food-drink", "tags": []}, "mondelez": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "food-drink", "tags": []}, "kraft-heinz": {"name": "Kraft Heinz", "parent": null, "category_primary": "food-drink", "tags": []}, "mcdonalds": {"name": "McDonald's", "parent": null, "category_primary": "food-drink", "tags": []}, "burger-king": {"name": "Burger King", "parent": "restaurant-brands", "category_primary": "food-drink", "tags": []}, "restaurant-brands": {"name": "Restaurant Brands International", "parent": null, "category_primary": "food-drink", "tags": []}, "kfc": {"name": "KFC", "parent": "yum-brands", "category_primary": "food-drink", "tags": []}, "pizza-hut": {"name": "Pizza Hut", "parent": "yum-brands", "category_primary": "food-drink", "tags": []}, "yum-brands": {"name": "Yum! Brands", "parent": null, "category_primary": "food-drink", "tags": []}, "starbucks": {"name": "Starbucks", "parent": null, "category_primary": "food-drink", "tags": []}, "dominos": {"name": "<PERSON><PERSON>'s", "parent": null, "category_primary": "food-drink", "tags": []}, "chipotle": {"name": "Chipotle", "parent": null, "category_primary": "food-drink", "tags": []}, "subway": {"name": "Subway", "parent": null, "category_primary": "food-drink", "tags": []}, "costco": {"name": "Costco", "parent": null, "category_primary": "retail", "tags": []}, "walmart": {"name": "Walmart", "parent": null, "category_primary": "retail", "tags": []}, "target": {"name": "Target", "parent": null, "category_primary": "retail", "tags": []}, "best-buy": {"name": "Best Buy", "parent": null, "category_primary": "retail", "tags": []}, "home-depot": {"name": "Home Depot", "parent": null, "category_primary": "retail", "tags": []}, "lowes": {"name": "Lowe's", "parent": null, "category_primary": "retail", "tags": []}, "ikea": {"name": "IKEA", "parent": null, "category_primary": "retail", "tags": []}, "zara": {"name": "<PERSON><PERSON>", "parent": "inditex", "category_primary": "fashion", "tags": []}, "inditex": {"name": "Inditex", "parent": null, "category_primary": "fashion", "tags": []}, "hm": {"name": "H&M", "parent": null, "category_primary": "fashion", "tags": []}, "uniqlo": {"name": "Uniqlo", "parent": "fast-retailing", "category_primary": "fashion", "tags": []}, "fast-retailing": {"name": "Fast Retailing", "parent": null, "category_primary": "fashion", "tags": []}, "loreal": {"name": "L'Oréal", "parent": null, "category_primary": "beauty", "tags": []}, "estee-lauder": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "beauty", "tags": []}, "sephora": {"name": "Sephora", "parent": "lvmh", "category_primary": "beauty", "tags": []}, "lvmh": {"name": "LVMH", "parent": null, "category_primary": "fashion", "tags": []}, "gucci": {"name": "<PERSON><PERSON>", "parent": "kering", "category_primary": "fashion", "tags": []}, "kering": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "fashion", "tags": []}, "prada": {"name": "Prada", "parent": null, "category_primary": "fashion", "tags": []}, "hermes": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "fashion", "tags": []}, "chanel": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "fashion", "tags": []}, "rolex": {"name": "Rolex", "parent": null, "category_primary": "fashion", "tags": []}, "cartier": {"name": "<PERSON><PERSON>", "parent": "richemont", "category_primary": "fashion", "tags": []}, "richemont": {"name": "Richemont", "parent": null, "category_primary": "fashion", "tags": []}, "louis-vuitton": {"name": "<PERSON>", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "moet-hennessy": {"name": "<PERSON><PERSON><PERSON>", "parent": "lvmh", "category_primary": "food-drink", "tags": []}, "dior": {"name": "<PERSON><PERSON>", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "fendi": {"name": "<PERSON><PERSON>", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "givenchy": {"name": "Givenchy", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "bulgari": {"name": "Bulgari", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "tiffany": {"name": "Tiffany & Co.", "parent": "lvmh", "category_primary": "fashion", "tags": []}, "hennessy": {"name": "<PERSON><PERSON><PERSON>", "parent": "moe<PERSON>-he<PERSON><PERSON>", "category_primary": "food-drink", "tags": []}, "dom-perignon": {"name": "<PERSON>", "parent": "moe<PERSON>-he<PERSON><PERSON>", "category_primary": "food-drink", "tags": []}, "sega": {"name": "SEGA", "parent": null, "category_primary": "entertainment", "tags": []}, "nintendo": {"name": "Nintendo", "parent": null, "category_primary": "entertainment", "tags": []}, "ea": {"name": "Electronic Arts", "parent": null, "category_primary": "entertainment", "tags": []}, "activision": {"name": "Activision", "parent": "activision-blizzard", "category_primary": "entertainment", "tags": []}, "blizzard": {"name": "Blizzard", "parent": "activision-blizzard", "category_primary": "entertainment", "tags": []}, "king": {"name": "King", "parent": "activision-blizzard", "category_primary": "entertainment", "tags": ["mobile"]}, "activision-blizzard": {"name": "Activision Blizzard", "parent": null, "category_primary": "entertainment", "tags": []}, "ubisoft": {"name": "Ubisoft", "parent": null, "category_primary": "entertainment", "tags": []}, "take-two": {"name": "Take-Two", "parent": null, "category_primary": "entertainment", "tags": []}, "rockstar": {"name": "Rockstar Games", "parent": "take-two", "category_primary": "entertainment", "tags": []}, "2k": {"name": "2K", "parent": "take-two", "category_primary": "entertainment", "tags": []}, "bandai-namco": {"name": "Bandai Namco", "parent": null, "category_primary": "entertainment", "tags": []}, "square-enix": {"name": "Square Enix", "parent": null, "category_primary": "entertainment", "tags": []}, "cd-projekt": {"name": "CD PROJEKT", "parent": null, "category_primary": "entertainment", "tags": []}, "epic-games": {"name": "Epic Games", "parent": null, "category_primary": "entertainment", "tags": []}, "unity": {"name": "Unity", "parent": null, "category_primary": "technology", "tags": []}, "valve": {"name": "Valve", "parent": null, "category_primary": "entertainment", "tags": []}, "steam": {"name": "Steam", "parent": "valve", "category_primary": "entertainment", "tags": []}, "riot-games": {"name": "Riot Games", "parent": "tencent", "category_primary": "entertainment", "tags": []}, "supercell": {"name": "Supercell", "parent": "tencent", "category_primary": "entertainment", "tags": ["mobile"]}, "snap": {"name": "Snap", "parent": null, "category_primary": "social", "tags": ["messaging"]}, "pinterest": {"name": "Pinterest", "parent": null, "category_primary": "social", "tags": []}, "reddit": {"name": "Reddit", "parent": null, "category_primary": "social", "tags": ["forums"]}, "x": {"name": "X", "parent": null, "category_primary": "social", "tags": []}, "linkedin": {"name": "LinkedIn", "parent": "microsoft", "category_primary": "social", "tags": []}, "slack": {"name": "<PERSON><PERSON>ck", "parent": "salesforce", "category_primary": "communications", "tags": ["messaging", "productivity"]}, "atlassian": {"name": "Atlassian", "parent": null, "category_primary": "technology", "tags": ["devtools"]}, "github": {"name": "GitHub", "parent": "microsoft", "category_primary": "technology", "tags": ["devtools"]}, "gitlab": {"name": "GitLab", "parent": null, "category_primary": "technology", "tags": ["devtools", "ci"]}, "bitbucket": {"name": "Bitbucket", "parent": "atlassian", "category_primary": "technology", "tags": ["devtools"]}, "digitalocean": {"name": "DigitalOcean", "parent": null, "category_primary": "technology", "tags": []}, "heroku": {"name": "Heroku", "parent": "salesforce", "category_primary": "technology", "tags": []}, "cloudflare": {"name": "Cloudflare", "parent": null, "category_primary": "technology", "tags": ["cdn", "security", "edge"]}, "fastly": {"name": "Fastly", "parent": null, "category_primary": "technology", "tags": ["cdn", "edge"]}, "akamai": {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": null, "category_primary": "technology", "tags": ["cdn", "security"]}, "shopify": {"name": "Shopify", "parent": null, "category_primary": "e-commerce", "tags": ["ecommerce-platforms", "payments"]}, "woocommerce": {"name": "WooCommerce", "parent": "automattic", "category_primary": "e-commerce", "tags": ["ecommerce-platforms"]}, "automattic": {"name": "Automattic", "parent": null, "category_primary": "technology", "tags": ["cms"]}, "squarespace": {"name": "Squarespace", "parent": null, "category_primary": "technology", "tags": []}, "wix": {"name": "Wix", "parent": null, "category_primary": "technology", "tags": []}, "zendesk": {"name": "Zendesk", "parent": null, "category_primary": "technology", "tags": ["support", "helpdesk"]}, "hubspot": {"name": "HubSpot", "parent": null, "category_primary": "technology", "tags": ["crm", "marketing-automation"]}, "mailchimp": {"name": "Mailchimp", "parent": "intuit", "category_primary": "marketing", "tags": ["email-marketing", "automation"]}, "intuit": {"name": "Intuit", "parent": null, "category_primary": "finance", "tags": ["tax"]}, "quickbooks": {"name": "QuickBooks", "parent": "intuit", "category_primary": "finance", "tags": ["invoicing"]}, "xero": {"name": "Xero", "parent": null, "category_primary": "finance", "tags": ["invoicing"]}, "stripe": {"name": "Stripe", "parent": null, "category_primary": "finance", "tags": ["payments", "billing", "checkout"]}, "square": {"name": "Square", "parent": "block", "category_primary": "finance", "tags": ["payments"]}, "block": {"name": "Block", "parent": null, "category_primary": "finance", "tags": ["payments"]}, "cash-app": {"name": "Cash App", "parent": "block", "category_primary": "finance", "tags": ["payments"]}, "revolut": {"name": "Revolut", "parent": null, "category_primary": "finance", "tags": []}, "wise": {"name": "<PERSON>", "parent": null, "category_primary": "finance", "tags": []}, "monzo": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "finance", "tags": []}, "chime": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "finance", "tags": []}, "coinbase": {"name": "Coinbase", "parent": null, "category_primary": "finance", "tags": []}, "binance": {"name": "Binance", "parent": null, "category_primary": "finance", "tags": []}, "kraken": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "finance", "tags": []}, "robinhood": {"name": "Robinhood", "parent": null, "category_primary": "finance", "tags": []}, "charles-schwab": {"name": "<PERSON>", "parent": null, "category_primary": "finance", "tags": []}, "vanguard": {"name": "Vanguard", "parent": null, "category_primary": "finance", "tags": []}, "blackrock": {"name": "BlackRock", "parent": null, "category_primary": "finance", "tags": []}, "fidelity": {"name": "Fidelity", "parent": null, "category_primary": "finance", "tags": []}, "american-express": {"name": "American Express", "parent": null, "category_primary": "finance", "tags": ["payments"]}, "discover": {"name": "Discover", "parent": null, "category_primary": "finance", "tags": ["payments"]}, "citi": {"name": "Citi", "parent": null, "category_primary": "finance", "tags": []}, "jpmorgan": {"name": "JPMorgan", "parent": null, "category_primary": "finance", "tags": []}, "bank-of-america": {"name": "Bank of America", "parent": null, "category_primary": "finance", "tags": []}, "wells-fargo": {"name": "Wells Fargo", "parent": null, "category_primary": "finance", "tags": []}, "hsbc": {"name": "HSBC", "parent": null, "category_primary": "finance", "tags": []}, "barclays": {"name": "Barclays", "parent": null, "category_primary": "finance", "tags": []}, "santander": {"name": "Santander", "parent": null, "category_primary": "finance", "tags": []}, "bnp-paribas": {"name": "BNP Paribas", "parent": null, "category_primary": "finance", "tags": []}, "societe-generale": {"name": "Société Générale", "parent": null, "category_primary": "finance", "tags": []}, "credit-suisse": {"name": "Credit Suisse", "parent": null, "category_primary": "finance", "tags": []}, "ubs": {"name": "UBS", "parent": null, "category_primary": "finance", "tags": []}, "goldman-sachs": {"name": "Goldman Sachs", "parent": null, "category_primary": "finance", "tags": []}, "morgan-stanley": {"name": "Morgan Stanley", "parent": null, "category_primary": "finance", "tags": []}, "boeing": {"name": "Boeing", "parent": null, "category_primary": "aviation", "tags": []}, "airbus": {"name": "Airbus", "parent": null, "category_primary": "aviation", "tags": []}, "rolls-royce": {"name": "Rolls-Royce", "parent": null, "category_primary": "manufacturing", "tags": []}, "lockheed-martin": {"name": "Lockheed Martin", "parent": null, "category_primary": "manufacturing", "tags": []}, "raytheon": {"name": "Raytheon", "parent": "rtx", "category_primary": "manufacturing", "tags": []}, "rtx": {"name": "RTX", "parent": null, "category_primary": "manufacturing", "tags": []}, "northrop-grumman": {"name": "Northrop Grumman", "parent": null, "category_primary": "manufacturing", "tags": []}, "general-dynamics": {"name": "General Dynamics", "parent": null, "category_primary": "manufacturing", "tags": []}, "spacex": {"name": "SpaceX", "parent": null, "category_primary": "space", "tags": []}, "blue-origin": {"name": "Blue Origin", "parent": null, "category_primary": "space", "tags": []}, "north-face": {"name": "The North Face", "parent": "vf-corp", "category_primary": "outdoors", "tags": []}, "patagonia": {"name": "Patagonia", "parent": null, "category_primary": "outdoors", "tags": []}, "columbia": {"name": "Columbia", "parent": null, "category_primary": "outdoors", "tags": []}, "arcteryx": {"name": "Arc'teryx", "parent": null, "category_primary": "outdoors", "tags": []}, "vf-corp": {"name": "VF Corporation", "parent": null, "category_primary": "fashion", "tags": []}, "the-ordinary": {"name": "The Ordinary", "parent": "deciem", "category_primary": "beauty", "tags": []}, "deciem": {"name": "DECIEM", "parent": null, "category_primary": "beauty", "tags": []}, "glossier": {"name": "G<PERSON><PERSON>", "parent": null, "category_primary": "beauty", "tags": []}, "rivian": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "automotive", "tags": []}, "lucid": {"name": "Lucid", "parent": null, "category_primary": "automotive", "tags": []}, "ferrari": {"name": "Ferrari", "parent": null, "category_primary": "automotive", "tags": []}, "lamborghini": {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "porsche": {"name": "Porsche", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "audi": {"name": "Audi", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "volkswagen": {"name": "Volkswagen", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "skoda": {"name": "Škoda", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "seat": {"name": "SEAT", "parent": "vw-group", "category_primary": "automotive", "tags": []}, "vw-group": {"name": "Volkswagen Group", "parent": null, "category_primary": "automotive", "tags": []}, "bmw": {"name": "BMW", "parent": null, "category_primary": "automotive", "tags": []}, "mercedes-benz": {"name": "Mercedes-Benz", "parent": null, "category_primary": "automotive", "tags": []}, "hyundai": {"name": "Hyundai", "parent": "hyundai-motor", "category_primary": "automotive", "tags": []}, "kia": {"name": "<PERSON><PERSON>", "parent": "hyundai-motor", "category_primary": "automotive", "tags": []}, "hyundai-motor": {"name": "Hyundai Motor Group", "parent": null, "category_primary": "automotive", "tags": []}, "toyota": {"name": "Toyota", "parent": null, "category_primary": "automotive", "tags": []}, "lexus": {"name": "<PERSON>us", "parent": "toyota", "category_primary": "automotive", "tags": []}, "honda": {"name": "Honda", "parent": null, "category_primary": "automotive", "tags": []}, "acura": {"name": "Acura", "parent": "honda", "category_primary": "automotive", "tags": []}, "nissan": {"name": "Nissan", "parent": null, "category_primary": "automotive", "tags": []}, "infiniti": {"name": "Infiniti", "parent": "nissan", "category_primary": "automotive", "tags": []}, "renault": {"name": "Renault", "parent": null, "category_primary": "automotive", "tags": []}, "peugeot": {"name": "Peugeot", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "fiat": {"name": "Fiat", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "jeep": {"name": "Jeep", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "ram": {"name": "Ram", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "alfa-romeo": {"name": "Alfa Romeo", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "maserati": {"name": "Maserati", "parent": "stel<PERSON><PERSON>", "category_primary": "automotive", "tags": []}, "stellantis": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "automotive", "tags": []}, "ford": {"name": "Ford", "parent": null, "category_primary": "automotive", "tags": []}, "general-motors": {"name": "General Motors", "parent": null, "category_primary": "automotive", "tags": []}, "chevrolet": {"name": "Chevrolet", "parent": "general-motors", "category_primary": "automotive", "tags": []}, "gmc": {"name": "GMC", "parent": "general-motors", "category_primary": "automotive", "tags": []}, "cadillac": {"name": "Cadillac", "parent": "general-motors", "category_primary": "automotive", "tags": []}, "buick": {"name": "Buick", "parent": "general-motors", "category_primary": "automotive", "tags": []}, "tesco": {"name": "Tesco", "parent": null, "category_primary": "retail", "tags": []}, "sainsburys": {"name": "Sainsbury's", "parent": null, "category_primary": "retail", "tags": []}, "aldi": {"name": "ALDI", "parent": null, "category_primary": "retail", "tags": []}, "lidl": {"name": "Lidl", "parent": null, "category_primary": "retail", "tags": []}, "carrefour": {"name": "Carrefour", "parent": null, "category_primary": "retail", "tags": []}, "auchan": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "retail", "tags": []}, "alibaba-cloud": {"name": "Alibaba Cloud", "parent": "alibaba", "category_primary": "technology", "tags": []}, "baidu": {"name": "Baidu", "parent": null, "category_primary": "technology", "tags": ["search"]}, "jd": {"name": "JD.com", "parent": null, "category_primary": "e-commerce", "tags": []}, "meituan": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "e-commerce", "tags": []}, "pdd": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": null, "category_primary": "e-commerce", "tags": ["social-commerce"]}, "wechat": {"name": "WeChat", "parent": "tencent", "category_primary": "social", "tags": ["messaging", "payments"]}, "weibo": {"name": "Weibo", "parent": null, "category_primary": "social", "tags": []}, "xiaomi": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "technology", "tags": []}, "oppo": {"name": "OPPO", "parent": "bbk", "category_primary": "technology", "tags": []}, "vivo": {"name": "vivo", "parent": "bbk", "category_primary": "technology", "tags": []}, "oneplus": {"name": "OnePlus", "parent": "bbk", "category_primary": "technology", "tags": []}, "realme": {"name": "realme", "parent": "bbk", "category_primary": "technology", "tags": []}, "bbk": {"name": "BBK Electronics", "parent": null, "category_primary": "technology", "tags": []}, "huawei": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "technology", "tags": []}, "ericsson": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "telecom", "tags": []}, "nokia": {"name": "Nokia", "parent": null, "category_primary": "telecom", "tags": []}, "t-mobile": {"name": "T-Mobile", "parent": null, "category_primary": "telecom", "tags": []}, "verizon": {"name": "Verizon", "parent": null, "category_primary": "telecom", "tags": []}, "att": {"name": "AT&T", "parent": null, "category_primary": "telecom", "tags": []}, "vodafone": {"name": "Vodafone", "parent": null, "category_primary": "telecom", "tags": []}, "orange": {"name": "Orange", "parent": null, "category_primary": "telecom", "tags": []}, "telefonica": {"name": "Telefónica", "parent": null, "category_primary": "telecom", "tags": []}, "bt": {"name": "BT", "parent": null, "category_primary": "telecom", "tags": []}, "sky": {"name": "Sky", "parent": null, "category_primary": "news-media", "tags": []}, "bbc": {"name": "BBC", "parent": null, "category_primary": "news-media", "tags": []}, "cnn": {"name": "CNN", "parent": null, "category_primary": "news-media", "tags": []}, "nytimes": {"name": "The New York Times", "parent": null, "category_primary": "news-media", "tags": []}, "washington-post": {"name": "The Washington Post", "parent": null, "category_primary": "news-media", "tags": []}, "bloomberg": {"name": "Bloomberg", "parent": null, "category_primary": "news-media", "tags": []}, "reuters": {"name": "Reuters", "parent": null, "category_primary": "news-media", "tags": []}, "ft": {"name": "Financial Times", "parent": null, "category_primary": "news-media", "tags": []}, "wsj": {"name": "The Wall Street Journal", "parent": null, "category_primary": "news-media", "tags": []}, "guardian": {"name": "The Guardian", "parent": null, "category_primary": "news-media", "tags": []}, "fox": {"name": "Fox News", "parent": null, "category_primary": "news-media", "tags": []}, "disney": {"name": "Disney", "parent": null, "category_primary": "entertainment", "tags": []}, "hulu": {"name": "<PERSON><PERSON>", "parent": "disney", "category_primary": "entertainment", "tags": []}, "espn": {"name": "ESPN", "parent": "disney", "category_primary": "entertainment", "tags": []}, "pixar": {"name": "Pixar", "parent": "disney", "category_primary": "entertainment", "tags": []}, "marvel": {"name": "Marvel", "parent": "disney", "category_primary": "entertainment", "tags": []}, "lucasfilm": {"name": "Lucasfilm", "parent": "disney", "category_primary": "entertainment", "tags": []}, "warner-bros": {"name": "Warner Bros.", "parent": "wbd", "category_primary": "entertainment", "tags": []}, "hbo": {"name": "HBO", "parent": "wbd", "category_primary": "entertainment", "tags": []}, "discovery": {"name": "Discovery", "parent": "wbd", "category_primary": "entertainment", "tags": []}, "wbd": {"name": "Warner Bros. Discovery", "parent": null, "category_primary": "entertainment", "tags": []}, "paramount": {"name": "Paramount", "parent": null, "category_primary": "entertainment", "tags": []}, "peacock": {"name": "<PERSON>", "parent": "nbcuniversal", "category_primary": "entertainment", "tags": []}, "nbcuniversal": {"name": "NBCUniversal", "parent": "comcast", "category_primary": "entertainment", "tags": []}, "comcast": {"name": "Comcast", "parent": null, "category_primary": "telecom", "tags": []}, "netflix-games": {"name": "Netflix Games", "parent": "netflix", "category_primary": "entertainment", "tags": ["mobile"]}, "hbo-max": {"name": "Max", "parent": "wbd", "category_primary": "entertainment", "tags": []}, "paramount-plus": {"name": "Paramount+", "parent": "paramount", "category_primary": "entertainment", "tags": []}, "sony-pictures": {"name": "Sony Pictures", "parent": "sony", "category_primary": "entertainment", "tags": []}, "playstation": {"name": "PlayStation", "parent": "sony", "category_primary": "entertainment", "tags": []}, "xbox": {"name": "Xbox", "parent": "microsoft", "category_primary": "entertainment", "tags": []}, "bethesda": {"name": "Bethesda", "parent": "microsoft", "category_primary": "entertainment", "tags": []}, "mojang": {"name": "Mojan<PERSON>", "parent": "microsoft", "category_primary": "entertainment", "tags": []}, "linkedin-learning": {"name": "LinkedIn Learning", "parent": "microsoft", "category_primary": "education", "tags": []}, "coursera": {"name": "Coursera", "parent": null, "category_primary": "education", "tags": []}, "udemy": {"name": "Udemy", "parent": null, "category_primary": "education", "tags": []}, "edx": {"name": "edX", "parent": null, "category_primary": "education", "tags": []}, "duolingo": {"name": "Duolingo", "parent": null, "category_primary": "education", "tags": []}, "khan-academy": {"name": "Khan Academy", "parent": null, "category_primary": "education", "tags": []}, "udacity": {"name": "Udacity", "parent": null, "category_primary": "education", "tags": []}, "pluralsight": {"name": "Pluralsight", "parent": null, "category_primary": "education", "tags": []}, "datadog": {"name": "Datadog", "parent": null, "category_primary": "technology", "tags": ["observability", "monitoring", "metrics"]}, "new-relic": {"name": "New Relic", "parent": null, "category_primary": "technology", "tags": ["observability", "monitoring"]}, "splunk": {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": null, "category_primary": "technology", "tags": ["logging", "observability"]}, "elastic": {"name": "Elastic", "parent": null, "category_primary": "technology", "tags": ["search", "elasticsearch", "observability"]}, "hashicorp": {"name": "HashiCorp", "parent": null, "category_primary": "technology", "tags": ["terraform"]}, "terraform": {"name": "Terraform", "parent": "hashicorp", "category_primary": "technology", "tags": []}, "okta": {"name": "Okta", "parent": null, "category_primary": "security-privacy", "tags": ["identity", "mfa"]}, "auth0": {"name": "Auth0", "parent": "okta", "category_primary": "security-privacy", "tags": ["identity", "oidc", "passwordless"]}, "cloudflare-zero-trust": {"name": "Cloudflare Zero Trust", "parent": "cloudflare", "category_primary": "security-privacy", "tags": ["zero-trust"]}, "zscaler": {"name": "<PERSON><PERSON><PERSON>", "parent": null, "category_primary": "security-privacy", "tags": ["zero-trust", "security"]}, "palo-alto-networks": {"name": "Palo Alto Networks", "parent": null, "category_primary": "security-privacy", "tags": ["security"]}, "crowdstrike": {"name": "CrowdStrike", "parent": null, "category_primary": "security-privacy", "tags": ["security"]}, "sentinelone": {"name": "SentinelOne", "parent": null, "category_primary": "security-privacy", "tags": ["security"]}, "snowflake": {"name": "Snowflake", "parent": null, "category_primary": "technology", "tags": ["data-warehouses", "sql"]}, "databricks": {"name": "Databricks", "parent": null, "category_primary": "technology", "tags": ["lakehouse", "mlops"]}, "confluent": {"name": "Confluent", "parent": null, "category_primary": "technology", "tags": ["streaming-tech"]}, "stripe-atlas": {"name": "Stripe Atlas", "parent": "stripe", "category_primary": "business", "tags": []}, "openai": {"name": "OpenAI", "parent": null, "category_primary": "technology", "tags": ["ai-foundation-models", "api"]}, "anthropic": {"name": "Anthropic", "parent": null, "category_primary": "technology", "tags": ["ai-foundation-models"]}, "cohere": {"name": "Cohere", "parent": null, "category_primary": "technology", "tags": ["nlp", "api"]}, "mistral": {"name": "Mistral AI", "parent": null, "category_primary": "technology", "tags": ["api"]}, "perplexity": {"name": "Perplexity", "parent": null, "category_primary": "technology", "tags": ["search", "agents"]}, "hugging-face": {"name": "Hugging Face", "parent": null, "category_primary": "technology", "tags": ["community"]}, "deepl": {"name": "DeepL", "parent": null, "category_primary": "technology", "tags": ["translation", "api"]}, "figma": {"name": "Figma", "parent": null, "category_primary": "design", "tags": ["design-systems", "collaboration", "prototyping"]}, "adobe-creative-cloud": {"name": "Adobe Creative Cloud", "parent": "adobe", "category_primary": "technology", "tags": []}, "canva": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "design", "tags": ["collaboration"]}, "notion": {"name": "Notion", "parent": null, "category_primary": "productivity", "tags": ["notes", "databases", "collaboration"]}, "asana": {"name": "<PERSON><PERSON>", "parent": null, "category_primary": "productivity", "tags": ["tasks"]}, "trello": {"name": "Trello", "parent": "atlassian", "category_primary": "productivity", "tags": []}, "monday": {"name": "monday.com", "parent": null, "category_primary": "productivity", "tags": ["automation"]}, "dropbox": {"name": "Dropbox", "parent": null, "category_primary": "technology", "tags": ["collaboration"]}, "box": {"name": "Box", "parent": null, "category_primary": "technology", "tags": []}, "zoom": {"name": "Zoom", "parent": null, "category_primary": "communications", "tags": []}, "teams": {"name": "Microsoft Teams", "parent": "microsoft", "category_primary": "communications", "tags": ["collaboration", "chat"]}, "google-meet": {"name": "Google Meet", "parent": "google", "category_primary": "communications", "tags": ["chat"]}}