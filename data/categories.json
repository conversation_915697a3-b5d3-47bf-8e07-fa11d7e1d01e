{"technology": {"name": "Technology", "categories": {"developer-tools": "Developer Tools", "software": "Software", "cloud": "Cloud Computing", "ai": "Artificial Intelligence", "cybersecurity": "Cybersecurity", "devops": "DevOps", "data-science": "Data Science", "blockchain": "Blockchain", "open-source": "Open Source", "databases": "Databases", "mobile-development": "Mobile Development", "web-development": "Web Development", "testing": "Testing", "iot": "Internet of Things", "edge-computing": "Edge Computing", "vr-ar": "Virtual/AR", "robotics": "Robotics", "hardware": "Hardware", "embedded-systems": "Embedded Systems", "nlp": "Natural Language Processing", "computer-vision": "Computer Vision", "mlops": "MLOps", "observability": "Observability", "developer-education": "Developer Education", "containers": "Containers", "serverless": "Serverless", "api-management": "API Management", "integration": "Integration", "low-code": "Low-Code/No-Code"}}, "e-commerce": {"name": "E-commerce", "categories": {"marketplace": "Marketplace", "fashion": "Fashion", "electronics": "Electronics", "home-garden": "Home & Garden", "grocery": "Grocery", "beauty": "Beauty", "sports": "Sports", "auto-parts": "Auto Parts", "luxury": "Luxury", "jewelry": "Jewelry", "toys": "Toys", "books": "Books", "pets": "Pets", "office-supplies": "Office Supplies", "baby": "Baby", "furniture": "Furniture", "diy": "DIY", "crafts": "Crafts", "music-gear": "Music Gear", "digital-goods": "Digital Goods", "subscriptions": "Subscriptions", "second-hand": "Second Hand", "wholesale": "Wholesale", "b2b": "B2B Commerce", "print-on-demand": "Print on Demand", "dropshipping": "Dropshipping"}}, "news-media": {"name": "News & Media", "categories": {"newspapers": "Newspapers", "magazines": "Magazines", "broadcast": "Broadcast", "online-news": "Online News", "blogs": "Blogs", "tech-news": "Tech News", "finance-news": "Finance News", "sports-news": "Sports News", "entertainment-news": "Entertainment News", "science-news": "Science News", "local-news": "Local News", "international": "International", "opinion": "Opinion", "investigative": "Investigative", "photojournalism": "Photojournalism", "data-journalism": "Data Journalism"}}, "education": {"name": "Education", "categories": {"universities": "Universities", "k12": "K-12", "online-learning": "Online Learning", "research": "Research", "libraries": "Libraries", "moocs": "MOOCs", "language-learning": "Language Learning", "coding-bootcamps": "Coding Bootcamps", "edtech": "EdTech", "test-prep": "Test Prep", "scholarships": "Scholarships", "academic-publishing": "Academic Publishing", "open-education": "Open Education"}}, "finance": {"name": "Finance", "categories": {"banking": "Banking", "investing": "Investing", "fintech": "Fintech", "crypto": "Crypto", "insurance": "Insurance", "accounting": "Accounting", "payments": "Payments", "lending": "Lending", "wealth-management": "Wealth Management", "personal-finance": "Personal Finance", "trading": "Trading", "neobanks": "Neobanks", "mortgages": "Mortgages", "tax": "Tax", "regtech": "RegTech"}}, "health": {"name": "Health", "categories": {"hospitals": "Hospitals", "clinics": "Clinics", "pharmacy": "Pharmacy", "fitness": "Fitness", "nutrition": "Nutrition", "mental-health": "Mental Health", "telemedicine": "Telemedicine", "wearables": "Wearables", "medical-devices": "Medical Devices", "biotech": "Biotech", "public-health": "Public Health", "wellness": "Wellness", "dental": "Dental", "vision": "Vision", "elder-care": "Elder Care", "rehab": "Rehabilitation"}}, "travel": {"name": "Travel", "categories": {"airlines": "Airlines", "hotels": "Hotels", "booking": "Booking", "destinations": "Destinations", "transport": "Transport", "car-rental": "Car Rental", "rail": "Rail", "cruises": "Cruises", "vacation-rentals": "Vacation Rentals", "travel-blogs": "Travel Blogs", "adventure": "Adventure Travel", "backpacking": "Backpacking", "loyalty": "Loyalty Programs", "tour-operators": "Tour Operators"}}, "entertainment": {"name": "Entertainment", "categories": {"streaming": "Streaming", "gaming": "Gaming", "music": "Music", "movies": "Movies", "events": "Events", "theatre": "Theatre", "podcasts": "Podcasts", "anime": "Anime", "comics": "Comics", "tickets": "Tickets", "fan-communities": "Fan Communities", "board-games": "Board Games", "fantasy-sports": "Fantasy Sports"}}, "social": {"name": "Social", "categories": {"social-networks": "Social Networks", "forums": "Forums", "messaging": "Messaging", "dating": "Dating", "creator-platforms": "Creator Platforms", "professional-networks": "Professional Networks", "knowledge-sharing": "Knowledge Sharing", "reviews": "Reviews", "communities": "Communities"}}, "government": {"name": "Government", "categories": {"federal": "Federal", "state": "State", "local": "Local", "agencies": "Agencies", "public-services": "Public Services", "judiciary": "Judiciary", "legislature": "Legislature", "executive": "Executive", "eprocurement": "E-Procurement", "open-data": "Open Data", "elections": "Elections", "immigration": "Immigration"}}, "nonprofit": {"name": "Nonprofit", "categories": {"charity": "Charity", "advocacy": "Advocacy", "foundations": "Foundations", "communities": "Communities", "environment": "Environment", "human-rights": "Human Rights", "animal-welfare": "Animal Welfare", "religion": "Religion", "arts-culture": "Arts & Culture", "education-support": "Education Support", "health-support": "Health Support"}}, "sports": {"name": "Sports", "categories": {"teams": "Teams", "leagues": "Leagues", "esports": "Esports", "outdoors": "Outdoors", "fitness-clubs": "Fitness Clubs", "running": "Running", "cycling": "Cycling", "soccer": "Soccer", "basketball": "Basketball", "tennis": "Tennis", "golf": "Golf", "motorsports": "Motorsports", "combat-sports": "Combat Sports", "winter-sports": "Winter Sports"}}, "real-estate": {"name": "Real Estate", "categories": {"residential": "Residential", "commercial": "Commercial", "rentals": "Rentals", "mortgage": "Mortgage", "brokerages": "Brokerages", "property-management": "Property Management", "architecture": "Architecture", "interior-design": "Interior Design", "construction": "Construction", "home-improvement": "Home Improvement"}}, "automotive": {"name": "Automotive", "categories": {"manufacturers": "Manufacturers", "dealers": "Dealers", "reviews": "Reviews", "aftermarket": "Aftermarket", "electric-vehicles": "Electric Vehicles", "charging": "Charging", "ride-sharing": "Ride Sharing", "logistics": "Logistics", "fleet": "Fleet", "motorsport": "Motorsport"}}, "food-drink": {"name": "Food & Drink", "categories": {"restaurants": "Restaurants", "recipes": "Recipes", "delivery": "Delivery", "grocers": "Grocers", "beverages": "Beverages", "coffee": "Coffee", "tea": "Tea", "alcohol": "Alcohol", "nutrition-products": "Nutrition Products", "kitchenware": "Kitchenware", "food-tech": "Food Tech"}}, "lifestyle": {"name": "Lifestyle", "categories": {"fashion-style": "Fashion & Style", "beauty-care": "Beauty & Care", "home-living": "Home & Living", "parenting": "Parenting", "weddings": "Weddings", "hobbies": "Hobbies", "astrology": "Astrology", "self-improvement": "Self Improvement"}}, "science": {"name": "Science", "categories": {"physics": "Physics", "chemistry": "Chemistry", "biology": "Biology", "astronomy": "Astronomy", "earth-science": "Earth Science", "mathematics": "Mathematics", "neuroscience": "Neuroscience", "computational": "Computational Science", "research-institutes": "Research Institutes"}}, "environment": {"name": "Environment", "categories": {"climate": "Climate", "energy": "Energy", "renewables": "Renewables", "sustainability": "Sustainability", "conservation": "Conservation", "recycling": "Recycling", "water": "Water", "air-quality": "Air Quality"}}, "business": {"name": "Business", "categories": {"b2b-services": "B2B Services", "hr": "HR", "legal-services": "Legal Services", "consulting": "Consulting", "procurement": "Procurement", "office-management": "Office Management", "corporate": "Corporate", "smb": "SMB"}}, "marketing": {"name": "Marketing", "categories": {"digital-marketing": "Digital Marketing", "advertising": "Advertising", "seo-services": "SEO Services", "content-marketing": "Content Marketing", "email-marketing": "Email Marketing", "social-media": "Social Media Marketing", "affiliate": "Affiliate Marketing", "market-research": "Market Research"}}, "design": {"name": "Design", "categories": {"graphic-design": "Graphic Design", "ui-ux": "UI/UX", "product-design": "Product Design", "industrial-design": "Industrial Design", "illustration": "Illustration", "typography": "Typography", "branding": "Branding", "motion-design": "Motion Design"}}, "art-culture": {"name": "Art & Culture", "categories": {"museums": "Museums", "galleries": "Galleries", "theaters": "Theaters", "festivals": "Festivals", "literature": "Literature", "performing-arts": "Performing Arts", "heritage": "Heritage", "photography": "Photography"}}, "career": {"name": "Career", "categories": {"job-boards": "Job Boards", "recruiting": "Recruiting", "resume": "Resume/CV", "freelancing": "Freelancing", "remote-work": "Remote Work", "career-advice": "Career Advice", "salary": "Salary & Compensation"}}, "productivity": {"name": "Productivity", "categories": {"task-management": "Task Management", "notes": "Notes", "calendars": "Calendars", "time-tracking": "Time Tracking", "automation": "Automation", "knowledge-management": "Knowledge Management", "collaboration": "Collaboration"}}, "communications": {"name": "Communications", "categories": {"email": "Email", "video-conferencing": "Video Conferencing", "voip": "VoIP", "sms": "SMS", "contact-centers": "Contact Centers", "customer-support": "Customer Support"}}, "security-privacy": {"name": "Security & Privacy", "categories": {"identity-access": "Identity & Access", "endpoint-security": "Endpoint Security", "network-security": "Network Security", "appsec": "Application Security", "privacy-tools": "Privacy <PERSON>ls", "threat-intel": "Threat Intelligence", "governance": "Governance, Risk & Compliance"}}, "agriculture": {"name": "Agriculture", "categories": {"farming": "Farming", "agritech": "AgriTech", "seeds": "Seeds", "fertilizers": "Fertilizers", "equipment": "Equipment", "livestock": "Livestock", "aquaculture": "Aquaculture", "forestry": "Forestry"}}, "energy-utilities": {"name": "Energy & Utilities", "categories": {"oil-gas": "Oil & Gas", "power": "Power", "grid": "Grid", "nuclear": "Nuclear", "renewable-energy": "Renewable Energy", "energy-storage": "Energy Storage", "utilities": "Utilities"}}, "telecom": {"name": "Telecom", "categories": {"carriers": "Carriers", "isp": "ISPs", "5g": "5G", "satellite": "Satellite", "infrastructure": "Infrastructure", "mvno": "MVNO", "roaming": "Roaming"}}, "logistics": {"name": "Logistics", "categories": {"shipping": "Shipping", "freight": "Freight", "warehousing": "Warehousing", "last-mile": "Last Mile", "supply-chain": "Supply Chain", "inventory": "Inventory", "tracking": "Tracking"}}, "gaming-industry": {"name": "Gaming Industry", "categories": {"studios": "Studios", "publishers": "Publishers", "engines": "Game Engines", "esports-orgs": "Esports Orgs", "mods": "Mods", "gaming-hardware": "Gaming Hardware"}}, "space": {"name": "Space", "categories": {"launch": "Launch", "satcom": "SatCom", "earth-observation": "Earth Observation", "space-tourism": "Space Tourism", "deep-space": "Deep Space", "astronautics": "Astronautics"}}, "pets": {"name": "Pets", "categories": {"pet-care": "Pet Care", "pet-food": "Pet Food", "adoption": "Adoption", "veterinary": "Veterinary", "pet-products": "Pet Products"}}, "housing": {"name": "Housing", "categories": {"renting": "Renting", "home-buying": "Home Buying", "mortgage-tools": "Mortgage Tools", "hoa": "HOA", "property-tech": "Property Tech"}}, "law-legal": {"name": "Law & Legal", "categories": {"law-firms": "Law Firms", "legal-tech": "Legal Tech", "courts": "Courts", "compliance": "Compliance", "contracts": "Contracts", "intellectual-property": "Intellectual Property"}}, "human-resources": {"name": "Human Resources", "categories": {"ats": "Applicant Tracking", "payroll": "Payroll", "benefits": "Benefits", "learning": "Learning & Development", "performance": "Performance Management", "recruitment": "Recruitment"}}, "housing-community": {"name": "Housing & Community", "categories": {"co-living": "Co-living", "student-housing": "Student Housing", "senior-living": "Senior Living", "housing-assistance": "Housing Assistance"}}, "media-production": {"name": "Media Production", "categories": {"video-production": "Video Production", "vfx": "VFX", "post-production": "Post Production", "music-production": "Music Production", "photography-services": "Photography Services"}}, "events": {"name": "Events", "categories": {"event-management": "Event Management", "ticketing": "Ticketing", "venues": "Venues", "trade-shows": "Trade Shows", "conferences": "Conferences", "meetups": "Meetups"}}, "charity-cause": {"name": "Charity & Cause", "categories": {"fundraising": "Fundraising", "donations": "Donations", "volunteering": "Volunteering", "disaster-relief": "Disaster Relief", "csr": "Corporate Social Responsibility"}}, "childcare": {"name": "Childcare", "categories": {"daycare": "Daycare", "nannies": "Nannies", "parenting-resources": "Parenting Resources", "kids-activities": "Kids Activities"}}, "eldercare": {"name": "Eldercare", "categories": {"assisted-living": "Assisted Living", "home-care": "Home Care", "retirement": "Retirement", "mobility-aids": "Mobility Aids"}}, "accessibility": {"name": "Accessibility", "categories": {"assistive-tech": "Assistive Tech", "standards": "Standards", "screen-readers": "Screen Readers", "inclusive-design": "Inclusive Design"}}, "religion-spiritual": {"name": "Religion & Spiritual", "categories": {"churches": "Churches", "mosques": "Mosques", "synagogues": "Synagogues", "temples": "Temples", "spirituality": "Spirituality"}}, "politics": {"name": "Politics", "categories": {"parties": "Parties", "campaigns": "Campaigns", "policy": "Policy", "think-tanks": "Think Tanks", "watchdogs": "Watchdogs"}}, "geography": {"name": "Geography", "categories": {"cities": "Cities", "countries": "Countries", "regions": "Regions", "tourism-boards": "Tourism Boards", "mapping": "Mapping"}}, "history": {"name": "History", "categories": {"museums-history": "History Museums", "archives": "Archives", "genealogy": "Genealogy", "heritage-sites": "Heritage Sites"}}, "weather": {"name": "Weather", "categories": {"forecasts": "Forecasts", "climate-data": "Climate Data", "storms": "Storms", "meteorology": "Meteorology"}}, "metaverse": {"name": "Metaverse", "categories": {"virtual-worlds": "Virtual Worlds", "avatars": "Avatars", "digital-assets": "Digital Assets", "vr-platforms": "VR Platforms"}}, "cryptography": {"name": "Cryptography", "categories": {"protocols": "Protocols", "wallets": "Wallets", "exchanges": "Exchanges", "defi": "<PERSON><PERSON><PERSON>", "nft": "NFT"}}, "analytics-bi": {"name": "Analytics & BI", "categories": {"bi-platforms": "BI Platforms", "dashboards": "Dashboards", "etl": "ETL", "data-lakes": "Data Lakes", "governance": "Data Governance", "catalogs": "Data Catalogs"}}, "operations": {"name": "Operations", "categories": {"it-operations": "IT Operations", "field-service": "Field Service", "sre": "SRE", "asset-management": "Asset Management", "cmms": "CMMS"}}, "manufacturing": {"name": "Manufacturing", "categories": {"cad-cam": "CAD/CAM", "mes": "MES", "plm": "PLM", "factory-automation": "Factory Automation", "industrial-iot": "Industrial IoT", "quality-control": "Quality Control"}}, "aviation": {"name": "Aviation", "categories": {"airlines-business": "Airlines (Business)", "airports": "Airports", "private-aviation": "Private Aviation", "flight-tracking": "Flight Tracking", "aircraft-manufacturers": "Aircraft Manufacturers"}}, "maritime": {"name": "Maritime", "categories": {"shipping-lines": "Shipping Lines", "ports": "Ports", "chartering": "Chartering", "marine-tech": "Marine Tech", "yachting": "Yachting"}}, "photography": {"name": "Photography", "categories": {"galleries-online": "Online Galleries", "stock-photos": "Stock Photos", "cameras": "Cameras", "photo-editing": "Photo Editing", "printing": "Printing"}}, "home-services": {"name": "Home Services", "categories": {"plumbing": "Plumbing", "electrical": "Electrical", "cleaning": "Cleaning", "landscaping": "Landscaping", "pest-control": "Pest Control", "moving": "Moving", "security-systems": "Security Systems"}}, "professional-services": {"name": "Professional Services", "categories": {"accounting-services": "Accounting Services", "legal-services-pro": "Legal Services", "consulting-pro": "Consulting", "it-services": "IT Services", "translation": "Translation", "design-agencies": "Design Agencies"}}, "payments": {"name": "Payments", "categories": {"gateways": "Gateways", "processors": "Processors", "pos": "POS", "billing": "Billing", "invoicing": "Invoicing", "fraud-detection": "<PERSON>aud Detection"}}, "ad-tech": {"name": "Ad Tech", "categories": {"dsp": "DSP", "ssp": "SSP", "ad-servers": "Ad Servers", "measurement": "Measurement", "cdp": "CDP", "crm": "CRM"}}, "smb-tools": {"name": "SMB Tools", "categories": {"website-builders": "Website Builders", "crm-smb": "CRM", "pos-smb": "POS", "inventory-smb": "Inventory", "appointments": "Appointments", "invoicing-smb": "Invoicing"}}, "opensource-ecosystem": {"name": "Open Source Ecosystem", "categories": {"foundations-oss": "Foundations", "package-registries": "Package Registries", "code-hosting": "Code Hosting", "ci-cd": "CI/CD", "issue-trackers": "Issue Trackers", "package-managers": "Package Managers"}}, "dev-education": {"name": "Developer Education", "categories": {"tutorials": "Tutorials", "docs": "Documentation", "courses": "Courses", "sandboxes": "Sandboxes", "certifications": "Certifications"}}, "ai-industry": {"name": "AI Industry", "categories": {"foundation-models": "Foundation Models", "ai-apis": "AI APIs", "vector-databases": "Vector Databases", "prompting": "Prompting", "agents": "Agents", "guardrails": "Guardrails"}}, "hr-tech": {"name": "HR Tech", "categories": {"background-checks": "Background Checks", "engagement": "Engagement", "org-chart": "Org Chart", "workforce": "Workforce Management", "shift-scheduling": "Shift Scheduling"}}, "compliance": {"name": "Compliance", "categories": {"privacy-compliance": "Privacy Compliance", "security-compliance": "Security Compliance", "financial-compliance": "Financial Compliance", "audit": "Audit", "risk": "Risk"}}, "retail": {"name": "Retail", "categories": {"grocery-retail": "Grocery Retail", "department-stores": "Department Stores", "specialty-retail": "Specialty Retail", "convenience-stores": "Convenience Stores", "mall-brands": "Mall Brands"}}, "fashion": {"name": "Fashion", "categories": {"streetwear": "Streetwear", "luxury-fashion": "Luxury Fashion", "footwear": "Footwear", "accessories": "Accessories", "sustainable-fashion": "Sustainable Fashion"}}, "beauty": {"name": "Beauty", "categories": {"skincare": "Skincare", "makeup": "Makeup", "haircare": "Haircare", "fragrance": "Fragrance", "mens-grooming": "Men's Grooming"}}, "home-living": {"name": "Home & Living", "categories": {"decor": "Decor", "appliances": "Appliances", "smart-home": "Smart Home", "organizers": "Organizers", "lighting": "Lighting", "bath": "Bath"}}, "hobbies": {"name": "Hobbies", "categories": {"model-making": "Model Making", "woodworking": "Woodworking", "knitting": "Knitting", "gardening": "Gardening", "collectibles": "Collectibles", "drones": "Drones"}}, "outdoors": {"name": "Outdoors", "categories": {"camping": "Camping", "hiking": "Hiking", "fishing": "Fishing", "hunting": "Hunting", "climbing": "Climbing", "water-sports": "Water Sports"}}, "pets-animals": {"name": "Pets & Animals", "categories": {"dogs": "Dogs", "cats": "Cats", "birds": "Birds", "reptiles": "Reptiles", "aquariums": "Aquariums", "wildlife": "Wildlife"}}, "transport": {"name": "Transport", "categories": {"public-transit": "Public Transit", "taxis": "Taxis", "micromobility": "Micromobility", "ev-charging": "EV Charging", "maps-navigation": "Maps & Navigation"}}, "maps-gis": {"name": "Maps & GIS", "categories": {"gis-software": "GIS Software", "mapping-platforms": "Mapping Platforms", "geocoding": "Geocoding", "routing": "Routing", "geo-data": "Geo Data"}}, "insurance-industry": {"name": "Insurance Industry", "categories": {"life": "Life", "health-insurance": "Health", "auto-insurance": "Auto", "home-insurance": "Home", "reinsurance": "Reinsurance", "insurtech": "<PERSON><PERSON><PERSON><PERSON>"}}, "public-safety": {"name": "Public Safety", "categories": {"police": "Police", "fire": "Fire", "ems": "EMS", "disaster-management": "Disaster Management", "cyber-response": "Cyber Response"}}, "agencies-creative": {"name": "Agencies (Creative)", "categories": {"branding-agencies": "Branding Agencies", "digital-agencies": "Digital Agencies", "pr": "PR", "content-studios": "Content Studios", "production-houses": "Production Houses"}}, "pr-communications": {"name": "PR & Communications", "categories": {"press-releases": "Press Releases", "media-monitoring": "Media Monitoring", "influencer": "Influencer", "reputation": "Reputation"}}, "education-kids": {"name": "Education (Kids)", "categories": {"learning-games": "Learning Games", "printables": "Printables", "activities": "Activities", "early-learning": "Early Learning"}}, "parenting": {"name": "Parenting", "categories": {"pregnancy": "Pregnancy", "newborn": "Newborn", "toddlers": "Toddlers", "teens": "Teens", "family-activities": "Family Activities"}}, "communications-devices": {"name": "Communication Devices", "categories": {"smartphones": "Smartphones", "wearables-devices": "Wearables", "tablets": "Tablets", "smartwatches": "Smartwatches", "accessories-devices": "Accessories"}}, "app-platforms": {"name": "App Platforms", "categories": {"ios-apps": "iOS Apps", "android-apps": "Android Apps", "web-apps": "Web Apps", "desktop-apps": "Desktop Apps", "cross-platform": "Cross-platform"}}, "documentation": {"name": "Documentation", "categories": {"api-docs": "API Docs", "product-docs": "Product Docs", "developer-docs": "Developer Docs", "knowledge-bases": "Knowledge Bases"}}, "hardware-industry": {"name": "Hardware Industry", "categories": {"semiconductors": "Semiconductors", "chip-design": "Chip Design", "foundries": "Foundries", "pc-building": "PC Building", "peripherals": "Peripher<PERSON>"}}, "print-media": {"name": "Print Media", "categories": {"publishing": "Publishing", "printing-services": "Printing Services", "magazine-publishers": "Magazine Publishers", "newspaper-publishers": "Newspaper Publishers"}}, "translation-localization": {"name": "Translation & Localization", "categories": {"l10n-tools": "L10n Tools", "i18n": "i18n", "language-services": "Language Services", "terminology": "Terminology"}}, "accessories": {"name": "Accessories", "categories": {"bags": "Bags", "wallets": "Wallets", "belts": "Belts", "watches": "Watches", "jewelry-accessories": "Jewelry"}}, "toys-games": {"name": "Toys & Games", "categories": {"educational-toys": "Educational Toys", "puzzles": "Puzzles", "rc": "RC", "board-games-toys": "Board Games", "collectibles-toys": "Collectibles"}}, "arts-crafts": {"name": "Arts & Crafts", "categories": {"painting": "Painting", "drawing": "Drawing", "sculpture": "Sculpture", "paper-crafts": "Paper Crafts", "textiles": "Textiles"}}}